export interface Medicine {
  id: string;
  medicineId: string;
  name: string;
  defaultCost: number;
  description?: string;
  createdAt: string;
  updatedAt: string;
  productName?: string;
  mrp?: number;
  qty?: string;
  productForm?: string;
  medicineType?: string;
}

export interface OrganizationMedicine {
  id: string;
  medicineId: string;
  organizationId: string;
  isActive: boolean;
  price?: number;
  createdAt: string;
  updatedAt: string;
  medicine?: Medicine;
}

export interface MedicineListItem extends Medicine {
  isActive: boolean;
  organizationPrice?: number;
  organizationCost?: number;
  organizationMedicineId?: string;
  selection?: boolean;
  productName?: string;
  mrp?: number;
  qty?: string;
  productForm?: string;
  medicineType?: string;
  price?: number;
}

export interface MedicineListResponse {
  medicines: MedicineListItem[];
  totalRecords: number;
  currentPage: number;
  totalPages: number;
  continuationToken?: string | undefined;
  hasMoreResults?: boolean | undefined;
  pageSize?: number | undefined;
  totalFetched?: number | undefined;
}

export interface MedicineListParams {
  searchText?: string;
  pageSize?: number;
  page?: number;
  organizationId: string;
  isActive?: boolean;
  continuationToken?: string;
}

export interface UpdateMedicineRequest {
  organizationId: string;
  isChecked?: string;
  selectAll?: boolean;
  medicines?: {
    medicineId: string;
    isActive: boolean;
    price?: number;
  }[];
}

export interface MedicineFilterForm {
  searchText: string;
  isActive: string;
}

export interface BulkUpdateForm {
  selectedMedicines: string[];
  action: 'activate' | 'deactivate' | 'updatePrice';
  price?: number;
}
