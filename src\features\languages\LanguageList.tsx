import { format } from 'date-fns';
import { Languages, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DataTable, { Column } from '../../components/Common/DataTable';
import Modal from '../../components/Common/Modal';
import OrganizationRequired from '../../components/Common/OrganizationRequired';
import SearchBar from '../../components/Common/SearchBar';
import StatusBadge from '../../components/Common/StatusBadge';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import { fetchLanguages } from '../../store/slices/languageSlice';
import { Language } from '../../types';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import { getOrganizationId } from '../../utils/organization-utils';
import LanguageForm from './LanguageForm';

const LanguageList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization, isSuperAdmin, isOrganizationAdmin } = useAuth();
  const { languages, loading, total, page, limit } = useSelector(
    (state: RootState) => state.languages
  );
  const organizationId = getCurrentOrganizationId();

  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<Language | null>(
    null
  );

  useEffect(() => {
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchLanguages({
          organizationId: orgId,
          page: 1,
          limit: 10,
        })
      );
    }
  }, [dispatch, selectedOrganization]);

  const handlePageChange = (newPage: number) => {
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchLanguages({
          organizationId: orgId,
          page: newPage,
          limit,
          search: searchValue,
          status: statusFilter,
        })
      );
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchLanguages({
          organizationId: orgId,
          page: 1,
          limit,
          search: value,
          status: statusFilter,
        })
      );
    }
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchLanguages({
          organizationId: orgId,
          page: 1,
          limit,
          search: searchValue,
          status,
        })
      );
    }
  };

  const handleCreate = () => {
    setSelectedLanguage(null);
    setShowModal(true);
  };

  const handleEdit = (language: Language) => {
    setSelectedLanguage(language);
    setShowModal(true);
  };

  const columns: Column<Language>[] = [
    {
      key: 'name',
      label: 'Language Name',
      render: (name, language) => (
        <div className='flex items-center space-x-3'>
          <div className='flex-shrink-0'>
            <div className='w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center'>
              <Languages className='w-5 h-5 text-indigo-600' />
            </div>
          </div>
          <div>
            <div className='font-medium text-gray-900'>{name}</div>
            <div className='text-sm text-gray-500'>
              ISO Code: {language.isoCode}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (status) => <StatusBadge status={status} />,
    },
    {
      key: 'createdAt',
      label: 'Created At',
      render: (createdAt) => format(new Date(createdAt), 'MMM dd, yyyy HH:mm'),
    },
  ];

  // For super admins, require organization selection
  if (isSuperAdmin && !selectedOrganization) {
    return <OrganizationRequired feature='languages' />;
  }

  // For organization admins or when organizationId is available, proceed
  if (!organizationId) {
    if (isOrganizationAdmin) {
      return (
        <OrganizationRequired
          feature='languages'
          customMessage='Unable to load your organization. Please try logging in again.'
          customTitle='Organization Loading Error'
        />
      );
    } else {
      return <OrganizationRequired feature='languages' />;
    }
  }

  return (
    <div className='space-y-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Languages</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Manage languages for{' '}
            {selectedOrganization?.name || 'Your Organization'}
          </p>
        </div>
        <div className='flex items-center gap-3'>
          <SearchBar
            value={searchValue}
            onChange={handleSearch}
            placeholder='Search languages...'
            size='md'
          />
          <button
            onClick={handleCreate}
            className='inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <Plus className='w-4 h-4 mr-2' />
            Add Language
          </button>
        </div>
      </div>

      <DataTable
        data={languages}
        columns={columns}
        loading={loading}
        pagination={{
          page,
          limit,
          total,
          onPageChange: handlePageChange,
        }}
        searchFilters={
          <div className='flex items-center space-x-4'>
            <label
              htmlFor='status-filter'
              className='text-sm font-medium text-gray-700'
            >
              Status:
            </label>
            <select
              id='status-filter'
              value={statusFilter}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
            >
              <option value=''>All</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        }
        onEdit={handleEdit}
      />

      <Modal
        isOpen={showModal}
        title={selectedLanguage ? 'Edit Language' : 'Create Language'}
        onClose={() => setShowModal(false)}
      >
        <LanguageForm
          language={selectedLanguage}
          onSuccess={() => setShowModal(false)}
        />
      </Modal>
    </div>
  );
};

export default LanguageList;
