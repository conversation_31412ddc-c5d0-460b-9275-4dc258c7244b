import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Role } from '../../../types';
import { ApiError, InitialState } from '../../../utils/reducer-utils';
import roleService, {
  CreateRoleData,
  RoleParams,
  UpdateRoleData,
} from './role.service';

type ExtendedInitialState = InitialState<Role> & {
  roles: Role[];
  currentRole: Role | null;
  total: number;
  totalPages: number;
  page: number;
  limit: number;
  searchName: string;
  error: string | null;
};

const initialState: ExtendedInitialState = {
  loading: true, // Start with loading true to show loading initially
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  errorMessage: null,
  successMessage: null,
  allEntities: [],
  roles: [],
  currentRole: null,
  total: 0,
  totalPages: 0,
  page: 1,
  limit: 10,
  searchName: '',
  error: null,
};

export const fetchRoles = createAsyncThunk(
  'roles/fetchRoles',
  async (params: RoleParams, { rejectWithValue }) => {
    try {
      return await roleService.fetchRoles(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchRoleById = createAsyncThunk(
  'roles/fetchRoleById',
  async (id: string, { rejectWithValue }) => {
    try {
      return await roleService.fetchRoleById(id);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const createRole = createAsyncThunk(
  'roles/createRole',
  async (data: CreateRoleData, { rejectWithValue }) => {
    try {
      return await roleService.createRole(data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updateRole = createAsyncThunk(
  'roles/updateRole',
  async (data: UpdateRoleData, { rejectWithValue }) => {
    try {
      return await roleService.updateRole(data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const deleteRole = createAsyncThunk(
  'roles/deleteRole',
  async (id: string, { rejectWithValue }) => {
    try {
      await roleService.deleteRole(id);
      return id;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const roleSlice = createSlice({
  name: 'roles',
  initialState,
  reducers: {
    setSearchName: (state, action) => {
      state.searchName = action.payload;
      state.page = 1;
      state.loading = true; // Set loading when search changes
      // Clear existing data to prevent showing old data while loading
      state.roles = [];
      state.total = 0;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    setPageSize: (state, action) => {
      state.limit = action.payload;
      state.page = 1;
    },
    resetFilters: (state) => {
      state.searchName = '';
      state.page = 1;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
      state.successMessage = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchRoles.fulfilled, (state, action) => {
        // Handle the API response structure: either an array directly or an object with items array
        const responseData = action?.payload;
        const rolesData = Array.isArray(responseData)
          ? responseData
          : responseData?.items || [];

        // Transform API roles to match our Role interface
        state.roles = rolesData.map((role: any) => ({
          id: role.id,
          name: role.name,
          description: role.description || '',
          organizationId: role.organizationId,
          permissions: [], // Default empty permissions for now
          isSystem: role.isDefault || false, // Map isDefault to isSystem for backward compatibility
          isDefault: role.isDefault || false, // Keep the original isDefault field
          createdAt: role.created_on ? new Date(role.created_on) : new Date(),
        }));

        // Handle pagination if response includes it, otherwise use default values
        if (!Array.isArray(responseData) && responseData) {
          state.total = responseData.totalItemCount || rolesData.length;
          state.totalPages = responseData.totalPages || 1;
          state.page = responseData.currentPage || state.page;
        } else {
          state.total = rolesData.length;
          state.totalPages = 1;
          state.page = 1;
        }

        state.loading = false;
        state.error = null;
      })
      .addCase(fetchRoleById.fulfilled, (state, action) => {
        state.currentRole = action.payload;
        state.loading = false;
        state.error = null;
      })
      .addCase(createRole.fulfilled, (state) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Role created successfully';
        state.error = null;
      })
      .addCase(createRole.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        const errorData = error?.response?.data as
          | string
          | { error?: string; message?: string }
          | undefined;

        // Handle different error response formats
        if (typeof errorData === 'string') {
          // If it's a string, use it directly
          state.error = errorData;
        } else if (errorData && 'error' in errorData) {
          // If it's an object with an 'error' property
          state.error = errorData.error || 'An unknown error occurred';
        } else if (errorData && 'message' in errorData) {
          // If it's an object with a 'message' property
          state.error = errorData.message || 'An unknown error occurred';
        } else {
          // Fallback to default error message
          state.error =
            error?.message || 'Failed to create role. Please try again.';
        }
        state.successMessage = null;
      })
      .addCase(updateRole.fulfilled, (state) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Role updated successfully';
        state.error = null;
      })
      .addCase(updateRole.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to update role. Please try again.';
        state.successMessage = null;
      })
      .addCase(deleteRole.fulfilled, (state, action) => {
        state.updating = false;
        state.updateSuccess = true;
        state.successMessage = 'Role deleted successfully';
        state.error = null;

        // Remove the role from the list
        const deletedRoleId = action.meta.arg;
        if (deletedRoleId) {
          state.roles = state.roles.filter((role) => role.id !== deletedRoleId);
          state.total = Math.max(0, state.total - 1);
        }
      })
      .addCase(deleteRole.rejected, (state, action) => {
        state.updating = false;
        state.updateSuccess = false;
        const error = action.payload as ApiError;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to delete role. Please try again.';
        state.successMessage = null;
      })
      .addMatcher(isPending(fetchRoles, fetchRoleById), (state) => {
        state.loading = true;
        state.error = null;
        // Clear existing data when starting a new fetch to prevent showing old data
        if (state.roles.length > 0) {
          state.roles = [];
          state.total = 0;
        }
      })
      .addMatcher(isPending(createRole, updateRole, deleteRole), (state) => {
        state.updating = true;
        state.updateSuccess = false;
        state.error = null;
        state.successMessage = null;
      })
      .addMatcher(isRejected(fetchRoles, fetchRoleById), (state, action) => {
        // Don't set loading to false if the request was cancelled (duplicate request)
        const error = action.payload as any;
        if (
          error?.name === 'RequestCancelledError' ||
          error?.message === 'Request was cancelled'
        ) {
          return; // Keep loading state as is for cancelled requests
        }

        state.loading = false;
        const apiError = error as ApiError;
        state.error =
          (typeof apiError?.response?.data === 'string'
            ? apiError.response.data
            : apiError?.response?.data?.message) ||
          apiError?.message ||
          'Failed to load roles. Please try again.';
      })
      // Reset slice on logout
      .addMatcher(
        (action) => action.type === 'RESET_ALL_SLICES',
        () => initialState
      );
  },
});

// Export actions
export const {
  setSearchName,
  setPage,
  setPageSize,
  resetFilters,
  clearMessages,
  clearUpdateSuccess,
} = roleSlice.actions;

export default roleSlice.reducer;
