import { format } from 'date-fns';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import DataTable, { Column } from '../../../components/Common/DataTable';
import SelectInput from '../../../components/Common/MUISelect';
import SearchBar from '../../../components/Common/SearchBar';
import DatePicker from '../../../components/DatePicker';
import { InvoiceDetailResponse } from '../../../store/features/invoices/invoice.service';
import { NormalizedInvoice } from '../../../store/features/invoices/invoice.slice';
import { useDoctorProfile } from '../hooks/useDoctorProfile';
import { useInvoiceById } from '../hooks/useInvoiceById';
import { useInvoices } from '../hooks/useInvoices';
import ConsultationBillModal from './ConsultationBillModal';
import DoctorProfileModal from './DoctorProfileModal';
import LabTestBillDetailsModal from './LabTestBillDetailsModal';
import PatientProfileModal from './PatientProfileModal';
import PatientRegistrationBillModal from './PatientRegistrationBillModal';
import PrescriptionBillDetailsModal from './PrescriptionBillDetailsModal';

interface InvoiceListProps {
  organizationName: string;
  organizationId?: string | null;
}

const billTypeOptions = [
  { label: 'All Bills', value: 'all' },
  { label: 'Consultation', value: 'consultation' },
  { label: 'Patient Registration', value: 'patient_registration' },
  { label: 'Pharmacy', value: 'prescription' },
  { label: 'Lab Test', value: 'lab_test' },
];

const genderOptions = [
  { label: 'All Genders', value: 'all' },
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' },
  { label: 'Others', value: 'others' },
];

const InvoiceListComponent: React.FC<InvoiceListProps> = memo(
  ({ organizationName, organizationId }) => {
    const {
      invoices,
      loading,
      total,
      page,
      limit,
      searchText,
      billTypeFilter,
      genderFilter,
      fromDate,
      toDate,
      search,
      filterByBillType,
      filterByGender,
      filterByDateRange,
      changePage,
      clearFilters,
    } = useInvoices(organizationId);

    const [localSearchText, setLocalSearchText] = useState(searchText);
    const [_expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
    const [selectedInvoice, setSelectedInvoice] =
      useState<NormalizedInvoice | null>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isPatientModalOpen, setIsPatientModalOpen] = useState(false);
    const [isDoctorModalOpen, setIsDoctorModalOpen] = useState(false);
    const [selectedDoctorInvoice, setSelectedDoctorInvoice] =
      useState<NormalizedInvoice | null>(null);
    const [selectedPrescriptionInvoice, setSelectedPrescriptionInvoice] =
      useState<any>(null);
    const [selectedPrescriptionId, setSelectedPrescriptionId] = useState<
      string | null
    >(null);
    const [selectedLabTestInvoice, setSelectedLabTestInvoice] =
      useState<any>(null);
    const [selectedLabTestId, setSelectedLabTestId] = useState<string | null>(
      null
    );
    const [
      selectedPatientRegistrationInvoice,
      setSelectedPatientRegistrationInvoice,
    ] = useState<any>(null);
    const [selectedPatientRegistrationId, setSelectedPatientRegistrationId] =
      useState<string | null>(null);

    const {
      invoice: normalizedInvoiceDetail,
      invoiceDetail: fetchedInvoiceDetail,
      loading: invoiceDetailLoading,
      fetchInvoice,
    } = useInvoiceById(organizationId);

    const {
      invoice: _normalizedPatientInvoice,
      invoiceDetail: patientInvoiceDetail,
      loading: patientInvoiceDetailLoading,
      fetchInvoice: fetchPatientInvoice,
    } = useInvoiceById(organizationId);

    const {
      doctorProfile,
      invoiceDetail: doctorProfileInvoiceDetail,
      profilePictureUrl,
      loading: doctorProfileLoading,
      fetchDoctorProfile,
      clearDoctorProfile,
    } = useDoctorProfile(organizationId);

    useEffect(() => {
      setLocalSearchText(searchText);
    }, [searchText]);

    const toggleRowExpansion = useCallback((invoiceId: string) => {
      setExpandedRows((prev) => {
        const newSet = new Set(prev);
        if (newSet.has(invoiceId)) {
          newSet.delete(invoiceId);
        } else {
          newSet.add(invoiceId);
        }
        return newSet;
      });
    }, []);

    const handleExpandClick = useCallback(
      (invoice: NormalizedInvoice) => {
        const billType = invoice.billType?.toLowerCase();
        if (billType === 'consultation') {
          setSelectedInvoice(invoice);
          setIsModalOpen(true);
          fetchInvoice(invoice.id);
        } else if (billType === 'patient_registration') {
          setSelectedPatientRegistrationId(invoice.id);
          setSelectedPatientRegistrationInvoice(null);
          fetchInvoice(invoice.id)
            .then((data) => {
              if (data) {
                setSelectedPatientRegistrationInvoice(data);
              }
            })
            .catch((error) => {
              console.error(
                'Error fetching patient registration details:',
                error
              );
            });
        } else if (billType === 'prescription') {
          setSelectedPrescriptionId(invoice.id);
          setSelectedPrescriptionInvoice(null);
          fetchInvoice(invoice.id)
            .then((data) => {
              if (data) {
                setSelectedPrescriptionInvoice(data);
              }
            })
            .catch((error) => {
              console.error('Error fetching prescription details:', error);
            });
        } else if (billType === 'lab_test') {
          setSelectedLabTestId(invoice.id);
          setSelectedLabTestInvoice(null);
          fetchInvoice(invoice.id)
            .then((data) => {
              if (data) {
                setSelectedLabTestInvoice(data);
              }
            })
            .catch((error) => {
              console.error('Error fetching lab test details:', error);
            });
        } else {
          toggleRowExpansion(invoice.id);
        }
      },
      [fetchInvoice, toggleRowExpansion]
    );

    const handleCloseModal = useCallback(() => {
      setIsModalOpen(false);
      setSelectedInvoice(null);
    }, []);

    const handlePatientNameClick = useCallback(
      (invoice: NormalizedInvoice) => {
        setIsPatientModalOpen(true);
        fetchPatientInvoice(invoice.id);
      },
      [fetchPatientInvoice]
    );

    const handleClosePatientModal = useCallback(() => {
      setIsPatientModalOpen(false);
    }, []);

    const handleDoctorNameClick = useCallback(
      (invoice: NormalizedInvoice) => {
        if (!invoice.doctorName) return;
        setSelectedDoctorInvoice(invoice);
        setIsDoctorModalOpen(true);
        clearDoctorProfile();
        fetchDoctorProfile(invoice.id);
      },
      [fetchDoctorProfile, clearDoctorProfile]
    );

    const handleCloseDoctorModal = useCallback(() => {
      setIsDoctorModalOpen(false);
      setSelectedDoctorInvoice(null);
      clearDoctorProfile();
    }, [clearDoctorProfile]);

    const columns: Column<NormalizedInvoice>[] = useMemo(
      () => [
        {
          key: 'invoiceDate',
          label: 'Date',
          width: '120px',
          render: (value) => {
            if (!value) return '-';
            try {
              const parsedDate =
                typeof value === 'string' || value instanceof Date
                  ? new Date(value)
                  : null;
              if (!parsedDate || Number.isNaN(parsedDate.getTime())) {
                return '-';
              }
              return format(parsedDate, 'dd/MM/yyyy');
            } catch {
              return '-';
            }
          },
        },
        {
          key: 'billType',
          label: 'Type',
          width: '140px',
          render: (type) => {
            if (!type) return '-';
            const typeStr = type.toString();
            if (typeStr.toLowerCase() === 'prescription') return 'Pharmacy';
            return typeStr
              .replace(/[_-]/g, ' ')
              .replace(/\b\w/g, (char: string) => char.toUpperCase());
          },
        },
        {
          key: 'patientId',
          label: 'Patient ID',
          width: '120px',
          render: (id) => id || '-',
        },
        {
          key: 'patientName',
          label: 'Full Name',
          width: '180px',
          render: (name, invoice) => (
            <button
              onClick={() => handlePatientNameClick(invoice)}
              className='block truncate max-w-[160px] text-left cursor-pointer'
              title='Click to view details'
            >
              {name || '-'}
            </button>
          ),
        },
        {
          key: 'gender',
          label: 'Gender',
          width: '100px',
          render: (gender) => {
            if (!gender) return '-';
            const genderStr = String(gender).trim();
            if (genderStr.toLowerCase() === 'male') return 'M';
            if (genderStr.toLowerCase() === 'female') return 'F';
            return genderStr;
          },
        },
        {
          key: 'amount',
          label: 'Amount (₹)',
          width: '140px',
          render: (_, invoice) =>
            invoice.amount !== undefined && invoice.amount !== null
              ? `${invoice.currency || '₹'}${Number(invoice.amount).toFixed(2)}`
              : '-',
        },
        {
          key: 'modeOfPayment',
          label: 'Mode of Payment',
          width: '140px',
          render: (mode) => mode || '-',
        },
        {
          key: 'transactionId',
          label: 'Transaction ID',
          width: '160px',
          render: (tx) => tx || '-',
        },
        {
          key: 'doctorName',
          label: 'Doctor Name',
          width: '160px',
          render: (doctor, invoice) =>
            doctor ? (
              <button
                onClick={() => handleDoctorNameClick(invoice)}
                className='block truncate max-w-[150px] text-left '
                title='Click to view details'
              >
                {doctor}
              </button>
            ) : (
              '-'
            ),
        },
        {
          key: 'expand',
          label: '',
          width: '50px',
          render: (_, invoice) => {
            return (
              <button
                onClick={() => handleExpandClick(invoice)}
                className='flex items-center justify-center w-6 h-6 rounded-full border-[0.5px] border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-colors p-1'
                aria-label='Expand row'
              >
                <img
                  src='/images/expand-icon.svg'
                  alt='Expand'
                  className='w-3 h-3'
                />
              </button>
            );
          },
        },
      ],
      [handleExpandClick, handlePatientNameClick, handleDoctorNameClick]
    );

    const handleSearchChange = (value: string) => {
      setLocalSearchText(value);
      search(value);
    };

    return (
      <div className='space-y-6'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Invoices</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Administrative view of invoices for {organizationName}
          </p>
        </div>

        <DataTable<NormalizedInvoice>
          columns={columns}
          data={invoices}
          loading={loading}
          searchFilters={
            <div className='flex w-full flex-wrap items-center gap-1 lg:flex-nowrap'>
              <SearchBar
                className='w-[190px]'
                value={localSearchText}
                onChange={handleSearchChange}
                placeholder='Search by Patient name/ID'
                size='md'
              />
              <SelectInput
                value={billTypeFilter}
                onChange={(e) => filterByBillType(e.target.value)}
                options={billTypeOptions}
                size='small'
                sx={{ minWidth: 110, width: 110, height: 20, mb: 3 }}
              />
              <SelectInput
                value={genderFilter}
                onChange={(e) => filterByGender(e.target.value)}
                options={genderOptions}
                size='small'
                sx={{ minWidth: 130, width: 130, height: 20, mb: 3 }}
              />
              <div className='flex items-center gap-2'>
                <span className='text-sm text-gray-600'>Date</span>
                <DatePicker
                  value={fromDate ? new Date(fromDate) : null}
                  onChange={(date) => {
                    const formatted = date
                      ? `${date.getFullYear()}-${String(
                          date.getMonth() + 1
                        ).padStart(2, '0')}-${String(date.getDate()).padStart(
                          2,
                          '0'
                        )}`
                      : '';
                    filterByDateRange(formatted, toDate);
                  }}
                  placeholder='Start date'
                  className='w-[130px] rounded-lg border border-gray-300 px-2 py-1 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500'
                />
                <span className='text-gray-400'>to</span>
                <DatePicker
                  value={toDate ? new Date(toDate) : null}
                  onChange={(date) => {
                    const formatted = date
                      ? `${date.getFullYear()}-${String(
                          date.getMonth() + 1
                        ).padStart(2, '0')}-${String(date.getDate()).padStart(
                          2,
                          '0'
                        )}`
                      : '';
                    filterByDateRange(fromDate, formatted);
                  }}
                  placeholder='End date'
                  className='w-[140px] rounded-lg border border-gray-300 px-2 py-1 text-sm focus:border-transparent focus:ring-2 focus:ring-blue-500'
                />
              </div>
              <AppButton
                kind='default'
                sx={{ minWidth: 120 }}
                onClick={() => {
                  clearFilters();
                  setLocalSearchText('');
                }}
              >
                Clear Filter
              </AppButton>
            </div>
          }
          pagination={{
            total,
            page,
            limit,
            onPageChange: changePage,
          }}
        />

        {/* Consultation Bill Modal */}
        <ConsultationBillModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          invoice={selectedInvoice || normalizedInvoiceDetail}
          invoiceDetail={fetchedInvoiceDetail as InvoiceDetailResponse | null}
          loading={invoiceDetailLoading}
        />

        {/* Patient Profile Modal */}
        <PatientProfileModal
          isOpen={isPatientModalOpen}
          onClose={handleClosePatientModal}
          invoiceDetail={patientInvoiceDetail as InvoiceDetailResponse | null}
          invoices={invoices}
          loading={patientInvoiceDetailLoading}
        />

        <DoctorProfileModal
          isOpen={isDoctorModalOpen}
          onClose={handleCloseDoctorModal}
          doctorProfile={doctorProfile}
          profilePictureUrl={profilePictureUrl}
          loading={doctorProfileLoading}
          invoiceDoctor={
            doctorProfileInvoiceDetail?.doctor ||
            (selectedDoctorInvoice?.doctorName
              ? { name: selectedDoctorInvoice.doctorName }
              : null)
          }
        />

        <PrescriptionBillDetailsModal
          isOpen={!!selectedPrescriptionId}
          onClose={() => {
            setSelectedPrescriptionId(null);
            setSelectedPrescriptionInvoice(null);
          }}
          invoice={selectedPrescriptionInvoice}
          isLoading={!selectedPrescriptionInvoice}
        />
        <LabTestBillDetailsModal
          isOpen={!!selectedLabTestId}
          onClose={() => {
            setSelectedLabTestId(null);
            setSelectedLabTestInvoice(null);
          }}
          invoice={selectedLabTestInvoice}
          isLoading={!selectedLabTestInvoice}
        />
        <PatientRegistrationBillModal
          isOpen={!!selectedPatientRegistrationId}
          onClose={() => {
            setSelectedPatientRegistrationId(null);
            setSelectedPatientRegistrationInvoice(null);
          }}
          invoice={null}
          invoiceDetail={selectedPatientRegistrationInvoice}
          loading={
            !selectedPatientRegistrationInvoice &&
            !!selectedPatientRegistrationId
          }
        />
      </div>
    );
  }
);

InvoiceListComponent.displayName = 'InvoiceListComponent';

export default InvoiceListComponent;
