import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import { generateMockApiResponse, mockBranches } from '../../data/mockData';
import { Branch, PaginatedResponse } from '../../types';

interface BranchState {
  branches: Branch[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: BranchState = {
  branches: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

export const fetchBranches = createAsyncThunk(
  'branches/fetchBranches',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    let filteredBranches = mockBranches.filter(
      (branch) => branch.organizationId === params.organizationId
    );

    if (params.search) {
      filteredBranches = filteredBranches.filter((branch) =>
        branch.name.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    if (params.status) {
      filteredBranches = filteredBranches.filter(
        (branch) => branch.status === params.status
      );
    }

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredBranches.slice(startIndex, endIndex);

    const response: PaginatedResponse<Branch> = {
      data: paginatedData,
      total: filteredBranches.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 500);
  }
);

export const createBranch = createAsyncThunk(
  'branches/createBranch',
  async (branchData: Omit<Branch, 'id' | 'createdAt'>) => {
    const newBranch: Branch = {
      id: uuidv4(),
      ...branchData,
      createdAt: new Date(),
    };
    mockBranches.unshift(newBranch);
    return await generateMockApiResponse(newBranch, 800);
  }
);

export const updateBranch = createAsyncThunk(
  'branches/updateBranch',
  async ({ id, data }: { id: string; data: Partial<Branch> }) => {
    const index = mockBranches.findIndex((branch) => branch.id === id);
    if (index === -1) {
      throw new Error('Branch not found');
    }
    const updatedBranch = { ...mockBranches[index], ...data } as Branch;
    mockBranches[index] = updatedBranch;
    return await generateMockApiResponse(updatedBranch, 800);
  }
);

const branchSlice = createSlice({
  name: 'branches',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchBranches.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBranches.fulfilled, (state, action) => {
        state.loading = false;
        state.branches = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchBranches.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch branches';
      })
      .addCase(createBranch.fulfilled, (state, action) => {
        state.branches.unshift(action.payload.data);
        state.total += 1;
      })
      .addCase(updateBranch.fulfilled, (state, action) => {
        const index = state.branches.findIndex(
          (branch) => branch.id === action.payload.data.id
        );
        if (index !== -1) {
          state.branches[index] = action.payload.data;
        }
      });
  },
});

export default branchSlice.reducer;
