import React, { forwardRef } from 'react';

export interface TextInputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  endDecoration?: React.ReactNode;
  iconClassName?: string;
  error?: boolean;
  helperText?: string;
}

const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (
    {
      endDecoration,
      iconClassName,
      error,
      helperText,
      className = '',
      ...props
    },
    ref
  ) => {
    const baseClasses =
      'w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent';
    const errorClasses = error
      ? 'border-red-300 focus:ring-red-500 focus:border-red-300'
      : 'border-gray-300';

    const inputClasses = `${baseClasses} ${errorClasses} ${endDecoration ? 'pr-10' : ''} ${className}`;

    return (
      <div className='relative w-full'>
        <input ref={ref} className={inputClasses} {...props} />
        {endDecoration && (
          <div
            className={`absolute inset-y-0 right-0 flex items-center pr-2 ${iconClassName || ''}`}
          >
            {endDecoration}
          </div>
        )}
        {helperText && (
          <p
            className={`mt-1 text-sm ${error ? 'text-red-600' : 'text-gray-500'}`}
          >
            {helperText}
          </p>
        )}
      </div>
    );
  }
);

TextInput.displayName = 'TextInput';

export default TextInput;
