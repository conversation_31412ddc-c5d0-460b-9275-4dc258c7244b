import { PATHS } from '../constants/paths';
import { isOrganizationAdmin, isSuperAdmin } from './auth-utils';

// Track if we've already handled the current session
let currentSessionHandled = false;

export const handlePostLoginRedirect = (
  navigate: (path: string, options?: { replace?: boolean }) => void,
  currentPath?: string
) => {
  const isLoginOrRoot =
    currentPath === PATHS.LOGIN || currentPath === PATHS.ROOT;

  if (!currentSessionHandled && isLoginOrRoot) {
    currentSessionHandled = true;

    if (isSuperAdmin()) {
      // Super Admin should go to organizations page
      navigate(PATHS.ORGANIZATIONS, { replace: true });
    } else if (isOrganizationAdmin()) {
      // Organization Admin should go to users page
      navigate(PATHS.USERS, { replace: true });
    } else {
      // Any other role should be redirected to unauthorized page
      navigate('/unauthorized', { replace: true });
    }
  }
};

// Export a function to reset the redirect flag if needed (e.g., after logout)
export const resetRedirectState = () => {
  currentSessionHandled = false;
};
