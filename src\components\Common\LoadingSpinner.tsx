import React from 'react';
import { PiSpinnerGapBold } from 'react-icons/pi';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

const sizeClasses = {
  sm: 'text-lg',
  md: 'text-2xl',
  lg: 'text-4xl',
};

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  text,
  className = '',
}) => {
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <PiSpinnerGapBold
        className={`animate-spin text-blue-600 ${sizeClasses[size]}`}
      />
      {text && <p className='mt-2 text-sm text-gray-600'>{text}</p>}
    </div>
  );
};

export default LoadingSpinner;
