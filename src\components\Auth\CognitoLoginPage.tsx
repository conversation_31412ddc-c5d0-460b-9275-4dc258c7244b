/**
 * Cognito Login Page Component
 * Example implementation of a login page using AWS Cognito
 * This can be used as a reference or directly integrated into your app
 */

import React, { useEffect } from 'react';

import { useCognitoAuth } from '../../hooks/useCognitoAuth';

const CognitoLoginPage: React.FC = () => {
  const { isAuthenticated, isLoading, error, login, cognitoUser, emrUser } =
    useCognitoAuth();

  useEffect(() => {
    // If user is authenticated, redirect to dashboard
    if (isAuthenticated && !isLoading) {
      console.log('User authenticated, redirecting to dashboard...');
      // You can customize this redirect based on your routing
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated, isLoading]);

  const handleLogin = async () => {
    try {
      await login();
    } catch (err) {
      console.error('Login failed:', err);
    }
  };

  if (isLoading) {
    return (
      <div style={styles.container}>
        <div style={styles.card}>
          <h1 style={styles.title}>Loading...</h1>
          <p style={styles.text}>Please wait while we authenticate you.</p>
        </div>
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div style={styles.container}>
        <div style={styles.card}>
          <h1 style={styles.title}>Welcome!</h1>
          <p style={styles.text}>You are logged in.</p>
          {cognitoUser && (
            <div style={styles.userInfo}>
              <p>
                <strong>Email:</strong> {cognitoUser.email}
              </p>
              {cognitoUser.name && (
                <p>
                  <strong>Name:</strong> {cognitoUser.name}
                </p>
              )}
            </div>
          )}
          {emrUser && (
            <div style={styles.userInfo}>
              <p>
                <strong>Role:</strong> {emrUser.userRole}
              </p>
              <p>
                <strong>Organization:</strong> {emrUser.organizationName}
              </p>
            </div>
          )}
          <p style={styles.text}>Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={styles.container}>
      <div style={styles.card}>
        <h1 style={styles.title}>Cognito Login</h1>
        <p style={styles.subtitle}>Sign in with AWS Cognito</p>

        {error && (
          <div style={styles.error}>
            <p>{error}</p>
          </div>
        )}

        <button
          onClick={handleLogin}
          style={styles.button}
          disabled={isLoading}
        >
          {isLoading ? 'Logging in...' : 'Login with Cognito'}
        </button>

        <div style={styles.info}>
          <p style={styles.infoText}>
            This will redirect you to AWS Cognito for authentication.
          </p>
        </div>
      </div>
    </div>
  );
};

// Inline styles for the component
const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: '#f5f5f5',
    fontFamily: 'Arial, sans-serif',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: '8px',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
    padding: '40px',
    maxWidth: '500px',
    width: '100%',
    textAlign: 'center',
  },
  title: {
    fontSize: '32px',
    fontWeight: 'bold',
    marginBottom: '10px',
    color: '#333',
  },
  subtitle: {
    fontSize: '16px',
    color: '#666',
    marginBottom: '30px',
  },
  text: {
    fontSize: '14px',
    color: '#666',
    marginBottom: '20px',
  },
  button: {
    backgroundColor: '#FF9900',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: 'bold',
    cursor: 'pointer',
    width: '100%',
    transition: 'background-color 0.3s',
  },
  error: {
    backgroundColor: '#fee',
    border: '1px solid #fcc',
    borderRadius: '4px',
    padding: '12px',
    marginBottom: '20px',
    color: '#c33',
  },
  info: {
    marginTop: '20px',
    padding: '15px',
    backgroundColor: '#f9f9f9',
    borderRadius: '4px',
  },
  infoText: {
    fontSize: '12px',
    color: '#666',
    margin: 0,
  },
  userInfo: {
    textAlign: 'left',
    backgroundColor: '#f9f9f9',
    padding: '15px',
    borderRadius: '4px',
    marginBottom: '20px',
  },
};

export default CognitoLoginPage;
