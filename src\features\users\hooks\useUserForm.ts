import { yupResolver } from '@hookform/resolvers/yup';
import { useEffect, useMemo } from 'react';
import { useForm, useWatch } from 'react-hook-form';

import { UserFormSchema, userFormSchema } from '../schemas/user.schema';
import { User } from '../types/user.types';

interface UseUserFormProps {
  user?: User | null;
  onSubmit: (data: UserFormSchema) => void | Promise<void>;
  onSuccess?: () => void;
}

export const useUserForm = ({
  user,
  onSubmit,
  onSuccess,
}: UseUserFormProps) => {
  const isEditing = !!user;

  const defaultValues = useMemo(
    () => ({
      name: user?.name || '',
      email: user?.email || '',
      userRole: user?.userRole || '',
      roleId: user?.roleId || '',
      status: user?.status || 'active',
      consultationFee: user?.consultationFee || null,
    }),
    [user]
  );

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting, isValid, isDirty },
    reset,
    setValue,
    watch,
    trigger,
  } = useForm<UserFormSchema>({
    defaultValues,
    resolver: yupResolver(userFormSchema) as any, // Type assertion to handle the resolver type mismatch
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  // Watch for changes to form values
  const formValues = useWatch({
    control,
  });

  // Reset form when user changes (for edit mode)
  useEffect(() => {
    reset(defaultValues);
    // Trigger validation after a small delay to ensure all fields are registered
    const timer = setTimeout(() => {
      trigger();
    }, 100);
    return () => clearTimeout(timer);
  }, [defaultValues, reset, trigger]);

  const handleFormSubmit = async (data: UserFormSchema) => {
    try {
      await onSubmit(data);
      onSuccess?.();
    } catch (error) {
      // Error is handled by the parent component
      console.error('Form submission error:', error);
    }
  };

  return {
    control,
    handleSubmit: handleSubmit(handleFormSubmit),
    errors,
    isSubmitting,
    isValid,
    isDirty,
    isEditing,
    setValue,
    watch,
    formValues,
  };
};
