import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { AppDispatch } from '../store';
import { getCurrentOrganizationId } from '../utils/auth-utils';
import { useAuth } from './useAuth';

/**
 * Hook that triggers API calls when navigating to specific pages
 * This ensures fresh data is loaded every time user clicks on menu items
 */
export const useNavigationApiTrigger = () => {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();
  const { isAuthenticated, isSuperAdmin, selectedOrganization } = useAuth();
  const organizationId = getCurrentOrganizationId();

  // Track the previous pathname to only trigger on actual navigation
  const prevPathnameRef = useRef<string>('');

  useEffect(() => {
    // Skip if this is the first render or if pathname hasn't actually changed
    if (prevPathnameRef.current === location.pathname) {
      return;
    }

    // Update the previous pathname
    prevPathnameRef.current = location.pathname;

    // Only trigger if user is authenticated and not on auth pages
    if (!isAuthenticated) return;
    if (
      location.pathname === '/login' ||
      location.pathname === '/set-password' ||
      location.pathname === '/password-reset-success' ||
      location.pathname === '/' ||
      location.pathname === '/dashboard'
    )
      return;

    // Get the current organization ID to use for API calls
    let currentOrgId: string | null = null;

    if (isSuperAdmin && selectedOrganization?.id) {
      currentOrgId = selectedOrganization.id;
    } else if (!isSuperAdmin && organizationId) {
      currentOrgId = organizationId;
    }

    // For super admin without selected organization, allow access to users page
    const isSuperAdminUsersPage =
      isSuperAdmin && location.pathname === '/users';

    // If no organization ID available and not super admin users page, don't make API calls
    if (!currentOrgId && !isSuperAdminUsersPage) {
      return;
    }

    // NOTE: Removed API calls for /users and /roles as these pages handle their own data fetching
    // through their respective hooks (useUsers, useRoles) to prevent race conditions and loading state conflicts

    // Only trigger API calls for pages that don't have their own data fetching logic
    // Currently no pages need navigation-triggered API calls as all list pages handle their own fetching
  }, [
    location.pathname,
    isAuthenticated,
    isSuperAdmin,
    selectedOrganization,
    organizationId,
    dispatch,
  ]);
};
