<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Cognito Configuration Checker</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 30px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    }

    h1 {
      color: #333;
      margin-bottom: 10px;
    }

    .subtitle {
      color: #666;
      margin-bottom: 30px;
    }

    .section {
      margin-bottom: 30px;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 8px;
      border-left: 4px solid #667eea;
    }

    .section h2 {
      color: #333;
      margin-bottom: 15px;
      font-size: 18px;
    }

    .config-item {
      margin-bottom: 15px;
      padding: 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
    }

    .config-label {
      font-weight: 600;
      color: #555;
      margin-bottom: 5px;
      font-size: 13px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .config-value {
      font-family: 'Courier New', monospace;
      color: #333;
      word-break: break-all;
      font-size: 14px;
      padding: 8px;
      background: #f5f5f5;
      border-radius: 4px;
      margin-top: 5px;
    }

    .button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      margin-right: 10px;
      margin-bottom: 10px;
      transition: transform 0.2s;
    }

    .button:hover {
      transform: translateY(-2px);
    }

    .button:active {
      transform: translateY(0);
    }

    .button.secondary {
      background: #6c757d;
    }

    .alert {
      padding: 15px;
      border-radius: 6px;
      margin-bottom: 20px;
    }

    .alert.error {
      background: #fee;
      border: 1px solid #fcc;
      color: #c33;
    }

    .alert.success {
      background: #efe;
      border: 1px solid #cfc;
      color: #3c3;
    }

    .alert.warning {
      background: #fff3cd;
      border: 1px solid #ffc107;
      color: #856404;
    }

    .alert.info {
      background: #e3f2fd;
      border: 1px solid #2196f3;
      color: #0d47a1;
    }

    .checklist {
      list-style: none;
      padding: 0;
    }

    .checklist li {
      padding: 10px;
      margin-bottom: 8px;
      background: white;
      border-radius: 4px;
      border-left: 3px solid #ccc;
    }

    .checklist li.checked {
      border-left-color: #4caf50;
    }

    .checklist li.unchecked {
      border-left-color: #f44336;
    }

    pre {
      background: #2d2d2d;
      color: #f8f8f2;
      padding: 15px;
      border-radius: 6px;
      overflow-x: auto;
      font-size: 13px;
      line-height: 1.5;
    }

    .copy-btn {
      background: #4caf50;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      margin-top: 10px;
    }

    .test-result {
      margin-top: 15px;
      padding: 15px;
      border-radius: 6px;
      background: #f5f5f5;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔍 Cognito Configuration Checker</h1>
    <p class="subtitle">Verify your AWS Cognito setup before testing login</p>

    <div id="alerts"></div>

    <div class="section">
      <h2>📋 Current Configuration</h2>
      <div class="config-item">
        <div class="config-label">Client ID</div>
        <div class="config-value" id="clientId"></div>
      </div>
      <div class="config-item">
        <div class="config-label">Cognito Domain</div>
        <div class="config-value" id="domain"></div>
      </div>
      <div class="config-item">
        <div class="config-label">Redirect URI</div>
        <div class="config-value" id="redirectUri"></div>
      </div>
      <div class="config-item">
        <div class="config-label">Authorization URL</div>
        <div class="config-value" id="authUrl"></div>
      </div>
    </div>

    <div class="section">
      <h2>🧪 Quick Tests</h2>
      <button class="button" onclick="testDomain()">Test Domain Accessibility</button>
      <button class="button" onclick="testAuthEndpoint()">Test Auth Endpoint</button>
      <button class="button secondary" onclick="copyConfig()">Copy Config to Clipboard</button>
      <div id="testResults"></div>
    </div>

    <div class="section">
      <h2>✅ Setup Checklist</h2>
      <p style="margin-bottom: 15px; color: #666;">Verify these items in your AWS Cognito Console:</p>
      <ul class="checklist">
        <li id="check1" class="unchecked">
          <strong>User Pool exists</strong> - Go to AWS Cognito → User Pools
        </li>
        <li id="check2" class="unchecked">
          <strong>App Client created</strong> - User Pool → App Integration → App clients
        </li>
        <li id="check3" class="unchecked">
          <strong>Domain configured</strong> - User Pool → App Integration → Domain
        </li>
        <li id="check4" class="unchecked">
          <strong>Callback URLs added</strong> - App client → Hosted UI → Allowed callback URLs
        </li>
        <li id="check5" class="unchecked">
          <strong>OAuth flows enabled</strong> - App client → Hosted UI → OAuth 2.0 grant types
        </li>
      </ul>
      <button class="button" onclick="markAllChecked()" style="margin-top: 15px;">Mark All as Checked</button>
    </div>

    <div class="section">
      <h2>🔧 How to Fix "Domain does not exist"</h2>
      <div class="alert warning">
        <strong>⚠️ Common Causes:</strong>
        <ol style="margin-left: 20px; margin-top: 10px;">
          <li>The Cognito domain hasn't been created in AWS Console</li>
          <li>The domain format is incorrect (check for typos)</li>
          <li>The User Pool is in a different region</li>
          <li>The domain is a custom domain that needs DNS setup</li>
        </ol>
      </div>

      <h3 style="margin-top: 20px; margin-bottom: 10px;">Step-by-Step Fix:</h3>
      <ol style="margin-left: 20px; line-height: 1.8;">
        <li><strong>Go to AWS Cognito Console</strong> → Select your region (ap-south-1)</li>
        <li><strong>Select your User Pool</strong> (or create one if it doesn't exist)</li>
        <li><strong>Go to "App Integration" tab</strong></li>
        <li><strong>Scroll to "Domain" section</strong></li>
        <li><strong>Click "Actions" → "Create Cognito domain"</strong></li>
        <li><strong>Enter a domain prefix</strong> (e.g., "arca-dev-829876691474")</li>
        <li><strong>Click "Create"</strong></li>
        <li><strong>Copy the full domain</strong> (it will be: your-prefix.auth.region.amazoncognito.com)</li>
        <li><strong>Update cognitoConfig.ts</strong> with the correct domain</li>
      </ol>
    </div>

    <div class="section">
      <h2>📝 Expected Domain Format</h2>
      <pre>your-domain-prefix.auth.ap-south-1.amazoncognito.com</pre>
      <p style="margin-top: 10px; color: #666;">
        <strong>Example:</strong> arca-dev-829876691474.auth.ap-south-1.amazoncognito.com
      </p>
    </div>

    <div class="section">
      <h2>🔗 Useful AWS Console Links</h2>
      <button class="button" onclick="openAWSConsole('cognito')">Open Cognito Console</button>
      <button class="button" onclick="openAWSConsole('userpool')">Open User Pools</button>
    </div>
  </div>

  <script>
    // Configuration
    const CLIENT_ID = '3a59nceivtu4virh7s06jkkpfu';
    const COGNITO_DOMAIN = 'arca-dev-829876691474.auth.ap-south-1.amazoncognito.com';
    const REDIRECT_URI = window.location.origin;

    // Display configuration
    document.getElementById('clientId').textContent = CLIENT_ID;
    document.getElementById('domain').textContent = COGNITO_DOMAIN;
    document.getElementById('redirectUri').textContent = REDIRECT_URI;

    const authUrl = `https://${COGNITO_DOMAIN}/oauth2/authorize?response_type=code&client_id=${CLIENT_ID}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}`;
    document.getElementById('authUrl').textContent = authUrl;

    // Test domain accessibility
    async function testDomain() {
      const resultsDiv = document.getElementById('testResults');
      resultsDiv.innerHTML = '<div class="test-result">Testing domain accessibility...</div>';

      try {
        // Try to fetch the domain (will fail due to CORS, but we can check if domain exists)
        const testUrl = `https://${COGNITO_DOMAIN}/.well-known/jwks.json`;

        const response = await fetch(testUrl, { method: 'HEAD', mode: 'no-cors' });

        resultsDiv.innerHTML = `
          <div class="alert success">
            <strong>✅ Domain appears to be accessible!</strong><br>
            The domain <code>${COGNITO_DOMAIN}</code> seems to exist.
          </div>
        `;
      } catch (error) {
        resultsDiv.innerHTML = `
          <div class="alert error">
            <strong>❌ Cannot verify domain</strong><br>
            Error: ${error.message}<br><br>
            This might be a CORS issue (normal) or the domain doesn't exist.<br>
            Try the "Test Auth Endpoint" button instead.
          </div>
        `;
      }
    }

    // Test auth endpoint by redirecting
    function testAuthEndpoint() {
      if (confirm('This will redirect you to the Cognito login page. Continue?')) {
        const params = new URLSearchParams({
          response_type: 'code',
          client_id: CLIENT_ID,
          redirect_uri: REDIRECT_URI,
          scope: 'openid email profile'
        });

        window.location.href = `https://${COGNITO_DOMAIN}/oauth2/authorize?${params}`;
      }
    }

    // Copy configuration to clipboard
    function copyConfig() {
      const config = `
Cognito Configuration:
=====================
Client ID: ${CLIENT_ID}
Domain: ${COGNITO_DOMAIN}
Redirect URI: ${REDIRECT_URI}
Region: ap-south-1

Authorization URL:
${authUrl}
      `.trim();

      navigator.clipboard.writeText(config).then(() => {
        showAlert('Configuration copied to clipboard!', 'success');
      }).catch(() => {
        showAlert('Failed to copy. Please copy manually.', 'error');
      });
    }

    // Mark all checklist items as checked
    function markAllChecked() {
      for (let i = 1; i <= 5; i++) {
        const item = document.getElementById(`check${i}`);
        item.className = 'checked';
      }
      showAlert('All items marked as checked!', 'success');
    }

    // Show alert
    function showAlert(message, type) {
      const alertsDiv = document.getElementById('alerts');
      const alert = document.createElement('div');
      alert.className = `alert ${type}`;
      alert.textContent = message;
      alertsDiv.appendChild(alert);

      setTimeout(() => {
        alert.remove();
      }, 5000);
    }

    // Open AWS Console
    function openAWSConsole(type) {
      const region = 'ap-south-1';
      let url;

      if (type === 'cognito') {
        url = `https://${region}.console.aws.amazon.com/cognito/v2/home?region=${region}`;
      } else if (type === 'userpool') {
        url = `https://${region}.console.aws.amazon.com/cognito/v2/idp/user-pools?region=${region}`;
      }

      window.open(url, '_blank');
    }

    // Check if we're on localhost
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      showAlert('⚠️ Make sure to add http://localhost:3000 to Cognito Allowed callback URLs', 'warning');
    }
  </script>
</body>
</html>
