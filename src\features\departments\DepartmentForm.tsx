import { Save, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch } from '../../store';
import {
  createDepartment,
  updateDepartment,
} from '../../store/slices/departmentSlice';
import { Department } from '../../types';
import { getOrganizationId } from '../../utils/organization-utils';

interface DepartmentFormProps {
  department?: Department | null;
  onSuccess: () => void;
}

const DepartmentForm: React.FC<DepartmentFormProps> = ({
  department,
  onSuccess,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'active' as 'active' | 'inactive',
  });

  useEffect(() => {
    if (department) {
      setFormData({
        name: department.name,
        description: department.description || '',
        status: department.status,
      });
    }
  }, [department]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    if (!formData.name.trim()) newErrors.name = 'Department name is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !selectedOrganization) return;

    setLoading(true);
    try {
      const orgId = getOrganizationId(selectedOrganization);
      if (!orgId) {
        throw new Error('No organization selected');
      }

      // Create a new object with the correct types
      const departmentData: Omit<Department, 'id' | 'createdAt'> = {
        name: formData.name,
        description: formData.description,
        status: formData.status,
        organizationId: orgId, // This is now guaranteed to be a string
      };

      if (department) {
        await dispatch(
          updateDepartment({ id: department.id, data: departmentData })
        );
      } else {
        await dispatch(createDepartment(departmentData));
      }
      onSuccess();
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange =
    (field: keyof typeof formData) =>
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
    ) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }));
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: '' }));
      }
    };

  return (
    <form onSubmit={handleSubmit} className='p-6 space-y-6'>
      <div className='space-y-4'>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Department Name *
          </label>
          <input
            type='text'
            value={formData.name}
            onChange={handleChange('name')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.name ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder='Enter department name'
          />
          {errors.name && (
            <p className='mt-1 text-sm text-red-600'>{errors.name}</p>
          )}
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={handleChange('description')}
            rows={3}
            className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
            placeholder='Enter a brief description'
          />
        </div>

        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Status
          </label>
          <select
            value={formData.status}
            onChange={handleChange('status')}
            className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
          >
            <option value='active'>Active</option>
            <option value='inactive'>Inactive</option>
          </select>
        </div>
      </div>

      <div className='flex justify-end pt-4 space-x-4'>
        <button
          type='button'
          onClick={onSuccess}
          className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
        >
          <X className='w-4 h-4 mr-2' />
          Cancel
        </button>
        <button
          type='submit'
          disabled={loading}
          className='inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50'
        >
          {loading ? <LoadingSpinner /> : <Save className='w-4 h-4 mr-2' />}
          {department ? 'Save Changes' : 'Create Department'}
        </button>
      </div>
    </form>
  );
};

export default DepartmentForm;
