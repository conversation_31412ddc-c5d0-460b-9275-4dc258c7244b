import { ReactNode, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

interface URLRedirectHandlerProps {
  children: ReactNode;
}

/**
 * Component to handle URL redirects for HashRouter compatibility
 * Redirects old BrowserRouter URLs to HashRouter format
 */
const URLRedirectHandler: React.FC<URLRedirectHandlerProps> = ({
  children,
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Check if we're on the root path with query parameters
    // This handles cases like: /set-password?token=... -> /#/set-password?token=...
    if (location.pathname === '/' && location.search) {
      const urlParams = new URLSearchParams(location.search);
      const token = urlParams.get('token');

      if (token) {
        // This is likely a set-password link, redirect to hash route
        navigate(`/set-password${location.search}`, { replace: true });
        return;
      }
    }

    // Handle other potential redirects
    // Add more redirect logic here if needed
  }, [location, navigate]);

  return <>{children}</>; // This component doesn't render anything
};

export default URLRedirectHandler;
