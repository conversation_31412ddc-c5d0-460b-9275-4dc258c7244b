import { Building2, ChevronDown, LogOut, Menu, User } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { isSuperAdminRole } from '../../constants/roles';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import {
  clearSelectedOrganization,
  selectOrganization,
} from '../../store/features/auth/auth.slice';
import { fetchAllOrganizations } from '../../store/features/organizations/organization.slice';
import { getOrganizationId } from '../../utils/organization-utils';

interface HeaderProps {
  onMenuToggle: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    user,
    selectedOrganization,
    isSuperAdmin,
    isAuthenticated,
    getDisplayName,
    loggingOut,
  } = useAuth();
  const { allOrganizations, loading } = useSelector(
    (state: RootState) => state.organizations
  );

  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showOrgSelector, setShowOrgSelector] = useState(false);
  const [orgSearchTerm, setOrgSearchTerm] = useState('');

  // Refs for click outside functionality
  const orgSelectorRef = React.useRef<HTMLDivElement>(null);
  const userMenuRef = React.useRef<HTMLDivElement>(null);

  // Load selected organization from localStorage on component mount
  // This effect should run immediately when the component mounts, even before user data is fully loaded
  useEffect(() => {
    // Check if user is likely a super admin from localStorage (for immediate restoration)
    const userRole = localStorage.getItem('userRole');
    const isLikelySuperAdmin = isSuperAdminRole(userRole);

    if (isAuthenticated && (isSuperAdmin || isLikelySuperAdmin)) {
      const savedOrg = localStorage.getItem('selectedOrganization');
      if (savedOrg) {
        try {
          const org = JSON.parse(savedOrg);
          // Only update if the organization in localStorage is different from the current one
          if (!selectedOrganization || selectedOrganization.id !== org.id) {
            dispatch(selectOrganization(org.id));
          }
        } catch (error) {
          console.error('Failed to parse saved organization:', error);
        }
      }
    }
  }, [dispatch, isAuthenticated, isSuperAdmin, selectedOrganization]);

  // Track if we've already fetched organizations to prevent duplicate fetches
  const hasFetchedOrgs = React.useRef(false);

  // Fetch all organizations when component mounts or when organization data changes (for Super Admin)
  useEffect(() => {
    // Don't fetch if logging out
    if (loggingOut) return;

    const fetchOrgs = async () => {
      if (isAuthenticated && isSuperAdmin) {
        try {
          // Only fetch if we haven't already fetched or if we're forcing a refresh
          if (!hasFetchedOrgs.current) {
            await dispatch(fetchAllOrganizations()).unwrap();
            hasFetchedOrgs.current = true;
          }
        } catch (error: unknown) {
          // Ignore errors if the request was aborted or we're logging out
          if (
            error instanceof Error &&
            error.name !== 'AbortError' &&
            !loggingOut
          ) {
            console.error('Failed to fetch organizations:', error);
          }
        }
      }
    };

    // Reset the fetch flag when authentication state changes
    if (!isAuthenticated) {
      hasFetchedOrgs.current = false;
    }

    fetchOrgs();
  }, [dispatch, isAuthenticated, isSuperAdmin, loggingOut]);

  // Handle organization selector toggle (open/close dropdown)
  const handleOrgSelectorToggle = (isOpen: boolean) => {
    setShowOrgSelector(isOpen);
    // No need to fetch organizations every time dropdown opens
    // Organizations are already fetched when component mounts
  };

  // Click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        orgSelectorRef.current &&
        !orgSelectorRef.current.contains(event.target as Node)
      ) {
        setShowOrgSelector(false);
        setOrgSearchTerm(''); // Clear search when closing
      }
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = async () => {
    try {
      // Use unified logout to handle both Cognito and B2C
      const { logout } = await import('../../utils/unifiedAuth');
      await logout();
      // No need to navigate - logout will redirect automatically
    } catch (error) {
      console.error('Logout failed:', error);
      // Fallback: clear everything and redirect manually
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/login';
    }
  };

  const handleOrganizationSelect = async (orgId: string) => {
    try {
      // If orgId is empty string, it means "All Organizations" - clear selection
      if (orgId === '') {
        dispatch(clearSelectedOrganization());
      } else {
        // Find the organization in the list
        const org = allOrganizations.find((o) => o.id === orgId);
        if (org) {
          // Save the full organization object to localStorage
          localStorage.setItem('selectedOrganization', JSON.stringify(org));
          // Update the selected organization in Redux
          dispatch(selectOrganization(orgId));
        }
      }
      setShowOrgSelector(false);
      setOrgSearchTerm(''); // Clear search when selecting
    } catch (error) {
      console.error('Error selecting organization:', error);
    }
  };

  const filteredOrganizations = allOrganizations
    .filter((org) => org.isActive)
    .filter((org) => {
      const searchTerm = orgSearchTerm.trim().toLowerCase();
      if (!searchTerm) return true;
      return (
        org.name.toLowerCase().startsWith(searchTerm) ||
        org.contactEmail.toLowerCase().startsWith(searchTerm)
      );
    });

  return (
    <header className='bg-white shadow-sm border-b border-gray-200 px-4 py-3'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center space-x-4'>
          <button
            onClick={onMenuToggle}
            className='md:hidden text-gray-500 hover:text-gray-700'
          >
            <Menu className='w-6 h-6' />
          </button>

          <div className='hidden md:block'>
            <h1 className='text-xl font-semibold text-gray-900'>
              {isSuperAdmin
                ? selectedOrganization
                  ? selectedOrganization.name
                  : 'Arcaai Administration Portal'
                : user?.organizationName || 'Arcaai Administration Portal'}
            </h1>
          </div>
        </div>

        <div className='flex items-center space-x-4'>
          {/* Organization Display/Selector */}
          {isSuperAdmin && (
            <div className='relative' ref={orgSelectorRef}>
              {/* Super Admin: Show organization selector */}
              <button
                onClick={() => handleOrgSelectorToggle(!showOrgSelector)}
                className='flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors'
              >
                <Building2 className='w-4 h-4' />
                <span className='hidden sm:inline'>
                  {selectedOrganization?.name || 'All Organizations'}
                </span>
                <ChevronDown className='w-4 h-4' />
              </button>

              {isSuperAdmin && showOrgSelector && (
                <div className='absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50'>
                  <div className='p-3'>
                    <p className='text-xs font-medium text-gray-500 uppercase tracking-wide mb-3'>
                      Select Organization
                    </p>

                    {/* Search Input */}
                    {allOrganizations.length > 5 && (
                      <div className='mb-3'>
                        <input
                          type='text'
                          placeholder='Search organizations...'
                          value={orgSearchTerm}
                          onChange={(e) => setOrgSearchTerm(e.target.value)}
                          className='w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
                        />
                      </div>
                    )}

                    {loading ? (
                      <div className='px-3 py-2 text-sm text-gray-500'>
                        Loading organizations...
                      </div>
                    ) : allOrganizations.length === 0 ? (
                      <div className='px-3 py-2 text-sm text-gray-500'>
                        No organizations found
                      </div>
                    ) : filteredOrganizations.length === 0 ? (
                      <div className='px-3 py-2 text-sm text-gray-500'>
                        No organizations match your search
                      </div>
                    ) : (
                      <div className='max-h-64 overflow-y-auto scrollbar-thin'>
                        {/* All Organizations option - always show for super admins */}
                        <button
                          onClick={() => handleOrganizationSelect('')}
                          className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors border-b border-gray-100 ${
                            !selectedOrganization
                              ? 'bg-blue-100 text-blue-700'
                              : 'hover:bg-gray-100'
                          }`}
                        >
                          <div className='font-medium text-blue-600'>
                            All Organizations
                          </div>
                          <div className='text-xs text-gray-500'>
                            View data across all organizations
                          </div>
                        </button>

                        {filteredOrganizations.map((org) => (
                          <button
                            key={org.id}
                            onClick={() => {
                              const orgId = getOrganizationId(org);
                              if (orgId) {
                                handleOrganizationSelect(orgId);
                              }
                            }}
                            className={`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${
                              selectedOrganization?.id === org.id
                                ? 'bg-blue-100 text-blue-700'
                                : 'hover:bg-gray-100'
                            }`}
                          >
                            <div className='font-medium'>{org.name}</div>
                            <div className='text-xs text-gray-500'>
                              {org.contactEmail}
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* User Menu */}
          <div className='relative' ref={userMenuRef}>
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className='flex items-center space-x-2 p-2 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors'
            >
              <div className='w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center'>
                <User className='w-4 h-4 text-white' />
              </div>
              <div className='hidden sm:block text-left'>
                <p className='text-sm font-medium'>{getDisplayName()}</p>
                <p className='text-xs text-gray-500'>{user?.userRole || ''}</p>
              </div>
              <ChevronDown className='w-4 h-4' />
            </button>

            {showUserMenu && (
              <div className='absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50'>
                <div className='p-2'>
                  <div className='px-3 py-2 border-b border-gray-100'>
                    <p className='text-sm font-medium text-gray-900'>
                      {getDisplayName()}
                    </p>
                    <p className='text-xs text-gray-500'>{user?.email}</p>
                  </div>

                  {/* TODO: Implement Profile Settings functionality in future */}
                  {/* <button className='w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md flex items-center space-x-2'>
                    <Settings className='w-4 h-4' />
                    <span>Profile Settings</span>
                  </button> */}

                  <button
                    onClick={handleLogout}
                    className='w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md flex items-center space-x-2'
                  >
                    <LogOut className='w-4 h-4' />
                    <span>Sign Out</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
