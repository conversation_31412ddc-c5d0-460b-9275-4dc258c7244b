import CloseIcon from '@mui/icons-material/Close';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  IconButton,
  Switch,
} from '@mui/material';
import { Plus, X } from 'lucide-react';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import ReactQuill from 'react-quill';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import MUISelect from '../../../components/Common/MUISelect';
import { useToast } from '../../../contexts/ToastContext';
import {
  getSubscriptionFeatures,
  getSubscriptionPlanById,
  SubscriptionFeature,
  SubscriptionPlan,
  SubscriptionPlanFeature,
  SubscriptionPlanFeaturesPayload,
  updateSubscriptionPlan,
  UpdateSubscriptionPlanRequest,
} from '../../../store/features/subscription/subscription.service';
import {
  applyListItemStyles,
  defaultQuillFormats,
  defaultQuillModules,
  normalizeListFormatting,
} from '../../../utils/richText';
import HierarchicalFeatureSelect, {
  type HierarchicalFeatureOption,
} from './HierarchicalFeatureSelect';

// Helper function to check if error is from a cancelled request
const isCancelledRequestError = (error: unknown): boolean => {
  if (!error) return false;
  const errorObj = error as Record<string, unknown>;
  return (
    errorObj?.name === 'RequestCancelledError' ||
    errorObj?.name === 'CanceledError' ||
    errorObj?.name === 'Cancel' ||
    errorObj?.code === 'ERR_CANCELED' ||
    errorObj?.code === 'CANCELLED' ||
    (typeof errorObj?.message === 'string' &&
      errorObj.message.toLowerCase().includes('cancel'))
  );
};

export type SectionKey = 'admin' | 'mrd' | 'emr' | 'billing';
export type RowType = 'section' | 'entry';

export interface PlanRow {
  id: string;
  rowType: RowType;
  section: SectionKey;
  feature?: string;
  monthlyAmount?: string;
  yearlyAmount?: string;
}

type RowFieldErrors = {
  monthly?: boolean;
  yearly?: boolean;
};

type RowErrorState = Record<string, RowFieldErrors>;

interface PlanEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  planId: string | null;
  onPlanUpdated?: () => void;
}

type FeatureOption = { label: string; value: string };
type SelectableFeatureOption = FeatureOption & { disabled?: boolean };

const ALL_FEATURE_OPTION_VALUE = '__ALL__';

const typeToSectionKey: Record<
  'MRD' | 'EMR' | 'Billing',
  Exclude<SectionKey, 'admin'>
> = {
  MRD: 'mrd',
  EMR: 'emr',
  Billing: 'billing',
};

const createEmptyFeatureOptions = (): Record<SectionKey, FeatureOption[]> => ({
  admin: [],
  mrd: [],
  emr: [],
  billing: [],
});

// Define the order for MRD features (after "All")
const MRD_FEATURE_ORDER = [
  'dashboard',
  'patient registration',
  'book consultation',
  'patient queue',
  'vitals',
];

// Define the order for EMR features (after "All")
const EMR_FEATURE_ORDER = [
  'dashboard',
  'patient info',
  'consultation',
  'lab master',
  'lab',
  'prescription',
  'lifestyle',
  'my profile',
];

const getFeatureSortOrder = (
  featureName: string,
  section: SectionKey
): number => {
  if (section === 'mrd') {
    const lowerName = featureName.toLowerCase().trim();
    // Try exact match first, then partial match
    const index = MRD_FEATURE_ORDER.findIndex(
      (orderItem) => lowerName === orderItem || lowerName.includes(orderItem)
    );
    if (index !== -1) {
      return index;
    }
    // Features not in the order list come after
    return MRD_FEATURE_ORDER.length;
  }
  // For other sections, maintain alphabetical order
  return -1;
};

const getEmrFeatureSortOrder = (featureName: string): number => {
  const lowerName = featureName.toLowerCase().trim();
  // Try exact match first, then partial match
  const index = EMR_FEATURE_ORDER.findIndex(
    (orderItem) => lowerName === orderItem || lowerName.includes(orderItem)
  );
  if (index !== -1) {
    return index;
  }
  // Features not in the order list come after
  return EMR_FEATURE_ORDER.length;
};

const mapFeaturesToOptions = (
  features: SubscriptionFeature[]
): Record<SectionKey, FeatureOption[]> => {
  const grouped = createEmptyFeatureOptions();

  features.forEach((feature) => {
    if (!feature.isActive) return;
    const section = typeToSectionKey[feature.type];
    if (!section) return;

    grouped[section].push({
      label: feature.featureName,
      value: feature.id ?? feature.featureName,
    });
  });

  (['mrd', 'emr', 'billing'] as Array<Exclude<SectionKey, 'admin'>>).forEach(
    (section) => {
      if (section === 'mrd') {
        // Sort MRD features according to custom order
        grouped[section].sort((a, b) => {
          const orderA = getFeatureSortOrder(a.label, section);
          const orderB = getFeatureSortOrder(b.label, section);
          if (orderA !== orderB) {
            return orderA - orderB;
          }
          // If same order priority, sort alphabetically
          return a.label.localeCompare(b.label);
        });
      } else {
        // For other sections, sort alphabetically
        grouped[section].sort((a, b) => a.label.localeCompare(b.label));
      }
      grouped[section] = [
        { label: 'All', value: ALL_FEATURE_OPTION_VALUE },
        ...grouped[section],
      ];
    }
  );

  return grouped;
};

// Map features to hierarchical structure for EMR
const mapFeaturesToHierarchicalOptions = (
  features: SubscriptionFeature[]
): HierarchicalFeatureOption[] => {
  const result: HierarchicalFeatureOption[] = [];
  const parentFeatures = new Map<string, SubscriptionFeature>();
  const childFeatures = new Map<string, SubscriptionFeature[]>();

  // First pass: identify parent features and group children
  features.forEach((feature) => {
    if (!feature.isActive) return;
    if (feature.type !== 'EMR') return;

    if (feature.subType && feature.subType.trim() !== '') {
      // This is a child feature
      const subType = feature.subType.trim();
      if (!childFeatures.has(subType)) {
        childFeatures.set(subType, []);
      }
      const children = childFeatures.get(subType);
      if (children) {
        children.push(feature);
      }
    } else {
      // This is a parent feature (no subType)
      // Store by trimmed featureName to handle whitespace issues
      const trimmedName = feature.featureName.trim();
      parentFeatures.set(trimmedName, feature);
    }
  });

  // Second pass: create hierarchical structure
  // Process parents that have children
  const processedParents = new Set<string>();

  childFeatures.forEach((children, subType) => {
    // Find the parent feature with name matching subType (trimmed comparison)
    const parentFeature = parentFeatures.get(subType);

    if (parentFeature) {
      // Parent exists, create hierarchical option
      const hierarchicalOption: HierarchicalFeatureOption = {
        label: parentFeature.featureName,
        value: parentFeature.id ?? parentFeature.featureName,
        disabled: false,
        children: children.map((child) => ({
          label: child.featureName,
          value: child.id ?? child.featureName,
          disabled: false,
        })),
      };
      result.push(hierarchicalOption);
      processedParents.add(parentFeature.featureName.trim());
    } else {
      // Parent doesn't exist, create a virtual parent
      const hierarchicalOption: HierarchicalFeatureOption = {
        label: subType,
        value: `__parent__${subType}`,
        disabled: false,
        children: children.map((child) => ({
          label: child.featureName,
          value: child.id ?? child.featureName,
          disabled: false,
        })),
      };
      result.push(hierarchicalOption);
    }
  });

  // Add remaining parent features without children
  parentFeatures.forEach((parent) => {
    const trimmedName = parent.featureName.trim();
    if (!processedParents.has(trimmedName)) {
      result.push({
        label: parent.featureName,
        value: parent.id ?? parent.featureName,
        disabled: false,
      });
    }
  });

  // Sort: use custom EMR order, then parents with children, then regular parents
  result.sort((a, b) => {
    // Skip "All" option in sorting (it will be added at the top)
    if (a.value === ALL_FEATURE_OPTION_VALUE) return -1;
    if (b.value === ALL_FEATURE_OPTION_VALUE) return 1;

    const aOrder = getEmrFeatureSortOrder(a.label);
    const bOrder = getEmrFeatureSortOrder(b.label);

    // If both have custom order, sort by order
    if (
      aOrder !== EMR_FEATURE_ORDER.length &&
      bOrder !== EMR_FEATURE_ORDER.length
    ) {
      if (aOrder !== bOrder) {
        return aOrder - bOrder;
      }
    } else if (aOrder !== EMR_FEATURE_ORDER.length) {
      return -1; // a has order, b doesn't
    } else if (bOrder !== EMR_FEATURE_ORDER.length) {
      return 1; // b has order, a doesn't
    }

    // If same order or both not in order list, sort by children first, then alphabetically
    const aHasChildren = a.children && a.children.length > 0;
    const bHasChildren = b.children && b.children.length > 0;
    if (aHasChildren && !bHasChildren) return -1;
    if (!aHasChildren && bHasChildren) return 1;
    return a.label.localeCompare(b.label);
  });

  // Add "All" option at the top
  result.unshift({
    label: 'All',
    value: ALL_FEATURE_OPTION_VALUE,
    disabled: false,
  });

  return result;
};

const createBaseSectionRows = (): PlanRow[] => [
  { id: 'sec-mrd', rowType: 'section', section: 'mrd' },
  { id: 'sec-emr', rowType: 'section', section: 'emr' },
  { id: 'sec-billing', rowType: 'section', section: 'billing' },
];

const createAddOnSectionRows = (): PlanRow[] => [
  { id: 'addon-sec-mrd', rowType: 'section', section: 'mrd' },
  { id: 'addon-sec-emr', rowType: 'section', section: 'emr' },
  { id: 'addon-sec-billing', rowType: 'section', section: 'billing' },
];

const PlanEditModal: React.FC<PlanEditModalProps> = ({
  isOpen,
  onClose,
  planId,
  onPlanUpdated,
}) => {
  const [planName, setPlanName] = useState('');
  const [validity, setValidity] = useState<'both' | 'monthly' | 'yearly'>(
    'both'
  );
  const [description, setDescription] = useState('');
  const [addOnEnabled, setAddOnEnabled] = useState(false);
  const [isActive, setIsActive] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState(false);
  const { success, error: showError } = useToast();

  const sectionLabels: Record<SectionKey, string> = {
    admin: 'Admin',
    mrd: 'MRD',
    emr: 'EMR',
    billing: 'Billing',
  };
  const [featureOptions, setFeatureOptions] = useState<
    Record<SectionKey, FeatureOption[]>
  >(createEmptyFeatureOptions);
  const [hierarchicalEmrOptions, setHierarchicalEmrOptions] = useState<
    HierarchicalFeatureOption[]
  >([]);
  const [isFetchingFeatures, setIsFetchingFeatures] = useState(false);
  const [featuresLoaded, setFeaturesLoaded] = useState(false);
  const [planLoaded, setPlanLoaded] = useState(false);
  const isInitialLoading =
    loadingPlan || isFetchingFeatures || !featuresLoaded || !planLoaded;

  const loadFeatureOptions = useCallback(async () => {
    setIsFetchingFeatures(true);
    setFeaturesLoaded(false);
    try {
      const response = await getSubscriptionFeatures();
      const features = response?.features ?? [];
      setFeatureOptions(mapFeaturesToOptions(features));
      // Create hierarchical options for EMR
      setHierarchicalEmrOptions(mapFeaturesToHierarchicalOptions(features));
    } catch (err) {
      if (!isCancelledRequestError(err)) {
        showError(
          'Error',
          'Failed to load subscription features. Please try again later.'
        );
      }
    } finally {
      setIsFetchingFeatures(false);
      setFeaturesLoaded(true);
    }
  }, [showError]);

  useEffect(() => {
    if (!isOpen) return;
    loadFeatureOptions();
  }, [isOpen, loadFeatureOptions]);

  useEffect(() => {
    if (!isOpen) return;
    const editor = quillRef.current?.getEditor?.();
    if (!editor) return;
    applyListItemStyles(editor.root);
  }, [description, isOpen]);

  // Make images selectable
  useEffect(() => {
    if (!isOpen) return;
    const editor = quillRef.current?.getEditor?.();
    if (!editor) return;

    const root = editor.root;
    const handleImageClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'IMG') {
        e.preventDefault();
        e.stopPropagation();

        // Find the index of the image by creating a range before it
        const range = document.createRange();
        range.setStartBefore(target);
        range.collapse(true);

        // Get the selection and find the index
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(range);
        }

        // Use Quill's selection to get the index
        setTimeout(() => {
          const quillSelection = editor.getSelection();
          if (quillSelection) {
            editor.setSelection(quillSelection.index, 1, 'user');
          } else {
            // Fallback: find index by counting text nodes before the image
            let index = 0;
            let node: Node | null = root.firstChild;
            while (node && node !== target.parentNode) {
              if (node.nodeType === Node.TEXT_NODE) {
                index += (node.textContent || '').length;
              } else if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                if (element.tagName === 'IMG') {
                  index += 1;
                } else {
                  index += (element.textContent || '').length;
                }
              }
              node = node.nextSibling;
            }
            editor.setSelection(index, 1, 'user');
          }
        }, 0);
      }
    };

    root.addEventListener('click', handleImageClick, true);

    return () => {
      root.removeEventListener('click', handleImageClick, true);
    };
  }, [isOpen, description]);

  const [rows, setRows] = useState<PlanRow[]>(() => createBaseSectionRows());

  const [addOnRows, setAddOnRows] = useState<PlanRow[]>(() =>
    createAddOnSectionRows()
  );
  const quillRef = useRef<ReactQuill | null>(null);

  const getSectionSelections = (
    targetRows: PlanRow[],
    section: SectionKey,
    excludeRowId?: string
  ): Set<string> => {
    const selections = new Set<string>();
    targetRows.forEach((row) => {
      if (
        row.rowType === 'entry' &&
        row.section === section &&
        row.feature &&
        row.id !== excludeRowId
      ) {
        selections.add(row.feature);
      }
    });
    return selections;
  };

  const getPlanFeatureOptions = (row: PlanRow): SelectableFeatureOption[] => {
    if (row.section === 'admin') {
      return featureOptions[row.section] ?? [];
    }

    const baseOptions = featureOptions[row.section] ?? [];
    const otherSelections = getSectionSelections(rows, row.section, row.id);
    const otherHasAll = otherSelections.has(ALL_FEATURE_OPTION_VALUE);
    const addOnSelections = getSectionSelections(addOnRows, row.section);
    const addOnHasAll = addOnSelections.has(ALL_FEATURE_OPTION_VALUE);

    return baseOptions.map((option) => {
      if (option.value === row.feature) {
        return option;
      }

      if (option.value === ALL_FEATURE_OPTION_VALUE) {
        return {
          ...option,
          disabled: otherSelections.size > 0 || addOnSelections.size > 0,
        };
      }

      return {
        ...option,
        disabled:
          otherSelections.has(option.value) ||
          otherHasAll ||
          addOnSelections.has(option.value) ||
          addOnHasAll,
      };
    });
  };

  const getAddOnFeatureOptions = (row: PlanRow): SelectableFeatureOption[] => {
    if (row.section === 'admin') {
      return [];
    }

    const baseOptions = featureOptions[row.section] ?? [];
    const filteredOptions = baseOptions.filter(
      (option) =>
        option.value !== ALL_FEATURE_OPTION_VALUE ||
        row.feature === ALL_FEATURE_OPTION_VALUE
    );

    const baseSelections = getSectionSelections(rows, row.section);
    const addOnSelections = getSectionSelections(
      addOnRows,
      row.section,
      row.id
    );
    const baseHasAll = baseSelections.has(ALL_FEATURE_OPTION_VALUE);

    return filteredOptions.map((option) => {
      if (option.value === row.feature) {
        return option;
      }

      return {
        ...option,
        disabled:
          baseHasAll ||
          addOnSelections.has(option.value) ||
          baseSelections.has(option.value),
      };
    });
  };

  const [rowErrors, setRowErrors] = useState<RowErrorState>({});
  const [addOnRowErrors, setAddOnRowErrors] = useState<RowErrorState>({});
  const [planNameError, setPlanNameError] = useState('');
  const [planFeatureError, setPlanFeatureError] = useState('');

  const handlePlanNameChange = useCallback(
    (value: string) => {
      setPlanName(value);
      if (planNameError && value.trim()) {
        if (/[a-zA-Z]/.test(value)) {
          setPlanNameError('');
        }
      }
    },
    [planNameError]
  );

  const resetFormState = useCallback(() => {
    setPlanName('');
    setDescription('');
    setValidity('both');
    setIsActive(true);
    setAddOnEnabled(false);
    setRows(createBaseSectionRows());
    setAddOnRows(createAddOnSectionRows());
    setRowErrors({});
    setAddOnRowErrors({});
    setPlanLoaded(false);
    setPlanNameError('');
    setPlanFeatureError('');
  }, []);

  useEffect(() => {
    if (!isOpen) {
      resetFormState();
    }
  }, [isOpen, resetFormState]);

  const hasValue = (value?: string) => {
    if (value === undefined || value === null) {
      return false;
    }
    const stringValue = String(value);
    return stringValue.trim() !== '';
  };

  const clearFieldError = (
    setter: React.Dispatch<React.SetStateAction<RowErrorState>>,
    id: string,
    field: 'monthly' | 'yearly'
  ) => {
    setter((prev) => {
      const existing = prev[id];
      if (!existing?.[field]) {
        return prev;
      }
      const next = { ...prev };
      const nextEntry = { ...existing };
      delete nextEntry[field];
      if (Object.keys(nextEntry).length === 0) {
        delete next[id];
      } else {
        next[id] = nextEntry;
      }
      return next;
    });
  };

  const removeRowError = (
    setter: React.Dispatch<React.SetStateAction<RowErrorState>>,
    id: string
  ) => {
    setter((prev) => {
      if (!prev[id]) {
        return prev;
      }
      const next = { ...prev };
      delete next[id];
      return next;
    });
  };

  const resolveFeatureValue = useCallback(
    (section: SectionKey, feature: SubscriptionPlanFeature): string => {
      if (!feature) return '';
      const options = featureOptions[section];
      if (feature.featureId) {
        const byId = options.find((opt) => opt.value === feature.featureId);
        if (byId) return byId.value;
      }
      const featureName = feature.featureName;
      if (featureName) {
        const lowerName = featureName.toLowerCase();
        const byLabel = options.find(
          (opt) => opt.label.toLowerCase() === lowerName
        );
        if (byLabel) return byLabel.value;
      }
      return '';
    },
    [featureOptions]
  );

  const populateForm = useCallback(
    (plan: SubscriptionPlan) => {
      setPlanName(plan.planName || '');
      setDescription(plan.description || '');
      const validityLower = plan.validity?.toLowerCase() as
        | 'both'
        | 'monthly'
        | 'yearly';
      setValidity(validityLower || 'both');
      setIsActive(plan.isActive ?? true);

      // Populate base features - insert entries right after their section headers
      const newRows: PlanRow[] = [
        { id: 'sec-mrd', rowType: 'section', section: 'mrd' },
        { id: 'sec-emr', rowType: 'section', section: 'emr' },
        { id: 'sec-billing', rowType: 'section', section: 'billing' },
      ];

      // Helper function to find section index and insert entries after it
      const insertEntriesAfterSection = (
        section: SectionKey,
        entries: PlanRow[]
      ) => {
        const sectionIndex = newRows.findIndex(
          (r) => r.rowType === 'section' && r.section === section
        );
        if (sectionIndex !== -1) {
          // Insert entries right after the section header
          newRows.splice(sectionIndex + 1, 0, ...entries);
        }
      };

      // Add MRD features right after MRD section
      if (plan.features.MRD?.length) {
        const mrdEntries: PlanRow[] = plan.features.MRD.map((feature) => {
          const featureValue = resolveFeatureValue('mrd', feature);
          return {
            id: `mrd-entry-${Date.now()}-${Math.random()}`,
            rowType: 'entry',
            section: 'mrd',
            feature: featureValue || feature.featureId || '',
            monthlyAmount: feature.monthlyAmount.toString(),
            yearlyAmount: feature.yearlyAmount.toString(),
          };
        });
        insertEntriesAfterSection('mrd', mrdEntries);
      }

      // Add EMR features right after EMR section
      if (plan.features.EMR?.length) {
        const emrEntries: PlanRow[] = plan.features.EMR.map((feature) => {
          const featureValue = resolveFeatureValue('emr', feature);
          return {
            id: `emr-entry-${Date.now()}-${Math.random()}`,
            rowType: 'entry',
            section: 'emr',
            feature: featureValue || feature.featureId || '',
            monthlyAmount: feature.monthlyAmount.toString(),
            yearlyAmount: feature.yearlyAmount.toString(),
          };
        });
        insertEntriesAfterSection('emr', emrEntries);
      }

      // Add Billing features right after Billing section
      if (plan.features.Billing?.length) {
        const billingEntries: PlanRow[] = plan.features.Billing.map(
          (feature) => {
            const featureValue = resolveFeatureValue('billing', feature);
            return {
              id: `billing-entry-${Date.now()}-${Math.random()}`,
              rowType: 'entry',
              section: 'billing',
              feature: featureValue || feature.featureId || '',
              monthlyAmount: feature.monthlyAmount.toString(),
              yearlyAmount: feature.yearlyAmount.toString(),
            };
          }
        );
        insertEntriesAfterSection('billing', billingEntries);
      }

      setRows(newRows);
      setPlanFeatureError('');

      // Populate add-on features - insert entries right after their section headers
      const newAddOnRows: PlanRow[] = [
        { id: 'addon-sec-mrd', rowType: 'section', section: 'mrd' },
        { id: 'addon-sec-emr', rowType: 'section', section: 'emr' },
        { id: 'addon-sec-billing', rowType: 'section', section: 'billing' },
      ];

      const hasAddOns =
        (plan.addOnFeatures.MRD?.length ?? 0) > 0 ||
        (plan.addOnFeatures.EMR?.length ?? 0) > 0 ||
        (plan.addOnFeatures.Billing?.length ?? 0) > 0;

      setAddOnEnabled(hasAddOns);

      if (hasAddOns) {
        // Helper function to find section index and insert entries after it
        const insertAddOnEntriesAfterSection = (
          section: SectionKey,
          entries: PlanRow[]
        ) => {
          const sectionIndex = newAddOnRows.findIndex(
            (r) => r.rowType === 'section' && r.section === section
          );
          if (sectionIndex !== -1) {
            // Insert entries right after the section header
            newAddOnRows.splice(sectionIndex + 1, 0, ...entries);
          }
        };

        // Add MRD add-ons right after MRD section
        if (plan.addOnFeatures.MRD?.length) {
          const mrdAddOnEntries: PlanRow[] = plan.addOnFeatures.MRD.map(
            (feature) => {
              const featureValue = resolveFeatureValue('mrd', feature);
              return {
                id: `addon-mrd-entry-${Date.now()}-${Math.random()}`,
                rowType: 'entry',
                section: 'mrd',
                feature: featureValue || feature.featureId || '',
                monthlyAmount: feature.monthlyAmount.toString(),
                yearlyAmount: feature.yearlyAmount.toString(),
              };
            }
          );
          insertAddOnEntriesAfterSection('mrd', mrdAddOnEntries);
        }

        // Add EMR add-ons right after EMR section
        if (plan.addOnFeatures.EMR?.length) {
          const emrAddOnEntries: PlanRow[] = plan.addOnFeatures.EMR.map(
            (feature) => {
              const featureValue = resolveFeatureValue('emr', feature);
              return {
                id: `addon-emr-entry-${Date.now()}-${Math.random()}`,
                rowType: 'entry',
                section: 'emr',
                feature: featureValue || feature.featureId || '',
                monthlyAmount: feature.monthlyAmount.toString(),
                yearlyAmount: feature.yearlyAmount.toString(),
              };
            }
          );
          insertAddOnEntriesAfterSection('emr', emrAddOnEntries);
        }

        // Add Billing add-ons right after Billing section
        if (plan.addOnFeatures.Billing?.length) {
          const billingAddOnEntries: PlanRow[] = plan.addOnFeatures.Billing.map(
            (feature) => {
              const featureValue = resolveFeatureValue('billing', feature);
              return {
                id: `addon-billing-entry-${Date.now()}-${Math.random()}`,
                rowType: 'entry',
                section: 'billing',
                feature: featureValue || feature.featureId || '',
                monthlyAmount: feature.monthlyAmount.toString(),
                yearlyAmount: feature.yearlyAmount.toString(),
              };
            }
          );
          insertAddOnEntriesAfterSection('billing', billingAddOnEntries);
        }
      }

      setAddOnRows(newAddOnRows);
      setRowErrors({});
      setAddOnRowErrors({});
      setPlanLoaded(true);
    },
    [resolveFeatureValue]
  );

  // Fetch and populate plan data
  useEffect(() => {
    if (!isOpen || !planId || !featuresLoaded) return;

    resetFormState();

    const fetchPlan = async () => {
      setLoadingPlan(true);
      try {
        const plan = await getSubscriptionPlanById(planId);
        populateForm(plan);
      } catch (err: unknown) {
        // Skip toast for cancelled requests
        if (isCancelledRequestError(err)) {
          setLoadingPlan(false);
          return;
        }
        const errorMessage =
          (err as { response?: { data?: { message?: string } } })?.response
            ?.data?.message ||
          (err as { message?: string })?.message ||
          'Failed to load plan';
        showError('Error', errorMessage);
        onClose();
      } finally {
        setLoadingPlan(false);
      }
    };

    fetchPlan();
  }, [
    isOpen,
    planId,
    populateForm,
    showError,
    onClose,
    featuresLoaded,
    resetFormState,
  ]);

  useEffect(() => {
    setRowErrors({});
    setAddOnRowErrors({});
  }, [validity]);

  useEffect(() => {
    if (!addOnEnabled) {
      setAddOnRowErrors({});
    }
  }, [addOnEnabled]);

  const addEntryForSection = (section: SectionKey) => {
    const newRow: PlanRow = {
      id: `${section}-entry-${Date.now()}`,
      rowType: 'entry',
      section,
      feature: '',
      monthlyAmount: '',
      yearlyAmount: '',
    };

    const sectionIndex = rows.findIndex(
      (r) => r.rowType === 'section' && r.section === section
    );
    if (sectionIndex === -1) {
      setRows((prev) => [...prev, newRow]);
      return;
    }

    let insertAt = rows.length;
    for (let i = sectionIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i];
      if (nextRow && nextRow.rowType === 'section') {
        insertAt = i;
        break;
      }
    }
    const next = [...rows.slice(0, insertAt), newRow, ...rows.slice(insertAt)];
    setRows(next);
  };

  const removeRow = (id: string) => {
    // Find the row being removed to check if it's a parent
    const rowToRemove = rows.find((r) => r.id === id);
    let childRowIds: string[] = [];

    if (
      rowToRemove &&
      rowToRemove.rowType === 'entry' &&
      rowToRemove.section === 'emr' &&
      rowToRemove.feature
    ) {
      // Find the parent in hierarchical options
      const parentOption = hierarchicalEmrOptions.find(
        (opt) =>
          opt.value === rowToRemove.feature &&
          opt.children &&
          opt.children.length > 0
      );

      if (parentOption && parentOption.children) {
        // Get all child feature values
        const childValues = new Set(
          parentOption.children.map((child: { value: string }) => child.value)
        );

        // Find all child rows
        childRowIds = rows
          .filter(
            (r) =>
              r.rowType === 'entry' &&
              r.section === 'emr' &&
              r.feature &&
              childValues.has(r.feature)
          )
          .map((r) => r.id);
      }
    }

    // Remove the row and all child rows
    setRows((prev) => {
      const idsToRemove = new Set([id, ...childRowIds]);
      return prev.filter((r) => !idsToRemove.has(r.id));
    });

    // Remove errors for all removed rows
    removeRowError(setRowErrors, id);
    childRowIds.forEach((childId) => {
      removeRowError(setRowErrors, childId);
    });
  };

  const addOnAddEntryForSection = (section: SectionKey) => {
    const newRow: PlanRow = {
      id: `addon-${section}-entry-${Date.now()}`,
      rowType: 'entry',
      section,
      feature: '',
      monthlyAmount: '',
      yearlyAmount: '',
    };
    const sectionIndex = addOnRows.findIndex(
      (r) => r.rowType === 'section' && r.section === section
    );
    if (sectionIndex === -1) {
      setAddOnRows((prev) => [...prev, newRow]);
      return;
    }
    let insertAt = addOnRows.length;
    for (let i = sectionIndex + 1; i < addOnRows.length; i++) {
      const nextRow = addOnRows[i];
      if (nextRow && nextRow.rowType === 'section') {
        insertAt = i;
        break;
      }
    }
    const next = [
      ...addOnRows.slice(0, insertAt),
      newRow,
      ...addOnRows.slice(insertAt),
    ];
    setAddOnRows(next);
  };

  const addOnRemoveRow = (id: string) => {
    // Find the row being removed to check if it's a parent
    const rowToRemove = addOnRows.find((r) => r.id === id);
    let childRowIds: string[] = [];

    if (
      rowToRemove &&
      rowToRemove.rowType === 'entry' &&
      rowToRemove.section === 'emr' &&
      rowToRemove.feature
    ) {
      // Find the parent in hierarchical options
      const parentOption = hierarchicalEmrOptions.find(
        (opt) =>
          opt.value === rowToRemove.feature &&
          opt.children &&
          opt.children.length > 0
      );

      if (parentOption && parentOption.children) {
        // Get all child feature values
        const childValues = new Set(
          parentOption.children.map((child: { value: string }) => child.value)
        );

        // Find all child rows
        childRowIds = addOnRows
          .filter(
            (r) =>
              r.rowType === 'entry' &&
              r.section === 'emr' &&
              r.feature &&
              childValues.has(r.feature)
          )
          .map((r) => r.id);
      }
    }

    // Remove the row and all child rows
    setAddOnRows((prev) => {
      const idsToRemove = new Set([id, ...childRowIds]);
      return prev.filter((r) => !idsToRemove.has(r.id));
    });

    // Remove errors for all removed rows
    removeRowError(setAddOnRowErrors, id);
    childRowIds.forEach((childId) => {
      removeRowError(setAddOnRowErrors, childId);
    });
  };

  const addOnSetRowValue = (
    id: string,
    updates: Partial<
      Pick<PlanRow, 'feature' | 'monthlyAmount' | 'yearlyAmount'>
    >
  ) => {
    if ('monthlyAmount' in updates) {
      const value = updates.monthlyAmount ?? '';
      if (hasValue(value)) {
        clearFieldError(setAddOnRowErrors, id, 'monthly');
      }
    }
    if ('yearlyAmount' in updates) {
      const value = updates.yearlyAmount ?? '';
      if (hasValue(value)) {
        clearFieldError(setAddOnRowErrors, id, 'yearly');
      }
    }
    setAddOnRows((prev) =>
      prev.map((r) => (r.id === id ? { ...r, ...updates } : r))
    );
  };

  const setRowValue = (
    id: string,
    updates: Partial<
      Pick<PlanRow, 'feature' | 'monthlyAmount' | 'yearlyAmount'>
    >
  ) => {
    if ('monthlyAmount' in updates) {
      const value = updates.monthlyAmount ?? '';
      if (hasValue(value)) {
        clearFieldError(setRowErrors, id, 'monthly');
      }
    }
    if ('yearlyAmount' in updates) {
      const value = updates.yearlyAmount ?? '';
      if (hasValue(value)) {
        clearFieldError(setRowErrors, id, 'yearly');
      }
    }
    setRows((prev) =>
      prev.map((r) => (r.id === id ? { ...r, ...updates } : r))
    );
  };

  const sectionHasEntries = (section: SectionKey) =>
    rows.some((r) => r.rowType === 'entry' && r.section === section);

  const expandEmrWithAllFeatures = (sourceRow: PlanRow) => {
    if (sourceRow.section !== 'emr') {
      return;
    }

    // Collect all features (parents and children) from hierarchical options
    const allFeatureValues: string[] = [];

    hierarchicalEmrOptions.forEach((option) => {
      if (option.value === ALL_FEATURE_OPTION_VALUE) {
        return; // Skip the "All" option itself
      }

      // Add parent feature
      allFeatureValues.push(option.value);

      // Add all children if they exist
      if (option.children && option.children.length > 0) {
        option.children.forEach((child: { value: string }) => {
          allFeatureValues.push(child.value);
        });
      }
    });

    if (!allFeatureValues.length) {
      setRowValue(sourceRow.id, { feature: '' });
      return;
    }

    const timestamp = Date.now();
    const monthlyAmount = sourceRow.monthlyAmount || '';
    const yearlyAmount = sourceRow.yearlyAmount || '';

    setRows((prev) => {
      const next: PlanRow[] = [];
      let inserted = false;
      const newEntries = allFeatureValues.map((featureValue, index) => ({
        id: `${sourceRow.section}-entry-${timestamp}-${index}`,
        rowType: 'entry' as const,
        section: sourceRow.section,
        feature: featureValue,
        monthlyAmount,
        yearlyAmount,
      }));

      prev.forEach((row) => {
        if (row.section === sourceRow.section && row.rowType === 'entry') {
          if (!inserted) {
            inserted = true;
            next.push(...newEntries);
          }
          return;
        }
        next.push(row);
      });

      if (!inserted) {
        const sectionIndex = next.findIndex(
          (row) =>
            row.rowType === 'section' && row.section === sourceRow.section
        );
        if (sectionIndex === -1) {
          return [...next, ...newEntries];
        }
        const copy = [...next];
        copy.splice(sectionIndex + 1, 0, ...newEntries);
        return copy;
      }

      return next;
    });
  };

  const expandSectionWithAllFeatures = (sourceRow: PlanRow) => {
    if (sourceRow.section === 'admin') {
      return;
    }

    const availableFeatures =
      featureOptions[sourceRow.section]?.filter(
        (option) => option.value !== ALL_FEATURE_OPTION_VALUE
      ) ?? [];

    if (!availableFeatures.length) {
      setRowValue(sourceRow.id, { feature: '' });
      return;
    }

    const timestamp = Date.now();
    const monthlyAmount = sourceRow.monthlyAmount || '';
    const yearlyAmount = sourceRow.yearlyAmount || '';

    setRows((prev) => {
      const next: PlanRow[] = [];
      let inserted = false;
      const newEntries = availableFeatures.map((option, index) => ({
        id: `${sourceRow.section}-entry-${timestamp}-${index}`,
        rowType: 'entry' as const,
        section: sourceRow.section,
        feature: option.value,
        monthlyAmount,
        yearlyAmount,
      }));

      prev.forEach((row) => {
        if (row.section === sourceRow.section && row.rowType === 'entry') {
          if (!inserted) {
            inserted = true;
            next.push(...newEntries);
          }
          return;
        }
        next.push(row);
      });

      if (!inserted) {
        const sectionIndex = next.findIndex(
          (row) =>
            row.rowType === 'section' && row.section === sourceRow.section
        );
        if (sectionIndex === -1) {
          return [...next, ...newEntries];
        }
        const copy = [...next];
        copy.splice(sectionIndex + 1, 0, ...newEntries);
        return copy;
      }

      return next;
    });
  };

  const handlePlanFeatureChange = (row: PlanRow, nextFeature: string) => {
    if (row.section === 'emr' && nextFeature === ALL_FEATURE_OPTION_VALUE) {
      expandEmrWithAllFeatures(row);
      return;
    }
    if (row.section !== 'admin' && nextFeature === ALL_FEATURE_OPTION_VALUE) {
      expandSectionWithAllFeatures(row);
      return;
    }
    if (nextFeature) {
      setPlanFeatureError('');
    }
    setRowValue(row.id, { feature: nextFeature });
  };

  const handlePlanFeatureMultipleChange = (
    row: PlanRow,
    selectedValues: string[]
  ) => {
    if (selectedValues.length === 0) return;
    const firstValue = selectedValues[0];
    if (!firstValue) return;

    // Check if current row's feature is a parent with children
    const isCurrentRowParent =
      row.section === 'emr' &&
      hierarchicalEmrOptions.some(
        (opt) =>
          opt.value === row.feature && opt.children && opt.children.length > 0
      );

    // If current row is a parent, always create a new row for the child
    // Otherwise, update the current row if it's a single selection
    if (selectedValues.length === 1 && !isCurrentRowParent) {
      // Single selection and not a parent, just update the current row
      handlePlanFeatureChange(row, firstValue);
      return;
    }

    // Create new row(s) for the selection(s)
    const monthlyAmount = row.monthlyAmount || '';
    const yearlyAmount = row.yearlyAmount || '';
    const timestamp = Date.now();

    setRows((prev) => {
      const newRows: PlanRow[] = [];
      let currentRowUpdated = false;

      const existingFeatures = new Set(
        prev
          .filter((r) => r.rowType === 'entry' && r.section === row.section)
          .map((r) => r.feature)
          .filter((f): f is string => Boolean(f))
      );

      let valuesToAdd: string[] = [];
      if (isCurrentRowParent) {
        valuesToAdd = selectedValues;
      } else {
        const hasChild = selectedValues.some((value) =>
          hierarchicalEmrOptions.some((opt) =>
            opt.children?.some((child) => child.value === value)
          )
        );

        if (hasChild && selectedValues.length > 1) {
          valuesToAdd = selectedValues.filter(
            (value) => !existingFeatures.has(value)
          );
        } else {
          valuesToAdd = selectedValues.slice(1);
        }
      }

      prev.forEach((r) => {
        if (r.id === row.id && !currentRowUpdated) {
          const hasChild = selectedValues.some((value) =>
            hierarchicalEmrOptions.some((opt) =>
              opt.children?.some((child) => child.value === value)
            )
          );

          if (isCurrentRowParent) {
            newRows.push(r);
          } else if (hasChild && selectedValues.length > 1) {
            const isCurrentRowEmpty = !r.feature || r.feature.trim() === '';
            const parentValue = selectedValues[0];
            const childValue = selectedValues[1];
            const parentExists =
              parentValue && existingFeatures.has(parentValue);

            if (isCurrentRowEmpty) {
              if (parentValue && !parentExists) {
                newRows.push({
                  ...r,
                  feature: parentValue,
                });

                valuesToAdd = valuesToAdd.filter(
                  (value) => value !== parentValue
                );
              } else if (childValue && parentExists) {
                newRows.push({
                  ...r,
                  feature: childValue,
                });

                valuesToAdd = valuesToAdd.filter(
                  (value) => value !== childValue
                );
              } else {
                newRows.push(r);
              }
            } else {
              newRows.push(r);
            }
          } else {
            newRows.push({
              ...r,
              feature: firstValue,
            });
          }
          currentRowUpdated = true;

          valuesToAdd.forEach((value, index) => {
            if (value && value.trim() !== '' && !existingFeatures.has(value)) {
              newRows.push({
                id: `${row.section}-entry-${timestamp}-${index}`,
                rowType: 'entry',
                section: row.section,
                feature: value,
                monthlyAmount,
                yearlyAmount,
              });
            }
          });
        } else {
          newRows.push(r);
        }
      });

      return newRows;
    });

    if (selectedValues.length > 0) {
      setPlanFeatureError('');
    }
  };

  const handleAddOnFeatureMultipleChange = (
    row: PlanRow,
    selectedValues: string[]
  ) => {
    if (selectedValues.length === 0) return;
    const firstValue = selectedValues[0];
    if (!firstValue) return;

    const isCurrentRowParent =
      row.section === 'emr' &&
      hierarchicalEmrOptions.some(
        (opt) =>
          opt.value === row.feature && opt.children && opt.children.length > 0
      );

    if (selectedValues.length === 1 && !isCurrentRowParent) {
      addOnSetRowValue(row.id, { feature: firstValue });
      return;
    }

    const monthlyAmount = row.monthlyAmount || '';
    const yearlyAmount = row.yearlyAmount || '';
    const timestamp = Date.now();

    setAddOnRows((prev) => {
      const newRows: PlanRow[] = [];
      let currentRowUpdated = false;

      const existingFeatures = new Set(
        prev
          .filter((r) => r.rowType === 'entry' && r.section === row.section)
          .map((r) => r.feature)
          .filter((f): f is string => Boolean(f))
      );

      const baseFeatures = new Set(
        rows
          .filter((r) => r.rowType === 'entry' && r.section === row.section)
          .map((r) => r.feature)
          .filter((f): f is string => Boolean(f))
      );

      const getParentForChild = (childValue: string): string | null => {
        for (const opt of hierarchicalEmrOptions) {
          if (opt.children?.some((child) => child.value === childValue)) {
            return opt.value;
          }
        }
        return null;
      };

      const filteredSelectedValues = selectedValues.filter((value) => {
        const parent = getParentForChild(value);

        if (parent) {
          return true;
        } else {
          return !baseFeatures.has(value);
        }
      });

      let valuesToAdd: string[] = [];
      let valueForCurrentRow: string | null = null;

      if (isCurrentRowParent) {
        valuesToAdd = filteredSelectedValues.filter(
          (value) => !existingFeatures.has(value)
        );
      } else {
        const hasChild = selectedValues.some((value) =>
          hierarchicalEmrOptions.some((opt) =>
            opt.children?.some((child) => child.value === value)
          )
        );

        if (hasChild && selectedValues.length > 1) {
          const firstValueIsParent = !getParentForChild(firstValue);
          if (firstValueIsParent && baseFeatures.has(firstValue)) {
            const childValue = selectedValues.find((value) =>
              hierarchicalEmrOptions.some((opt) =>
                opt.children?.some((child) => child.value === value)
              )
            );
            if (childValue) {
              valueForCurrentRow = childValue;

              valuesToAdd = [];
            } else {
              valuesToAdd = filteredSelectedValues.filter(
                (value) => !existingFeatures.has(value)
              );
            }
          } else {
            const firstFiltered = filteredSelectedValues[0];
            if (firstFiltered && !baseFeatures.has(firstFiltered)) {
              valueForCurrentRow = firstFiltered;

              valuesToAdd = filteredSelectedValues
                .slice(1)
                .filter(
                  (value) =>
                    !existingFeatures.has(value) && value !== firstFiltered
                );
            } else {
              valuesToAdd = filteredSelectedValues.filter(
                (value) => !existingFeatures.has(value)
              );
            }
          }
        } else {
          const firstValueParent = getParentForChild(firstValue);
          if (!firstValueParent || !baseFeatures.has(firstValue)) {
            valueForCurrentRow = firstValue;

            valuesToAdd = [];
          } else {
            valuesToAdd = filteredSelectedValues.filter(
              (value) => !existingFeatures.has(value)
            );
          }
        }
      }

      prev.forEach((r) => {
        if (r.id === row.id && !currentRowUpdated) {
          const isCurrentRowEmpty = !r.feature || r.feature.trim() === '';

          if (isCurrentRowParent) {
            newRows.push(r);
          } else if (valueForCurrentRow && (isCurrentRowEmpty || !r.feature)) {
            newRows.push({
              ...r,
              feature: valueForCurrentRow,
            });
          } else {
            newRows.push(r);
          }
          currentRowUpdated = true;

          valuesToAdd.forEach((value, index) => {
            if (value && value.trim() !== '' && !existingFeatures.has(value)) {
              newRows.push({
                id: `addon-${row.section}-entry-${timestamp}-${index}`,
                rowType: 'entry',
                section: row.section,
                feature: value,
                monthlyAmount,
                yearlyAmount,
              });
            }
          });
        } else {
          newRows.push(r);
        }
      });

      return newRows;
    });
  };

  const handleImageUpload = useCallback(() => {
    const editor = quillRef.current?.getEditor?.();
    if (!editor) return;
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.onchange = () => {
      const file = input.files?.[0];
      if (!file) {
        input.remove();
        return;
      }
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result;
        if (!result || typeof result !== 'string') {
          return;
        }
        const range = editor.getSelection(true);
        const index = range ? range.index : editor.getLength();
        editor.insertEmbed(index, 'image', result, 'user');
        editor.setSelection(index + 1, 0, 'user');
      };
      reader.readAsDataURL(file);
      input.value = '';
    };
    input.click();
  }, []);

  const quillModules = useMemo(
    () => ({
      ...defaultQuillModules,
      toolbar: {
        container: defaultQuillModules.toolbar,
        handlers: {
          image: handleImageUpload,
        },
      },
    }),
    [handleImageUpload]
  );

  useEffect(() => {
    if (!isOpen) return;

    let attempts = 0;
    const applyToolbarLabels = () => {
      const toolbar = (
        quillRef.current?.getEditor?.()?.getModule('toolbar') as {
          container?: HTMLElement;
        } | null
      )?.container;
      if (!toolbar) {
        if (attempts < 5) {
          attempts += 1;
          setTimeout(applyToolbarLabels, 60);
        }
        return;
      }

      // Add tooltips for size picker
      toolbar
        .querySelectorAll<HTMLElement>(
          '.ql-picker.ql-size .ql-picker-label, .ql-picker.ql-size .ql-picker-item'
        )
        .forEach((el) => {
          let value = el.getAttribute('data-value');
          if (!value && el.classList.contains('ql-picker-label')) {
            value =
              toolbar
                .querySelector<HTMLElement>(
                  '.ql-picker.ql-size .ql-picker-item.ql-selected'
                )
                ?.getAttribute('data-value') || null;
          }
          if (!value) return;
          const numeric = value.endsWith('px')
            ? value.replace('px', '')
            : value;
          el.setAttribute('data-label', numeric);
          el.setAttribute('aria-label', `${numeric} font size`);
          el.setAttribute('title', `Font size: ${numeric}px`);
        });

      // Add tooltips for all toolbar buttons
      const tooltips: Record<string, string> = {
        'ql-bold': 'Bold',
        'ql-italic': 'Italic',
        'ql-underline': 'Underline',
        'ql-strike': 'Strikethrough',
        'ql-link': 'Insert Link',
        'ql-image': 'Insert Image',
        'ql-clean': 'Clear Formatting',
      };

      Object.entries(tooltips).forEach(([className, tooltip]) => {
        const button = toolbar.querySelector<HTMLElement>(`.${className}`);
        if (button && !button.getAttribute('title')) {
          button.setAttribute('title', tooltip);
          button.setAttribute('aria-label', tooltip);
        }
      });

      // Add tooltips for list buttons (bullet and ordered list)
      let listAttempts = 0;
      const addListTooltips = () => {
        // Try multiple selectors to catch all possible button structures
        const listSelectors = [
          'button.ql-list[data-value="ordered"]',
          'button.ql-list[data-value="bullet"]',
          '.ql-list[data-value="ordered"]',
          '.ql-list[data-value="bullet"]',
          'button[class*="ql-list"][data-value="ordered"]',
          'button[class*="ql-list"][data-value="bullet"]',
        ];

        let foundOrdered = false;
        let foundBullet = false;

        listSelectors.forEach((selector) => {
          const buttons = toolbar.querySelectorAll<HTMLElement>(selector);
          buttons.forEach((button) => {
            const value = button.getAttribute('data-value');
            if (value === 'ordered' && !button.getAttribute('title')) {
              button.setAttribute('title', 'Numbered List');
              button.setAttribute('aria-label', 'Numbered List');
              foundOrdered = true;
            } else if (value === 'bullet' && !button.getAttribute('title')) {
              button.setAttribute('title', 'Bullet List');
              button.setAttribute('aria-label', 'Bullet List');
              foundBullet = true;
            }
          });
        });

        // Also try finding by iterating through all buttons
        if (!foundOrdered || !foundBullet) {
          const allButtons = toolbar.querySelectorAll<HTMLElement>('button');
          allButtons.forEach((button) => {
            if (button.classList.contains('ql-list')) {
              const value = button.getAttribute('data-value');
              if (value === 'ordered' && !button.getAttribute('title')) {
                button.setAttribute('title', 'Numbered List');
                button.setAttribute('aria-label', 'Numbered List');
                foundOrdered = true;
              } else if (value === 'bullet' && !button.getAttribute('title')) {
                button.setAttribute('title', 'Bullet List');
                button.setAttribute('aria-label', 'Bullet List');
                foundBullet = true;
              }
            }
          });
        }

        // If buttons still not found, retry after a short delay
        if ((!foundOrdered || !foundBullet) && listAttempts < 10) {
          listAttempts += 1;
          setTimeout(addListTooltips, 100);
        }
      };

      addListTooltips();

      // Add tooltips for pickers
      const pickerTooltips: Record<string, string> = {
        'ql-header': 'Text Heading',
        'ql-size': 'Font Size',
        'ql-color': 'Text Color',
        'ql-background': 'Background Color',
        'ql-list': 'List',
        'ql-align': 'Text Alignment',
      };

      Object.entries(pickerTooltips).forEach(([className, tooltip]) => {
        const picker = toolbar.querySelector<HTMLElement>(
          `.ql-picker.${className}`
        );
        if (picker) {
          const label = picker.querySelector<HTMLElement>('.ql-picker-label');
          if (label && !label.getAttribute('title')) {
            label.setAttribute('title', tooltip);
            label.setAttribute('aria-label', tooltip);
          }
        }
      });
    };

    applyToolbarLabels();
  }, [isOpen, quillModules]);

  const showMonthly = validity === 'both' || validity === 'monthly';
  const showYearly = validity === 'both' || validity === 'yearly';

  const featureGridTemplate = useMemo(() => {
    const actionColumn = 'minmax(64px, 0.2fr)';
    if (showMonthly && showYearly) {
      return [
        'minmax(0, 1fr)',
        'minmax(140px, 0.4fr)',
        'minmax(140px, 0.4fr)',
        actionColumn,
      ].join(' ');
    }
    return ['minmax(0, 1fr)', 'minmax(140px, 0.4fr)', actionColumn].join(' ');
  }, [showMonthly, showYearly]);

  const primaryAmountLabel = useMemo(() => {
    if (showMonthly) {
      return 'Monthly Amount (₹)';
    }
    if (showYearly) {
      return 'Yearly Amount (₹)';
    }
    return '';
  }, [showMonthly, showYearly]);

  const secondaryAmountLabel = useMemo(
    () => (showMonthly && showYearly ? 'Yearly Amount (₹)' : ''),
    [showMonthly, showYearly]
  );

  const totals = useMemo(() => {
    const baseMonthly = rows
      .filter((r) => r.rowType === 'entry')
      .reduce((sum, r) => sum + (parseFloat(r.monthlyAmount || '0') || 0), 0);
    const baseYearly = rows
      .filter((r) => r.rowType === 'entry')
      .reduce((sum, r) => sum + (parseFloat(r.yearlyAmount || '0') || 0), 0);

    // Don't include addon amounts in total
    return {
      monthly: baseMonthly,
      yearly: baseYearly,
    };
  }, [rows]);

  const addOnTotals = useMemo(() => {
    const addOnMonthly = addOnRows
      .filter((r) => r.rowType === 'entry')
      .reduce((sum, r) => sum + (parseFloat(r.monthlyAmount || '0') || 0), 0);
    const addOnYearly = addOnRows
      .filter((r) => r.rowType === 'entry')
      .reduce((sum, r) => sum + (parseFloat(r.yearlyAmount || '0') || 0), 0);

    return {
      monthly: addOnMonthly,
      yearly: addOnYearly,
    };
  }, [addOnRows]);

  const validateAmountRows = (
    entries: PlanRow[],
    context: 'plan' | 'add-on'
  ) => {
    if (!entries.length) {
      if (context === 'plan') {
        setRowErrors({});
      } else {
        setAddOnRowErrors({});
      }
      return true;
    }

    const requiresMonthly = validity === 'both' || validity === 'monthly';
    const requiresYearly = validity === 'both' || validity === 'yearly';

    const missingRows: RowErrorState = {};
    let missingMonthly = false;
    let missingYearly = false;

    entries.forEach((row) => {
      const rowFieldErrors: RowFieldErrors = {};

      if (requiresMonthly && !hasValue(row.monthlyAmount)) {
        rowFieldErrors.monthly = true;
        missingMonthly = true;
      }

      if (requiresYearly && !hasValue(row.yearlyAmount)) {
        rowFieldErrors.yearly = true;
        missingYearly = true;
      }

      if (Object.keys(rowFieldErrors).length > 0) {
        missingRows[row.id] = rowFieldErrors;
      }
    });

    if (context === 'plan') {
      setRowErrors(missingRows);
    } else {
      setAddOnRowErrors(missingRows);
    }

    return !(missingMonthly || missingYearly);
  };

  // Transform form data to API payload format
  const transformToApiPayload = (): UpdateSubscriptionPlanRequest => {
    const cleanedDescription = normalizeListFormatting(description);

    const sectionMap: Record<
      Exclude<SectionKey, 'admin'>,
      'MRD' | 'EMR' | 'Billing'
    > = {
      mrd: 'MRD',
      emr: 'EMR',
      billing: 'Billing',
    };

    const buildFeaturePayload = (
      planRows: PlanRow[]
    ): SubscriptionPlanFeaturesPayload => {
      const result: Record<
        'MRD' | 'EMR' | 'Billing',
        Map<
          string,
          {
            monthlyAmount: number;
            yearlyAmount: number;
          }
        >
      > = {
        MRD: new Map(),
        EMR: new Map(),
        Billing: new Map(),
      };

      const requiresMonthly = validity === 'monthly' || validity === 'both';
      const requiresYearly = validity === 'yearly' || validity === 'both';

      planRows
        .filter((r) => r.rowType === 'entry' && r.section !== 'admin')
        .forEach((row) => {
          if (!row.feature) {
            return;
          }

          const monthlyValid = !requiresMonthly || !!row.monthlyAmount;
          const yearlyValid = !requiresYearly || !!row.yearlyAmount;

          if (!monthlyValid || !yearlyValid) {
            return;
          }

          const sectionKey =
            sectionMap[row.section as Exclude<SectionKey, 'admin'>];
          if (!sectionKey) {
            return;
          }

          const monthlyAmount = parseFloat(row.monthlyAmount || '0') || 0;
          const yearlyAmount = parseFloat(row.yearlyAmount || '0') || 0;

          const availableFeatures =
            featureOptions[row.section]?.filter(
              (option) => option.value !== ALL_FEATURE_OPTION_VALUE
            ) ?? [];

          const featureIds =
            row.feature === ALL_FEATURE_OPTION_VALUE
              ? availableFeatures.map((option) => option.value)
              : [row.feature];

          featureIds.forEach((featureId) => {
            if (!featureId) return;
            result[sectionKey].set(featureId, {
              monthlyAmount,
              yearlyAmount,
            });
          });
        });

      const convertMapToPayload = (
        entryMap: Map<
          string,
          {
            monthlyAmount: number;
            yearlyAmount: number;
          }
        >
      ) =>
        Array.from(entryMap.entries()).map(([featureId, amount]) => ({
          featureId,
          monthlyAmount: amount.monthlyAmount,
          yearlyAmount: amount.yearlyAmount,
        }));

      return {
        MRD: convertMapToPayload(result.MRD),
        EMR: convertMapToPayload(result.EMR),
        Billing: convertMapToPayload(result.Billing),
      };
    };

    const features = buildFeaturePayload(rows);
    const addOnFeatures = buildFeaturePayload(addOnRows);

    const validityMap: Record<
      'both' | 'monthly' | 'yearly',
      'Both' | 'Monthly' | 'Yearly'
    > = {
      both: 'Both',
      monthly: 'Monthly',
      yearly: 'Yearly',
    };

    return {
      planName: planName.trim(),
      description: cleanedDescription,
      validity: validityMap[validity],
      features,
      addOnFeatures,
      isActive,
    };
  };

  const handleSave = async () => {
    if (!planId) return;

    let hasBlockingError = false;
    if (!planName.trim()) {
      setPlanNameError('Plan name is required');
      hasBlockingError = true;
    } else if (!/[a-zA-Z]/.test(planName.trim())) {
      setPlanNameError('Plan name must contain at least one letter');
      hasBlockingError = true;
    } else {
      setPlanNameError('');
    }

    const planFeatureEntries = rows.filter(
      (r) => r.rowType === 'entry' && r.section !== 'admin' && r.feature
    );
    if (!planFeatureEntries.length) {
      setPlanFeatureError('Add at least one feature to the plan');
      hasBlockingError = true;
    } else {
      setPlanFeatureError('');
    }
    if (!validateAmountRows(planFeatureEntries, 'plan')) {
      hasBlockingError = true;
    }

    const addOnFeatureEntries = addOnRows.filter(
      (r) => r.rowType === 'entry' && r.section !== 'admin' && r.feature
    );
    if (!validateAmountRows(addOnFeatureEntries, 'add-on')) {
      hasBlockingError = true;
    }

    if (hasBlockingError) {
      return;
    }

    setIsLoading(true);

    try {
      const payload = transformToApiPayload();
      const response = await updateSubscriptionPlan(planId, payload);

      // If request succeeds (no exception), treat as success
      // UPDATE endpoints may return 200 without a success field
      success('Success', response?.message || 'Plan updated successfully');
      setDescription(payload.description || '');
      onPlanUpdated?.();
      onClose();
    } catch (err: unknown) {
      // Skip toast for cancelled requests
      if (isCancelledRequestError(err)) {
        setIsLoading(false);
        return;
      }
      const axiosError = err as {
        response?: {
          data?:
            | string
            | {
                message?: string;
                error?: string;
                errors?: string | string[] | { message?: string };
              };
          status?: number;
          statusText?: string;
        };
        message?: string;
      };

      let errorMessage: string | undefined;

      if (axiosError.response?.data) {
        if (typeof axiosError.response.data === 'string') {
          errorMessage = axiosError.response.data;
        } else if (
          typeof axiosError.response.data === 'object' &&
          axiosError.response.data !== null &&
          'message' in axiosError.response.data
        ) {
          errorMessage = (axiosError.response.data as { message?: string })
            .message;
        } else if (
          typeof axiosError.response.data === 'object' &&
          axiosError.response.data !== null &&
          'error' in axiosError.response.data
        ) {
          const errorData = (axiosError.response.data as { error?: unknown })
            .error;
          errorMessage = typeof errorData === 'string' ? errorData : undefined;
        } else if (
          typeof axiosError.response.data === 'object' &&
          axiosError.response.data !== null &&
          'errors' in axiosError.response.data
        ) {
          const errors = (
            axiosError.response.data as {
              errors?: unknown;
            }
          ).errors;
          if (typeof errors === 'string') {
            errorMessage = errors;
          } else if (Array.isArray(errors) && errors.length > 0) {
            errorMessage =
              typeof errors[0] === 'string' ? errors[0] : String(errors[0]);
          } else if (
            typeof errors === 'object' &&
            errors !== null &&
            'message' in errors
          ) {
            errorMessage = (errors as { message?: string }).message;
          }
        }
      }

      if (!errorMessage) {
        errorMessage =
          axiosError.message || 'An error occurred while updating the plan';
      }

      showError('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        if (!isLoading) {
          onClose();
        }
      }}
      maxWidth='md'
      fullWidth
      scroll='body'
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mt: 1,
          pr: 3,
          pl: 3,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <span style={{ fontWeight: 600, fontSize: 20 }}>Edit Plan</span>
        <IconButton onClick={onClose} size='small' disabled={isLoading}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          maxHeight: 'calc(100vh - 250px)',
          overflowY: 'auto',
          paddingTop: 3,
          paddingBottom: 2,
          pr: 3,
          pl: 3,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
        }}
      >
        <div
          className='space-y-4 font-sans flex-1'
          style={{
            opacity: isInitialLoading ? 0.4 : 1,
            pointerEvents: isInitialLoading ? 'none' : 'auto',
            transition: 'opacity 0.2s ease',
          }}
        >
          <div className='mt-3 grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='space-y-4'>
              <div className='space-y-1'>
                <label className='block text-sm font-medium text-gray-700 font-sans'>
                  Plan Name *
                </label>
                <TextInput
                  placeholder='Enter Plan Name'
                  value={planName}
                  onChange={(e) => handlePlanNameChange(e.target.value)}
                  className='mt-1'
                  error={Boolean(planNameError)}
                  helperText={planNameError || undefined}
                  inputProps={{ 'aria-label': 'Plan Name' }}
                />
              </div>
              <div className='space-y-1'>
                <label className='block text-sm font-medium text-gray-700 font-sans'>
                  Validity *
                </label>
                <MUISelect
                  placeholder='Select validity'
                  value={validity}
                  onChange={(e) =>
                    setValidity(e.target.value as 'both' | 'monthly' | 'yearly')
                  }
                  options={[
                    { label: 'Both', value: 'both' },
                    { label: 'Monthly', value: 'monthly' },
                    { label: 'Yearly', value: 'yearly' },
                  ]}
                  inputProps={{ 'aria-label': 'Validity' }}
                />
              </div>
            </div>
            <div className='md:row-span-2 mt-1 space-y-1'>
              <label className='block text-sm font-medium text-gray-700 font-sans'>
                Description
              </label>
              <div className='quill-wrapper' style={{ height: '200px' }}>
                <ReactQuill
                  ref={quillRef}
                  theme='snow'
                  value={description}
                  onChange={(value) => {
                    setDescription(value);
                    const editorInstance = quillRef.current?.getEditor?.();
                    if (editorInstance) {
                      applyListItemStyles(editorInstance.root);
                    }
                  }}
                  placeholder='Enter description'
                  readOnly={isLoading || isInitialLoading}
                  modules={quillModules}
                  formats={defaultQuillFormats}
                />
              </div>
              <style>{`
                    .quill-wrapper .ql-toolbar.ql-snow {
                      display: flex;
                      flex-wrap: wrap;
                      gap: 3px;
                      padding: 4px 6px;
                      row-gap: 4px;
                    }
                    .quill-wrapper .ql-toolbar.ql-snow .ql-formats {
                      margin: 0;
                      display: flex;
                      align-items: center;
                      gap: 3px;
                    }
                    .quill-wrapper .ql-toolbar.ql-snow .ql-picker,
                    .quill-wrapper .ql-toolbar.ql-snow button {
                      height: 24px;
                    }
                    .quill-wrapper .ql-toolbar.ql-snow .ql-picker {
                      min-width: auto;
                      border: 1px solid rgba(15, 23, 42, 0.15);
                      border-radius: 4px;
                      display: flex;
                      align-items: center;
                    }
                    .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-header .ql-picker-label,
                    .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-header .ql-picker-item {
                      padding: 0;
                      border-radius: 4px;
                    }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size .ql-picker-label,
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size .ql-picker-item {
                      line-height: 24px;
                      padding: 0 2px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      column-gap: 1px;
                    }
                    .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size {
                      margin-left: 1px;
                    }
                    .quill-wrapper .ql-container {
                      height: calc(200px - 52px) !important;
                    }
                    .quill-wrapper .ql-editor {
                      min-height: calc(200px - 60px) !important;
                    }
                    .quill-wrapper .ql-editor ul,
                    .quill-wrapper .ql-editor ol {
                      margin: 0.5rem 0;
                      padding-left: 1.25rem;
                      list-style-position: outside;
                    }
                    .quill-wrapper .ql-editor ul {
                      list-style-type: disc;
                    }
                    .quill-wrapper .ql-editor ol {
                      list-style-type: decimal;
                    }
                    .quill-wrapper .ql-editor ul li,
                    .quill-wrapper .ql-editor ol li {
                      list-style-type: inherit;
                      margin-bottom: 0.25rem;
                    }
                    .quill-wrapper .ql-editor ul li::before,
                    .quill-wrapper .ql-editor ol li::before {
                      content: none;
                    }
                    .quill-wrapper .ql-editor ul li::marker,
                    .quill-wrapper .ql-editor ol li::marker {
                      color: inherit;
                      font-size: inherit;
                      font-weight: inherit;
                    }
                    .quill-wrapper .ql-editor p {
                      margin: 0 0 0.5rem 0;
                    }
                    .quill-wrapper .ql-toolbar .ql-direction,
                    .quill-wrapper .ql-toolbar button[aria-label*="Tx"],
                    .quill-wrapper .ql-toolbar button[title*="Tx"],
                    .quill-wrapper .ql-toolbar .ql-picker[data-label*="Tx"] {
                      display: none !important;
                    }
                    .quill-wrapper .ql-editor img {
                      cursor: pointer;
                      user-select: auto;
                      -webkit-user-select: auto;
                      -moz-user-select: auto;
                      -ms-user-select: auto;
                      display: inline-block;
                      max-width: 100%;
                      height: auto;
                      margin: 0.5rem 0;
                      border: 2px solid transparent;
                      border-radius: 4px;
                      transition: border-color 0.2s ease;
                    }
                    .quill-wrapper .ql-editor img:hover {
                      border-color: #3b82f6;
                      outline: 2px solid rgba(59, 130, 246, 0.2);
                    }
                    .quill-wrapper .ql-editor img.ql-selected,
                    .quill-wrapper .ql-editor .ql-image.ql-selected img {
                      border-color: #3b82f6;
                      outline: 2px solid rgba(59, 130, 246, 0.4);
                      background-color: rgba(59, 130, 246, 0.05);
                    }
                    .quill-wrapper .ql-editor .ql-image {
                      display: inline-block;
                      position: relative;
                    }
                  `}</style>
            </div>
          </div>

          <div className='border rounded-lg font-sans'>
            <div
              className='bg-[#0b4263] text-white text-sm font-medium px-4 py-2 rounded-t-lg grid items-center'
              style={{
                gridTemplateColumns: featureGridTemplate,
              }}
            >
              <div>Base Features</div>
              {(showMonthly || showYearly) && (
                <div className='text-center'>{primaryAmountLabel}</div>
              )}
              {showMonthly && showYearly && (
                <div className='text-center'>{secondaryAmountLabel}</div>
              )}
              <div />
            </div>
            {planFeatureError && (
              <div className='px-4 py-2 text-sm text-red-600'>
                {planFeatureError}
              </div>
            )}
            <div className='divide-y'>
              {rows.map((row) => {
                if (row.rowType === 'section') {
                  return (
                    <React.Fragment key={row.id}>
                      <div
                        className='grid items-center px-4 py-3 bg-gray-50'
                        style={{
                          gridTemplateColumns: featureGridTemplate,
                        }}
                      >
                        <div className='font-medium'>
                          {sectionLabels[row.section]}
                        </div>
                        {showMonthly && <div />}
                        {showYearly && <div />}
                        <div className='text-right'>
                          <button
                            onClick={() => addEntryForSection(row.section)}
                            className='inline-flex items-center justify-center h-7 w-7 rounded border border-gray-300 hover:bg-gray-100'
                            title='Add'
                          >
                            <Plus className='w-4 h-4' />
                          </button>
                        </div>
                      </div>
                      {!sectionHasEntries(row.section) && (
                        <div
                          className='grid items-center px-4 py-3'
                          style={{
                            gridTemplateColumns: featureGridTemplate,
                          }}
                        >
                          <div
                            style={{
                              gridColumn: '1 / -1',
                            }}
                          >
                            {isInitialLoading ? (
                              <div className='flex flex-col gap-2 animate-pulse'>
                                <div className='h-3 rounded bg-gray-200' />
                                <div className='h-3 rounded bg-gray-200 w-3/4' />
                              </div>
                            ) : (
                              <div className='text-center text-gray-500'>
                                No entry yet
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  );
                }

                return (
                  <div
                    key={row.id}
                    className='grid items-center px-4 py-3'
                    style={{
                      gridTemplateColumns: featureGridTemplate,
                    }}
                  >
                    <div className='flex items-center gap-2'>
                      {row.section === 'emr' &&
                      hierarchicalEmrOptions.length > 0 ? (
                        <HierarchicalFeatureSelect
                          placeholder={
                            isFetchingFeatures ? 'Loading...' : 'Select'
                          }
                          value={row.feature || ''}
                          onChange={(value) =>
                            handlePlanFeatureChange(row, value)
                          }
                          onMultipleChange={(values) =>
                            handlePlanFeatureMultipleChange(row, values)
                          }
                          options={hierarchicalEmrOptions.map((opt) => {
                            // Disable "All" if there are existing selections
                            if (opt.value === ALL_FEATURE_OPTION_VALUE) {
                              const existingSelections = rows.filter(
                                (r) =>
                                  r.rowType === 'entry' &&
                                  r.section === 'emr' &&
                                  r.feature &&
                                  r.id !== row.id
                              );
                              const addOnSelections = addOnRows.filter(
                                (r) =>
                                  r.rowType === 'entry' &&
                                  r.section === 'emr' &&
                                  r.feature
                              );
                              return {
                                ...opt,
                                disabled:
                                  existingSelections.length > 0 ||
                                  addOnSelections.length > 0,
                              };
                            }
                            return opt;
                          })}
                          disabled={isFetchingFeatures}
                          sx={{ minWidth: 180 }}
                          selectedFeatures={
                            new Set([
                              ...rows
                                .filter(
                                  (r) =>
                                    r.rowType === 'entry' &&
                                    r.section === 'emr' &&
                                    r.feature
                                )
                                .map((r) => r.feature)
                                .filter((f): f is string => Boolean(f)),
                              ...addOnRows
                                .filter(
                                  (r) =>
                                    r.rowType === 'entry' &&
                                    r.section === 'emr' &&
                                    r.feature
                                )
                                .map((r) => r.feature)
                                .filter((f): f is string => Boolean(f)),
                            ])
                          }
                        />
                      ) : (
                        <MUISelect
                          placeholder={
                            isFetchingFeatures ? 'Loading...' : 'Select'
                          }
                          value={row.feature || ''}
                          onChange={(e) =>
                            handlePlanFeatureChange(row, String(e.target.value))
                          }
                          options={getPlanFeatureOptions(row)}
                          disabled={isFetchingFeatures}
                          sx={{ minWidth: 180 }}
                        />
                      )}
                    </div>
                    {showMonthly && (
                      <div className='flex items-center justify-center'>
                        <TextInput
                          placeholder='XXX'
                          value={row.monthlyAmount || ''}
                          onChange={(e) =>
                            setRowValue(row.id, {
                              monthlyAmount: e.target.value,
                            })
                          }
                          type='number'
                          inputProps={{ min: 0 }}
                          error={!!rowErrors[row.id]?.monthly}
                          helperText={
                            rowErrors[row.id]?.monthly ? 'Required' : undefined
                          }
                          FormHelperTextProps={{ sx: { m: 0 } }}
                          sx={{
                            maxWidth: 140,
                            '& input[type=number]': {
                              MozAppearance: 'textfield',
                            },
                            '& input[type=number]::-webkit-outer-spin-button': {
                              WebkitAppearance: 'none',
                              margin: 0,
                            },
                            '& input[type=number]::-webkit-inner-spin-button': {
                              WebkitAppearance: 'none',
                              margin: 0,
                            },
                          }}
                        />
                      </div>
                    )}
                    {showYearly && (
                      <div className='flex items-center justify-center'>
                        <TextInput
                          placeholder='XXX'
                          value={row.yearlyAmount || ''}
                          onChange={(e) =>
                            setRowValue(row.id, {
                              yearlyAmount: e.target.value,
                            })
                          }
                          type='number'
                          inputProps={{ min: 0 }}
                          error={!!rowErrors[row.id]?.yearly}
                          helperText={
                            rowErrors[row.id]?.yearly ? 'Required' : undefined
                          }
                          FormHelperTextProps={{ sx: { m: 0 } }}
                          sx={{
                            maxWidth: 140,
                            '& input[type=number]': {
                              MozAppearance: 'textfield',
                            },
                            '& input[type=number]::-webkit-outer-spin-button': {
                              WebkitAppearance: 'none',
                              margin: 0,
                            },
                            '& input[type=number]::-webkit-inner-spin-button': {
                              WebkitAppearance: 'none',
                              margin: 0,
                            },
                          }}
                        />
                      </div>
                    )}
                    <div className='flex items-center justify-end gap-2'>
                      <button
                        onClick={() => removeRow(row.id)}
                        className='inline-flex items-center justify-center h-7 w-7 rounded border border-gray-300 hover:bg-gray-100'
                        title='Remove'
                      >
                        <X className='w-4 h-4' />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div
            className='grid items-center '
            style={{
              gridTemplateColumns: featureGridTemplate,
            }}
          >
            <div className='text-sm text-gray-700 font-medium'>
              Total Amount
            </div>
            {showMonthly && (
              <div className='text-left font-semibold px-4'>
                ₹{totals.monthly.toFixed(2)}
              </div>
            )}
            {showYearly && (
              <div className='text-left font-semibold px-4'>
                ₹{totals.yearly.toFixed(2)}
              </div>
            )}
            <div />
          </div>

          <div className='flex items-center gap-2 pt-3'>
            <FormControlLabel
              control={
                <Switch
                  checked={addOnEnabled}
                  onChange={(e) => setAddOnEnabled(e.target.checked)}
                  color='primary'
                />
              }
              label='Add On Features'
            />
          </div>

          {addOnEnabled && (
            <div className='border rounded-lg mt-3 font-sans'>
              <div
                className='bg-[#0b4263] text-white text-sm font-medium px-4 py-2 rounded-t-lg grid items-center'
                style={{
                  gridTemplateColumns: featureGridTemplate,
                }}
              >
                <div>Add On Features</div>
                {(showMonthly || showYearly) && (
                  <div className='text-center'>{primaryAmountLabel}</div>
                )}
                {showMonthly && showYearly && (
                  <div className='text-center'>{secondaryAmountLabel}</div>
                )}
                <div />
              </div>
              <div className='divide-y'>
                {addOnRows.map((row) => {
                  if (row.rowType === 'section') {
                    return (
                      <React.Fragment key={row.id}>
                        <div
                          className='grid items-center px-4 py-3 bg-gray-50'
                          style={{
                            gridTemplateColumns: featureGridTemplate,
                          }}
                        >
                          <div className='font-medium'>
                            {sectionLabels[row.section]}
                          </div>
                          {showMonthly && <div />}
                          {showYearly && <div />}
                          <div className='text-right'>
                            <button
                              onClick={() =>
                                addOnAddEntryForSection(row.section)
                              }
                              className='inline-flex items-center justify-center h-7 w-7 rounded border border-gray-300 hover:bg-gray-100'
                              title='Add'
                            >
                              <Plus className='w-4 h-4' />
                            </button>
                          </div>
                        </div>
                        {!addOnRows.some(
                          (r) =>
                            r.rowType === 'entry' && r.section === row.section
                        ) && (
                          <div
                            className='grid items-center px-4 py-3'
                            style={{
                              gridTemplateColumns: featureGridTemplate,
                            }}
                          >
                            <div
                              style={{
                                gridColumn: '1 / -1',
                              }}
                            >
                              {isInitialLoading ? (
                                <div className='flex flex-col gap-2 animate-pulse'>
                                  <div className='h-3 rounded bg-gray-200' />
                                  <div className='h-3 rounded bg-gray-200 w-3/4' />
                                </div>
                              ) : (
                                <div className='text-center text-gray-500'>
                                  No entry yet
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </React.Fragment>
                    );
                  }
                  return (
                    <div
                      key={row.id}
                      className='grid items-center px-4 py-3'
                      style={{
                        gridTemplateColumns: featureGridTemplate,
                      }}
                    >
                      <div className='flex items-center gap-2'>
                        {row.section === 'emr' &&
                        hierarchicalEmrOptions.length > 0 ? (
                          <HierarchicalFeatureSelect
                            placeholder={
                              isFetchingFeatures ? 'Loading...' : 'Select'
                            }
                            value={row.feature || ''}
                            onChange={(value) =>
                              addOnSetRowValue(row.id, { feature: value })
                            }
                            onMultipleChange={(values) =>
                              handleAddOnFeatureMultipleChange(row, values)
                            }
                            options={hierarchicalEmrOptions.map((opt) => {
                              // Disable "All" if there are existing selections
                              if (opt.value === ALL_FEATURE_OPTION_VALUE) {
                                const existingSelections = [
                                  ...rows.filter(
                                    (r) =>
                                      r.rowType === 'entry' &&
                                      r.section === 'emr' &&
                                      r.feature
                                  ),
                                  ...addOnRows.filter(
                                    (r) =>
                                      r.rowType === 'entry' &&
                                      r.section === 'emr' &&
                                      r.feature &&
                                      r.id !== row.id
                                  ),
                                ];
                                return {
                                  ...opt,
                                  disabled: existingSelections.length > 0,
                                };
                              }
                              return opt;
                            })}
                            disabled={isFetchingFeatures}
                            sx={{ minWidth: 180 }}
                            selectedFeatures={
                              new Set([
                                ...rows
                                  .filter(
                                    (r) =>
                                      r.rowType === 'entry' &&
                                      r.section === 'emr' &&
                                      r.feature
                                  )
                                  .map((r) => r.feature)
                                  .filter((f): f is string => Boolean(f)),
                                ...addOnRows
                                  .filter(
                                    (r) =>
                                      r.rowType === 'entry' &&
                                      r.section === 'emr' &&
                                      r.feature
                                  )
                                  .map((r) => r.feature)
                                  .filter((f): f is string => Boolean(f)),
                              ])
                            }
                          />
                        ) : (
                          <MUISelect
                            placeholder={
                              isFetchingFeatures ? 'Loading...' : 'Select'
                            }
                            value={row.feature || ''}
                            onChange={(e) =>
                              addOnSetRowValue(row.id, {
                                feature: String(e.target.value),
                              })
                            }
                            options={getAddOnFeatureOptions(row)}
                            disabled={isFetchingFeatures}
                            sx={{ minWidth: 180 }}
                          />
                        )}
                      </div>
                      {showMonthly && (
                        <div className='flex items-center justify-center'>
                          <TextInput
                            placeholder='XXX'
                            value={row.monthlyAmount || ''}
                            onChange={(e) =>
                              addOnSetRowValue(row.id, {
                                monthlyAmount: e.target.value,
                              })
                            }
                            type='number'
                            inputProps={{ min: 0 }}
                            error={!!addOnRowErrors[row.id]?.monthly}
                            helperText={
                              addOnRowErrors[row.id]?.monthly
                                ? 'Required'
                                : undefined
                            }
                            FormHelperTextProps={{ sx: { m: 0 } }}
                            sx={{
                              maxWidth: 140,
                              '& input[type=number]': {
                                MozAppearance: 'textfield',
                              },
                              '& input[type=number]::-webkit-outer-spin-button':
                                {
                                  WebkitAppearance: 'none',
                                  margin: 0,
                                },
                              '& input[type=number]::-webkit-inner-spin-button':
                                {
                                  WebkitAppearance: 'none',
                                  margin: 0,
                                },
                            }}
                          />
                        </div>
                      )}
                      {showYearly && (
                        <div className='flex items-center justify-center'>
                          <TextInput
                            placeholder='XXX'
                            value={row.yearlyAmount || ''}
                            onChange={(e) =>
                              addOnSetRowValue(row.id, {
                                yearlyAmount: e.target.value,
                              })
                            }
                            type='number'
                            inputProps={{ min: 0 }}
                            error={!!addOnRowErrors[row.id]?.yearly}
                            helperText={
                              addOnRowErrors[row.id]?.yearly
                                ? 'Required'
                                : undefined
                            }
                            FormHelperTextProps={{ sx: { m: 0 } }}
                            sx={{
                              maxWidth: 140,
                              '& input[type=number]': {
                                MozAppearance: 'textfield',
                              },
                              '& input[type=number]::-webkit-outer-spin-button':
                                {
                                  WebkitAppearance: 'none',
                                  margin: 0,
                                },
                              '& input[type=number]::-webkit-inner-spin-button':
                                {
                                  WebkitAppearance: 'none',
                                  margin: 0,
                                },
                            }}
                          />
                        </div>
                      )}
                      <div className='flex items-center justify-end gap-2'>
                        <button
                          onClick={() => addOnRemoveRow(row.id)}
                          className='inline-flex items-center justify-center h-7 w-7 rounded border border-gray-300 hover:bg-gray-100'
                          title='Remove'
                        >
                          <X className='w-4 h-4' />
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div
                className='grid items-center px-4 mt-3'
                style={{
                  gridTemplateColumns: featureGridTemplate,
                }}
              >
                <div className='text-sm text-gray-700 font-medium'>
                  Add-on Total Amount
                </div>
                {showMonthly && (
                  <div className='text-left font-semibold px-4'>
                    ₹{addOnTotals.monthly.toFixed(2)}
                  </div>
                )}
                {showYearly && (
                  <div className='text-left font-semibold px-4'>
                    ₹{addOnTotals.yearly.toFixed(2)}
                  </div>
                )}
                <div />
              </div>
            </div>
          )}
        </div>
        {isInitialLoading && (
          <div className='absolute inset-0 flex flex-col items-center justify-center gap-4 bg-white/80 backdrop-blur-sm px-6'>
            <div className='text-sm font-medium text-gray-600'>
              Loading plan details...
            </div>
            <div className='w-full flex flex-col gap-3 max-w-md animate-pulse'>
              <div className='h-4 rounded bg-gray-200' />
              <div className='h-4 rounded bg-gray-200' />
              <div className='h-4 rounded bg-gray-200' />
            </div>
          </div>
        )}
      </DialogContent>
      <DialogActions
        sx={{
          borderTop: '1px solid #e0e0e0',
          px: 3,
          py: 2,
          gap: 2,
          justifyContent: 'flex-end',
        }}
      >
        <AppButton
          kind='secondary'
          onClick={onClose}
          disabled={isLoading || isInitialLoading}
          sx={{ minWidth: 160 }}
        >
          Cancel
        </AppButton>
        <AppButton
          onClick={handleSave}
          disabled={isLoading || isInitialLoading}
          sx={{ minWidth: 160 }}
        >
          {isLoading
            ? 'Updating...'
            : isInitialLoading
              ? 'Loading...'
              : 'Update'}
        </AppButton>
      </DialogActions>
    </Dialog>
  );
};

export default PlanEditModal;
