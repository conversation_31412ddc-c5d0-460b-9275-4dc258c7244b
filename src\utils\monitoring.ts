import {
  createReactRouterV6DataOptions,
  Faro,
  getWebInstrumentations,
  initializeFaro,
  LogLevel,
  ReactIntegration,
} from '@grafana/faro-react';
import { TracingInstrumentation } from '@grafana/faro-web-tracing';
import { matchRoutes } from 'react-router-dom';

// Initialize Faro in both development and production, but with different configurations
let faro: Faro | null = null;

export const initializeMonitoring = () => {
  if (!faro) {
    const isProduction = import.meta.env.PROD;
    const appName = 'arca-ai-admin';
    const version = '1.0.0';
    const environment = isProduction ? 'production' : 'development';

    console.log(`Initializing Faro for ${environment} environment`);

    faro = initializeFaro({
      url: 'https://faro-collector-prod-us-west-0.grafana.net/collect/892859c8fc3f42a25dbbf8af4bed3cae',
      app: {
        name: appName,
        version,
        environment,
      },
      sessionTracking: {
        enabled: true,
        persistent: true,
      },
      batching: {
        sendTimeout: 2500,
      },
      instrumentations: [
        ...getWebInstrumentations(),
        new TracingInstrumentation(),
        new ReactIntegration({
          router: createReactRouterV6DataOptions({
            matchRoutes,
          }),
        }),
      ],
    });

    // Add event listeners for debugging
    faro.api.pushEvent('faro.initialized', { environment });

    // Log to console in development
    if (!isProduction) {
      const logContext = { appName, version, environment };
      faro.api.pushLog(['Faro initialized in development mode'], {
        level: LogLevel.INFO,
        context: logContext,
      });

      // Log the Faro instance for debugging
      console.log('Faro instance:', faro);
    }

    // Add global error handler
    window.addEventListener('error', (event) => {
      faro?.api.pushError(event.error || event);
    });

    window.addEventListener('unhandledrejection', (event) => {
      faro?.api.pushError(event.reason);
    });

    console.log('Faro monitoring initialized');
  }
  return faro;
};

export const getFaro = () => faro;

// Helper function to manually log errors
export const logError = (
  error: Error,
  context: Record<string, string> = {}
) => {
  const faroInstance = getFaro();
  if (faroInstance) {
    faroInstance.api.pushError(error, context);
  } else {
    console.error('Faro not initialized:', error, context);
  }
};

// Helper function to log custom events
export const logEvent = (
  name: string,
  attributes: Record<string, string> = {}
) => {
  const faroInstance = getFaro();
  if (faroInstance) {
    faroInstance.api.pushEvent(name, attributes);
  } else {
    console.log(`[Faro Event] ${name}`, attributes);
  }
};

// Helper function to manually log messages
export const logMessage = (
  message: string,
  level: LogLevel = LogLevel.INFO,
  context: Record<string, string> = {}
) => {
  const faroInstance = getFaro();
  if (faroInstance) {
    const logOptions: any = { level };
    if (Object.keys(context).length > 0) {
      logOptions.context = context;
    }
    faroInstance.api.pushLog([message], logOptions);
  } else {
    console.log(`[Faro ${level}] ${message}`, context);
  }
};
