import React from 'react';

import OrganizationRequired from '../../components/Common/OrganizationRequired';
import { useAuth } from '../../hooks/useAuth';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import InvoiceListComponent from './components/InvoiceListComponent';

const InvoiceList: React.FC = () => {
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  if (isSuperAdmin) {
    // Check localStorage as fallback for when user navigates back before Redux state hydrates
    let orgToUse = selectedOrganization;
    if (!orgToUse) {
      try {
        const savedOrg = localStorage.getItem('selectedOrganization');
        if (savedOrg) {
          orgToUse = JSON.parse(savedOrg);
        }
      } catch (error) {
        console.error(
          'Failed to parse saved organization from localStorage:',
          error
        );
      }
    }

    if (orgToUse) {
      return (
        <InvoiceListComponent
          organizationName={orgToUse.name}
          organizationId={orgToUse.id ?? null}
        />
      );
    }
    return (
      <OrganizationRequired
        feature='invoices'
        customTitle='Organization Required'
        customMessage='Please select an organization to view invoices.'
      />
    );
  }

  if (isOrganizationAdmin) {
    const orgId =
      userOrganizationId ||
      selectedOrganization?.id ||
      getCurrentOrganizationId();
    if (orgId) {
      const organizationName =
        selectedOrganization?.name || 'Your Organization';
      return (
        <InvoiceListComponent
          organizationName={organizationName}
          organizationId={orgId}
        />
      );
    }
    return (
      <div className='p-4 text-center text-red-500'>
        Organization not found. Please contact support.
      </div>
    );
  }

  return (
    <div className='flex h-64 items-center justify-center'>
      <p className='text-gray-500'>Loading invoices...</p>
    </div>
  );
};

export default InvoiceList;
