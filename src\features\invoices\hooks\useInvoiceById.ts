import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import { fetchInvoiceById } from '../../../store/features/invoices';
import {
  FetchInvoiceByIdParams,
  InvoiceDetailResponse,
} from '../../../store/features/invoices/invoice.service';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';

export const useInvoiceById = (organizationId?: string | null) => {
  const dispatch = useDispatch<AppDispatch>();

  const {
    entity: invoice,
    detail: invoiceDetail,
    loading,
    error,
  } = useSelector((state: RootState) => state.invoices);

  const currentOrganizationId = organizationId ?? getCurrentOrganizationId();

  const fetchInvoice = useCallback(
    async (id: string): Promise<InvoiceDetailResponse | undefined> => {
      if (!currentOrganizationId) {
        console.warn('Organization ID is required to fetch invoice');
        return undefined;
      }

      const params: FetchInvoiceByIdParams = {
        organizationId: currentOrganizationId,
        id,
      };

      const action = await dispatch(fetchInvoiceById(params));
      if (fetchInvoiceById.fulfilled.match(action)) {
        return action.payload as InvoiceDetailResponse;
      }
      return undefined;
    },
    [dispatch, currentOrganizationId]
  );

  return {
    invoice,
    invoiceDetail,
    loading,
    error,
    fetchInvoice,
  };
};
