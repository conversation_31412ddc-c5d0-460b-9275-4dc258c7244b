import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import { Search } from 'lucide-react';
import React from 'react';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = 'Search...',
  className = '',
  size = 'md',
}) => {
  // Map custom size to MUI size and width
  const sizeMap = { sm: 'small', md: 'medium', lg: 'medium' } as const;
  const widthMap = { sm: 192, md: 256, lg: 320 }; // px

  return (
    <Box className={className} sx={{ width: widthMap[size] }}>
      <TextField
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        size={sizeMap[size]}
        fullWidth
        InputProps={{
          startAdornment: (
            <InputAdornment position='start'>
              <Search className='text-gray-400 w-4 h-4' />
            </InputAdornment>
          ),
        }}
        variant='outlined'
        sx={{
          '& .MuiOutlinedInput-root': {
            borderRadius: 2,
            fontSize: '0.875rem',
            paddingRight: 0,
          },
          '& input': {
            paddingY: 1.2,
          },
        }}
      />
    </Box>
  );
};

export default SearchBar;
