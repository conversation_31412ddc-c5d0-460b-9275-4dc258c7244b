import React from 'react';

import WarningIcon from '../../assets/svg/WarningIcon';

interface SubscriptionExpiryModalProps {
  daysRemaining: number;
  onUpgrade: () => void;
  onCancel: () => void;
}

const SubscriptionExpiryModal: React.FC<SubscriptionExpiryModalProps> = ({
  daysRemaining,
  onUpgrade,
  onCancel,
}) => {
  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center p-2'>
      {/* Backdrop with blur */}
      <div className='absolute inset-0 bg-black bg-opacity-30 backdrop-blur-sm' />

      {/* Modal */}
      <div className='relative bg-white rounded-lg shadow-xl p-4 max-w-sm w-full flex flex-col items-center text-center'>
        {/* Warning Icon */}
        <div className='mb-4'>
          <WarningIcon className='w-16 h-16' />
        </div>

        {/* Message */}
        <h3 className='text-[18px] font-semibold text-[#001926] mb-2'>
          {daysRemaining === 0
            ? 'Your trial ends today!'
            : `Your trial ends in ${daysRemaining} ${
                daysRemaining === 1 ? 'Day' : 'Days'
              }!`}
        </h3>
        <p className='text-[14px] font-normal text-[#001926] mb-6'>
          Upgrade now to avoid
          <br />
          discontinuation.
        </p>

        {/* Action Buttons */}
        <div className='flex flex-col gap-3 w-full items-center'>
          <button
            onClick={onUpgrade}
            className='px-6 py-2 text-[14px] font-normal text-white bg-black rounded-3xl hover:bg-gray-800 transition-colors w-full sm:w-auto'
          >
            Upgrade Now
          </button>
          <button
            onClick={onCancel}
            className='text-gray-500 font-medium hover:text-gray-700 transition-colors text-sm'
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionExpiryModal;
