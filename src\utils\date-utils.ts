/**
 * Calculate age from date of birth (YYYY-MM-DD format)
 * @param dobString - Date of birth in YYYY-MM-DD format
 * @returns Age in years as a number
 */
export const calculateAge = (dobString: string): number => {
  if (!dobString) return 0;

  const today = new Date();
  const birthDate = new Date(dobString);

  // Check if the date is valid
  if (isNaN(birthDate.getTime())) return 0;

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If birthday hasn't occurred yet this year, subtract 1 from age
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age < 0 ? 0 : age; // Return 0 if DOB is in the future
};

/**

 * @param dateString - Date string in any valid format
 * @returns Number of days until the date (negative if date is in the past)
 */
export const getDaysUntilDate = (dateString: string): number => {
  if (!dateString) return 0;

  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day

  const targetDate = new Date(dateString);
  targetDate.setHours(0, 0, 0, 0); // Reset time to start of day

  // Check if the date is valid
  if (isNaN(targetDate.getTime())) return 0;

  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};
