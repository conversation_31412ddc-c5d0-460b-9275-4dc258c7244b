import { createAction } from '@reduxjs/toolkit';

// Action to clear success message from any slice
export const clearSuccessMessage = createAction<{ slice: string }>(
  'root/clearSuccessMessage'
);

// Action to clear error message from any slice
export const clearErrorMessage = createAction<{ slice: string }>(
  'root/clearErrorMessage'
);

// Action to clear all messages from any slice
export const clearAllMessages = createAction<{ slice: string }>(
  'root/clearAllMessages'
);
