import { Numbers } from '@mui/icons-material';
import { <PERSON>, Chip, Popover, TextField, Typography } from '@mui/material';
import React, { useState } from 'react';

interface AgeRangePickerProps {
  fromAge: number | null;
  toAge: number | null;
  onFromAgeChange: (age: number | null) => void;
  onToAgeChange: (age: number | null) => void;
  label?: string;
  size?: 'small' | 'medium';
  disabled?: boolean;
}

const AgeRangePicker: React.FC<AgeRangePickerProps> = ({
  fromAge,
  toAge,
  onFromAgeChange,
  onToAgeChange,
  label = 'Age Range',
  size = 'small',
  disabled = false,
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  const handleFromAgeChange = (value: string) => {
    const age = value ? parseInt(value) : null;
    if (age === null || (age >= 0 && age <= 150)) {
      onFromAgeChange(age);
    }
  };

  const handleToAgeChange = (value: string) => {
    const age = value ? parseInt(value) : null;
    if (age === null || (age >= 0 && age <= 150)) {
      onToAgeChange(age);
    }
  };

  const formatAgeRange = () => {
    if (fromAge !== null && toAge !== null) {
      return `${fromAge} - ${toAge} years`;
    } else if (fromAge !== null) {
      return `From ${fromAge} years`;
    } else if (toAge !== null) {
      return `Up to ${toAge} years`;
    }
    return 'Select age range';
  };

  const hasAgeRange = fromAge !== null || toAge !== null;

  return (
    <>
      <Chip
        icon={<Numbers sx={{ fontSize: 16 }} />}
        label={formatAgeRange()}
        onClick={handleClick}
        variant={hasAgeRange ? 'filled' : 'outlined'}
        color={hasAgeRange ? 'primary' : 'default'}
        disabled={disabled}
        sx={{
          height: 32,
          '& .MuiChip-label': {
            fontSize: '0.875rem',
            fontWeight: hasAgeRange ? 500 : 400,
          },
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: hasAgeRange ? 'primary.dark' : 'action.hover',
          },
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: {
            p: 2,
            minWidth: 280,
            boxShadow: 3,
          },
        }}
      >
        <Typography variant='subtitle2' sx={{ mb: 2, fontWeight: 600 }}>
          {label}
        </Typography>

        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <TextField
            type='number'
            label='From Age'
            value={fromAge || ''}
            onChange={(e) => handleFromAgeChange(e.target.value)}
            size={size}
            fullWidth
            inputProps={{
              min: 0,
              max: 150,
            }}
          />

          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'text.secondary',
              fontSize: '0.875rem',
              fontWeight: 500,
            }}
          >
            to
          </Box>

          <TextField
            type='number'
            label='To Age'
            value={toAge || ''}
            onChange={(e) => handleToAgeChange(e.target.value)}
            size={size}
            fullWidth
            inputProps={{
              min: 0,
              max: 150,
            }}
          />
        </Box>
      </Popover>
    </>
  );
};

export default AgeRangePicker;
