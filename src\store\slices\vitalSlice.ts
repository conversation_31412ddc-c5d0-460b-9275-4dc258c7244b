import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import { generateMockApiResponse, mockVitalTypes } from '../../data/mockData';
import { PaginatedResponse, VitalType } from '../../types';

interface VitalState {
  vitals: VitalType[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: VitalState = {
  vitals: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

export const fetchVitals = createAsyncThunk(
  'vitals/fetchVitals',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    let filteredVitals = mockVitalTypes.filter(
      (vital) => vital.organizationId === params.organizationId
    );

    if (params.search) {
      filteredVitals = filteredVitals.filter((vital) =>
        vital.name.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    if (params.status) {
      filteredVitals = filteredVitals.filter(
        (vital) => vital.status === params.status
      );
    }

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredVitals.slice(startIndex, endIndex);

    const response: PaginatedResponse<VitalType> = {
      data: paginatedData,
      total: filteredVitals.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 500);
  }
);

export const createVital = createAsyncThunk(
  'vitals/createVital',
  async (vitalData: Omit<VitalType, 'id' | 'createdAt'>) => {
    const newVital: VitalType = {
      id: uuidv4(),
      ...vitalData,
      createdAt: new Date(),
    };
    mockVitalTypes.unshift(newVital);
    return await generateMockApiResponse(newVital, 800);
  }
);

export const updateVital = createAsyncThunk(
  'vitals/updateVital',
  async ({ id, data }: { id: string; data: Partial<VitalType> }) => {
    const index = mockVitalTypes.findIndex((vital) => vital.id === id);
    if (index === -1) {
      throw new Error('Vital not found');
    }
    const updatedVital = { ...mockVitalTypes[index], ...data } as VitalType;
    mockVitalTypes[index] = updatedVital;
    return await generateMockApiResponse(updatedVital, 800);
  }
);

const vitalSlice = createSlice({
  name: 'vitals',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchVitals.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchVitals.fulfilled, (state, action) => {
        state.loading = false;
        state.vitals = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchVitals.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch vitals';
      })
      .addCase(createVital.fulfilled, (state, action) => {
        state.vitals.unshift(action.payload.data);
        state.total += 1;
      })
      .addCase(updateVital.fulfilled, (state, action) => {
        const index = state.vitals.findIndex(
          (vital) => vital.id === action.payload.data.id
        );
        if (index !== -1) {
          state.vitals[index] = action.payload.data as VitalType;
        }
      });
  },
});

export default vitalSlice.reducer;
