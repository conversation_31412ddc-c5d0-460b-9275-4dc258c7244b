import 'react-quill/dist/quill.snow.css';

import { Quill } from 'react-quill';

export type StyleKey =
  | 'color'
  | 'font-size'
  | 'font-style'
  | 'font-weight'
  | 'text-decoration';

const STYLE_KEYS: StyleKey[] = [
  'color',
  'font-size',
  'font-style',
  'font-weight',
  'text-decoration',
];

const parseStyleString = (
  style?: string | null
): Partial<Record<StyleKey, string>> => {
  if (!style) return {};
  return style
    .split(';')
    .map((fragment) => fragment.trim())
    .filter(Boolean)
    .reduce<Partial<Record<StyleKey, string>>>((acc, declaration) => {
      const [prop, value] = declaration.split(':');
      if (!prop || !value) return acc;
      const key = prop.trim() as StyleKey;
      if (STYLE_KEYS.includes(key)) {
        acc[key] = value.trim();
      }
      return acc;
    }, {});
};

const styleObjectToString = (
  styles: Partial<Record<StyleKey, string>>
): string =>
  Object.entries(styles)
    .filter(([, value]) => Boolean(value))
    .map(([key, value]) => `${key}: ${value}`)
    .join('; ');

const extractStylesFromElement = (
  element: Element
): Partial<Record<StyleKey, string>> => {
  const styles = parseStyleString(element.getAttribute('style'));

  const tag = element.tagName;
  if ((tag === 'STRONG' || tag === 'B') && !styles['font-weight']) {
    styles['font-weight'] = '700';
  }
  if ((tag === 'EM' || tag === 'I') && !styles['font-style']) {
    styles['font-style'] = 'italic';
  }
  if ((tag === 'U' || tag === 'INS') && !styles['text-decoration']) {
    styles['text-decoration'] = 'underline';
  }

  return styles;
};

const resolveListItemStyles = (
  li: Element
): Partial<Record<StyleKey, string>> => {
  const current = parseStyleString(li.getAttribute('style'));
  const aggregated: Partial<Record<StyleKey, string>> = { ...current };

  for (const child of Array.from(li.children)) {
    const childStyles = extractStylesFromElement(child);
    Object.entries(childStyles).forEach(([key, value]) => {
      if (value) {
        aggregated[key as StyleKey] = value;
      }
    });
  }

  return aggregated;
};

export const applyListItemStyles = (root: Element | null) => {
  if (!root) return;
  const listItems = root.querySelectorAll('li');
  listItems.forEach((li) => {
    const styles = resolveListItemStyles(li);
    if (Object.keys(styles).length > 0) {
      (li as HTMLElement).setAttribute('style', styleObjectToString(styles));
    } else {
      (li as HTMLElement).removeAttribute('style');
    }
  });
};

export const normalizeListFormatting = (html?: string): string => {
  if (!html) return '';
  if (typeof window === 'undefined') return html;

  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  doc.body
    .querySelectorAll('script, style, iframe, object')
    .forEach((el) => el.remove());
  applyListItemStyles(doc.body);

  return doc.body.innerHTML;
};

const registerDefaultQuillAttributors = (() => {
  let registered = false;

  return () => {
    if (registered) return;
    const Size = Quill.import('attributors/style/size');
    Size.whitelist = ['12px', '14px', '16px', '18px', '24px', '32px'];
    Quill.register(Size, true);

    const ColorStyle = Quill.import('attributors/style/color');
    const BackgroundStyle = Quill.import('attributors/style/background');
    Quill.register(ColorStyle, true);
    Quill.register(BackgroundStyle, true);

    registered = true;
  };
})();

registerDefaultQuillAttributors();

export const defaultQuillModules = {
  toolbar: [
    [{ header: [1, 2, 3, false] }],
    [{ size: ['12px', '14px', '16px', '18px', '24px', '32px'] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ color: [] }, { background: [] }],
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ align: [] }],
    ['link', 'image'],
    ['clean'],
  ],
};

export const defaultQuillFormats = [
  'header',
  'size',
  'bold',
  'italic',
  'underline',
  'strike',
  'color',
  'background',
  'list',
  'align',
  'link',
  'image',
];
