import React from 'react';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import SubscriptionExpiryModal from '../../components/Common/SubscriptionExpiryModal';
import { useAuth } from '../../hooks/useAuth';
import { useSubscriptionExpiryModal } from '../../hooks/useSubscriptionExpiryModal';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import UserListComponent from './components/UserList';

const UserList: React.FC = () => {
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  // Check subscription expiry and show modal from 7 days before until today
  const { showModal, daysRemaining, onClose, onUpgrade } =
    useSubscriptionExpiryModal();

  // For super admins
  if (isSuperAdmin) {
    // If organization is selected from header, use the selected organization ID
    if (selectedOrganization) {
      return (
        <>
          <UserListComponent
            organizationName={selectedOrganization.name}
            organizationId={selectedOrganization.id || null}
          />
          {/* Subscription Expiry Modal */}
          {showModal && (
            <SubscriptionExpiryModal
              daysRemaining={daysRemaining}
              onUpgrade={onUpgrade}
              onCancel={onClose}
            />
          )}
        </>
      );
    } else {
      // If no organization selected, show all users (don't pass organizationId)
      return (
        <>
          <UserListComponent
            organizationName='All Organizations'
            organizationId={null}
          />
          {/* Subscription Expiry Modal */}
          {showModal && (
            <SubscriptionExpiryModal
              daysRemaining={daysRemaining}
              onUpgrade={onUpgrade}
              onCancel={onClose}
            />
          )}
        </>
      );
    }
  }

  // For organization admins, ensure we always have an organization ID
  if (isOrganizationAdmin) {
    const orgId =
      userOrganizationId ||
      selectedOrganization?.id ||
      getCurrentOrganizationId();

    if (!orgId) {
      console.error('No organization ID found for organization admin');
      return (
        <div className='p-4 text-red-600'>
          Error: Organization ID is required. Please contact support.
        </div>
      );
    }

    const organizationName = selectedOrganization?.name || 'Your Organization';

    return (
      <>
        <UserListComponent
          organizationName={organizationName}
          organizationId={orgId}
        />
        {/* Subscription Expiry Modal */}
        {showModal && (
          <SubscriptionExpiryModal
            daysRemaining={daysRemaining}
            onUpgrade={onUpgrade}
            onCancel={onClose}
          />
        )}
      </>
    );
  }

  // Default fallback - only super admins should reach here
  // For non-super admins, we should have already returned a component with their organization ID
  console.error('Unexpected user role or missing organization ID');
  return (
    <div className='flex h-64 items-center justify-center'>
      <LoadingSpinner text='Loading users...' />
    </div>
  );
};

export default UserList;
