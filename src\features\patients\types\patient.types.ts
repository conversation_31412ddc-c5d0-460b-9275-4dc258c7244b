export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  fullName: string; // Full name from API
  age: number;
  gender: 'Male' | 'Female' | 'Other';
  contactPhone?: string;
  email?: string;
  organizationId: string;
  registrationDate: Date;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PatientFilters {
  searchText: string;
  gender: string;
  fromAge: number | null;
  toAge: number | null;
  fromDate: string;
  toDate: string;
  organizationId: string;
}

export interface PatientPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export type PatientGender = 'Male' | 'Female' | 'Other';

export const PATIENT_GENDERS: PatientGender[] = ['Male', 'Female', 'Other'];
