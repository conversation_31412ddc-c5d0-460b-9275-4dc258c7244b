import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import checker from 'vite-plugin-checker';

// https://vitejs.dev/config/
export default defineConfig({
  // Ensure public folder files are copied to dist
  publicDir: 'public',
  plugins: [
    react(),
    checker({
      // Use TypeScript checker with strict configuration
      typescript: {
        buildMode: true,
        tsconfigPath: './tsconfig.app.json',
      },
      // Disable ESLint checker temporarily to avoid compatibility issues
      // ESLint will still run via the build scripts
      eslint: false,
      // Fail build on errors (TypeScript only for now)
      enableBuild: true,
      // Show overlay in development
      overlay: {
        initialIsOpen: false,
        position: 'tl',
        badgeStyle: 'position: fixed; top: 10px; left: 10px; z-index: 9999;',
      },
      // Terminal output configuration
      terminal: true,
    }),
  ],
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@reduxjs/toolkit',
      'react-redux',
      'axios',
      'date-fns',
      'uuid',
      'yup',
      '@hookform/resolvers/yup',
      'react-hook-form',
    ],
  },
  build: {
    // Fail build on TypeScript errors
    rollupOptions: {
      onwarn(warning, warn) {
        // Log all warnings for debugging
        console.warn('Build warning:', warning);

        // Fail build on TypeScript errors
        if (
          warning.code === 'PLUGIN_WARNING' &&
          warning.plugin === 'typescript'
        ) {
          throw new Error(`TypeScript error: ${warning.message}`);
        }

        // Fail build on any error-level issues
        if (warning.code === 'UNRESOLVED_IMPORT') {
          throw new Error(`Unresolved import: ${warning.message}`);
        }

        warn(warning);
      },
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'redux-vendor': ['@reduxjs/toolkit', 'react-redux'],
          'ui-vendor': ['@mui/material', '@mui/icons-material', 'antd'],
          'form-vendor': ['react-hook-form', '@hookform/resolvers', 'yup'],
          'utils-vendor': ['axios', 'date-fns', 'uuid', 'lucide-react'],
        },
      },
    },
    // Ensure source maps for better error tracking
    sourcemap: true,
    // Minify only in production, keep readable for error tracking
    minify: process.env.NODE_ENV === 'production',
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
  server: {
    port: 3000,
  },
});
