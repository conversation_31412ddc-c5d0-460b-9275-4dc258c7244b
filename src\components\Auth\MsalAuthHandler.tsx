// B2C MSAL Auth Handler - DISABLED - Using only Cognito
// This entire component is commented out as we're using only Cognito authentication
/*
import { InteractionStatus } from '@azure/msal-browser';
import { useMsal } from '@azure/msal-react';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { setAuthState } from '../../store/features/auth/auth.slice';
import { User } from '../../types';
import { fetchUserInfo } from '../../utils/authUtils';
import { handlePostLoginRedirect } from '../../utils/redirect-utils';
import LoadingSpinner from '../Common/LoadingSpinner';

const MsalAuthHandler: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  // B2C MSAL Auth Handler - DISABLED - Using only Cognito
  // Return children directly without any B2C authentication logic
  return <>{children}</>;

  // Original B2C logic commented out below:
  /*
  const { instance, inProgress, accounts } = useMsal();
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const dispatch = useDispatch();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [fetchAttempted, setFetchAttempted] = useState(false);

  const fetchAndSetUser = useCallback(async () => {
    // Prevent multiple fetch attempts
    if (fetchAttempted) {
      console.log('MsalAuthHandler: Fetch already attempted, skipping');
      return null;
    }

    try {
      // fetchInProgressRef.current = true;
      setIsAuthenticating(true);
      setFetchAttempted(true);
      // First, clear any existing user data to prevent race conditions
      const emptyUser: User = {
        id: '',
        email: '',
        name: '',
        firstName: '',
        lastName: '',
        roles: [],
        userRole: '',
        organizationId: '',
        status: 'inactive',
        mustResetPassword: false,
        createdAt: new Date(),
        lastLogin: new Date(),
        phone: '',
        subscriptionExpiryDate: null,
      };

      dispatch(
        setAuthState({
          user: emptyUser,
          selectedOrganization: null,
          currentOrganization: null,
        })
      );

      const { user: emrUser } = await fetchUserInfo();

      if (emrUser) {
        // Map EMR user role to our expected role names
        const mapRoleName = (role: string): string => {
          const roleMap: Record<string, string> = {
            admin: 'ADMIN',
            superadmin: 'SUPER_ADMIN',
            super_admin: 'SUPER_ADMIN',
            'super admin': 'SUPER_ADMIN',
          };
          return roleMap[role.toLowerCase()] || role;
        };

        const mappedRoleName = mapRoleName(emrUser.userRole);

        // Map EMR user to our User type
        const user: User = {
          id: emrUser.id,
          email: emrUser.email,
          firstName: emrUser.name?.split(' ')[0] || '',
          lastName: emrUser.name?.split(' ').slice(1).join(' ') || '',
          name: emrUser.name,
          phone: emrUser.phoneNumber || '',
          organizationName: emrUser.organizationName,
          roles: [
            {
              id: emrUser.roleId,
              name: mappedRoleName,
              description: emrUser.userRole,
              permissions:
                emrUser.permissionKeys?.map((key) => ({
                  id: key,
                  key,
                  api: `/${key.replace(/\./g, '/')}`,
                  methods: ['GET', 'POST', 'PUT', 'DELETE'],
                  module:
                    (key.split('.')[0] as 'EMR' | 'MRD' | 'ADMIN') || 'ADMIN',
                  actions: ['read'],
                })) || [],
              organizationId: emrUser.organizationId,
              isSystem: true,
              departmentId: '',
              isDefault: false,
              createdAt: new Date(),
            },
          ],
          userRole: emrUser.userRole,
          organizationId: emrUser.organizationId,
          status: emrUser.isActive ? 'active' : 'inactive',
          mustResetPassword: false,
          createdAt: new Date(emrUser.createdAt || new Date()),
          lastLogin: new Date(emrUser.lastLogin || new Date()),
          subscriptionExpiryDate: emrUser.subscriptionExpiryDate
            ? new Date(emrUser.subscriptionExpiryDate)
            : null,
          subscriberId: emrUser.subscriberId || undefined,
        };

        // Save user to localStorage for persistence
        localStorage.setItem('user', JSON.stringify(user));

        // Update auth state with the new user
        await dispatch(
          setAuthState({
            user: {
              ...user,
              subscriptionExpiryDate: emrUser.subscriptionExpiryDate
                ? new Date(emrUser.subscriptionExpiryDate)
                : null,
            },
            selectedOrganization: null,
            currentOrganization: null,
          })
        );

        // Only handle redirect if we're on the login/root path
        // This prevents multiple redirects when the component re-renders
        if (pathname === PATHS.LOGIN || pathname === PATHS.ROOT) {
          handlePostLoginRedirect(navigate, pathname);
        }

        return user;
      } else {
        console.warn(
          'MsalAuthHandler: No user data received from fetchUserInfo. Redirect assumed handled by MSAL.'
        );
        return null;
      }
    } catch (error) {
      console.error('MsalAuthHandler: Error in fetchUserInfo:', error);
      // Clear auth state on error
      dispatch(
        setAuthState({
          user: {
            id: '',
            email: '',
            name: '',
            firstName: '',
            lastName: '',
            roles: [],
            userRole: 'user',
            organizationId: '',
            status: 'inactive',
            mustResetPassword: false,
            phone: '',
            organizationName: '',
            createdAt: new Date(),
            lastLogin: new Date(),
            subscriptionExpiryDate: null,
          },
          selectedOrganization: null,
          currentOrganization: null,
        })
      );

      // Redirect to unauthorized page when fetchUserByEmail fails
      console.log(
        'MsalAuthHandler: Redirecting to unauthorized page due to fetch error'
      );
      navigate('/unauthorized?reason=user_fetch_failed', { replace: true });

      return null;
    } finally {
      // fetchInProgressRef.current = false;
      setIsAuthenticating(false);
    }
  }, [dispatch, navigate]);

  const handleRedirect = useCallback(async () => {
    if (isProcessing) {
      return;
    }

    try {
      setIsProcessing(true);

      // Check for active account first
      const account = instance.getActiveAccount() || accounts[0];

      // If we have an active account and we're on login page or root
      if (account && (pathname === PATHS.LOGIN || pathname === PATHS.ROOT)) {
        // Don't redirect here, let the fetchAndSetUser complete first
        // The actual redirect will happen after user data is loaded
        return;
      }

      // Handle the redirect promise
      const response = await instance.handleRedirectPromise();

      if (response) {
        instance.setActiveAccount(response.account);

        if (response.accessToken) {
          localStorage.setItem('token', response.accessToken);

          // Fetch user info and handle redirection
          try {
            await fetchAndSetUser();
            // The actual redirect will be handled by fetchAndSetUser
            // after user data is loaded and stored in the state
          } catch (error) {
            console.error('Error during user info fetch:', error);
            // If there's an error, redirect to login
            navigate(PATHS.LOGIN, { replace: true });
          }
        }
      } else if (!account) {
        // If no active account and not on login page, redirect to login
        if (pathname !== PATHS.LOGIN && pathname !== PATHS.ROOT) {
          sessionStorage.setItem('preLoginUrl', pathname);
          navigate(PATHS.LOGIN, { replace: true });
        }
      } else {
        console.log(
          'MsalAuthHandler: User already logged in, no action needed'
        );
      }
    } catch (error) {
      console.error('MsalAuthHandler: Error during redirect handling:', error);
      // On error, redirect to login
      navigate(PATHS.LOGIN, { replace: true });
    } finally {
      setIsProcessing(false);
    }
  }, [instance, navigate, pathname, isProcessing, accounts, fetchAndSetUser]);

  useEffect(() => {
    const checkAuthAndFetchUser = async () => {
      try {
        const account = instance.getActiveAccount() || accounts[0];
        if (account && !fetchAttempted) {
          await fetchAndSetUser();
        } else if (inProgress === InteractionStatus.None) {
          // Only handle redirect if we're not in the middle of an interaction
          await handleRedirect();
        }
      } catch (error) {
        console.error('Error in checkAuthAndFetchUser:', error);

        navigate('/unauthorized?reason=user_fetch_failed', { replace: true });
      }
    };

    // Only run once on mount or when dependencies change
    if (!fetchAttempted) {
      checkAuthAndFetchUser();
    }

    // Note: Removed automatic 5-minute refresh interval to prevent
    // repeated API calls when the user email API fails
  }, [
    inProgress,
    handleRedirect,
    fetchAndSetUser,
    instance,
    accounts,
    navigate,
    fetchAttempted,
  ]);

  // Show loading state while handling redirect or authenticating
  if (inProgress !== InteractionStatus.None || isAuthenticating) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner />
      </div>
    );
  }

  return <>{children}</>;
};

export default MsalAuthHandler;
*/
