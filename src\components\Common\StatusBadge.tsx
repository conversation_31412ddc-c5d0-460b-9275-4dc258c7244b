import { Chip } from '@mui/material';
import React from 'react';

import { statusColors } from '../../constants/colors';

interface StatusBadgeProps {
  status: 'active' | 'inactive' | 'pending' | 'completed' | 'cancelled';
  size?: 'sm' | 'md';
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, size = 'md' }) => {
  const colorStyles = statusColors[status] || statusColors.default;

  const getStatusLabel = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <Chip
      label={getStatusLabel(status)}
      size={size === 'sm' ? 'small' : 'medium'}
      variant='outlined'
      sx={{
        ...colorStyles,
        fontWeight: 500,
        borderWidth: 1,
      }}
    />
  );
};

export default StatusBadge;
