import { useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { AppDispatch } from '../store';
import { fetchUserByEmail } from '../store/features/users/user.slice';
import { useAuth } from './useAuth';

export const useSubscriptionCheck = () => {
  const { user, isOrganizationAdmin, isAuthenticated } = useAuth();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const location = useLocation();
  const lastCheckedPathRef = useRef<string | null>(null);

  useEffect(() => {
    // Only check if user is organization admin and authenticated
    if (!isAuthenticated || !isOrganizationAdmin || !user?.email) {
      return;
    }

    // Only check once per page (when location changes)
    if (lastCheckedPathRef.current === location.pathname) {
      return;
    }

    const checkSubscription = async () => {
      try {
        lastCheckedPathRef.current = location.pathname;

        // Fetch the latest user data
        const result = await dispatch(fetchUserByEmail(user.email));

        if (fetchUserByEmail.fulfilled.match(result)) {
          const fetchedUser = result.payload;

          // Check if user has subscriberId and subscriptionExpiry
          if (fetchedUser?.subscriberId && fetchedUser?.subscriptionExpiry) {
            const expiryDate = new Date(fetchedUser.subscriptionExpiry);
            const now = new Date();

            if (expiryDate < now) {
              // Subscription expired, navigate to unauthorized page
              navigate('/unauthorized?reason=subscription_expired', {
                replace: true,
              });
            }
          }
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
      }
    };

    checkSubscription();
  }, [
    user?.email,
    isOrganizationAdmin,
    isAuthenticated,
    dispatch,
    navigate,
    location.pathname,
  ]);
};
