import { Organization } from '../types';

/**
 * Safely gets the organization ID from an organization object
 * @param org The organization object
 * @returns The organization ID or undefined if not available
 */
export const getOrganizationId = (
  org: Organization | null | undefined
): string | undefined => {
  return org?.id ?? undefined;
};

/**
 * Gets the organization ID or throws an error if not available
 * @param org The organization object
 * @param message Optional custom error message
 * @returns The organization ID
 * @throws Error if organization or ID is not available
 */
export const requireOrganizationId = (
  org: Organization | null | undefined,
  message = 'No organization selected'
): string => {
  const orgId = getOrganizationId(org);
  if (!orgId) {
    throw new Error(message);
  }
  return orgId;
};
