import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import LoadingSpinner from '../components/Common/LoadingSpinner';
import { PATHS } from '../constants/paths';
import {
  ALLOWED_ADMIN_PANEL_ROLES,
  isAllowedAdminRole,
  ROLES,
} from '../constants/roles';
import { setAuthState } from '../store/features/auth/auth.slice';
import userService from '../store/features/users/user.service';
import { User } from '../types';
import {
  cognitoLogin,
  getCognitoUserInfo,
  handleCognitoCallback,
  isCognitoAuthenticated,
} from '../utils/cognitoAuth';

const LandingPage: React.FC = (): JSX.Element => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [isCheckingAuth, setIsCheckingAuth] = useState(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const hasCode = urlParams.has('code');
    const isProcessing = Boolean(
      sessionStorage.getItem('cognito_processing_callback')
    );
    return hasCode || isProcessing;
  });

  useEffect(() => {
    const checkAuth = async () => {
      let shouldStopLoading = true;
      try {
        // Check if this is an OAuth callback from Cognito
        const urlParams = new URLSearchParams(window.location.search);
        const hasCode = urlParams.has('code');

        if (hasCode) {
          // Check if we are already processing this code in this session to prevent double invocation
          if (sessionStorage.getItem('cognito_processing_callback')) {
            console.log('Already processing callback, skipping...');
            // Keep the loader visible while the other invocation finishes processing.
            shouldStopLoading = false;
            return;
          }

          // Set flag to tell CognitoAuthHandler to wait
          sessionStorage.setItem('cognito_processing_callback', 'true');

          // Handle Cognito callback
          console.log('Handling Cognito OAuth callback...');
          try {
            const success = await handleCognitoCallback();
            if (success) {
              console.log(
                'Cognito authentication successful, fetching user profile...'
              );

              const cognitoUser = getCognitoUserInfo();
              if (cognitoUser?.email) {
                // Fetch full user details from EMR API
                const emrUser = await userService.fetchUserByEmail(
                  cognitoUser.email
                );

                if (emrUser) {
                  // Map EMR user role to app role format
                  const mapRoleName = (
                    role: string
                  ): (typeof ALLOWED_ADMIN_PANEL_ROLES)[number] | string => {
                    const normalized = role.toLowerCase().trim();
                    if (normalized.includes('organization')) {
                      return ROLES.ORGANIZATION_ADMIN;
                    }
                    if (
                      normalized === 'super admin' ||
                      normalized === 'super_admin' ||
                      normalized === 'superadmin'
                    ) {
                      return ROLES.SUPER_ADMIN;
                    }
                    if (
                      normalized === 'admin' ||
                      normalized === 'administrator'
                    ) {
                      return ROLES.ORGANIZATION_ADMIN;
                    }
                    return role;
                  };

                  const mappedRoleName = mapRoleName(emrUser.userRole);
                  const isSuperAdmin = mappedRoleName === ROLES.SUPER_ADMIN;
                  const isAllowed = isAllowedAdminRole(mappedRoleName);

                  if (!isAllowed) {
                    console.log(
                      'User role not allowed for Admin Panel:',
                      mappedRoleName
                    );
                    sessionStorage.removeItem('cognito_processing_callback');
                    navigate('/unauthorized', { replace: true });
                    return;
                  }

                  // Construct the user object
                  const user: User = {
                    id: emrUser.id,
                    email: emrUser.email,
                    firstName: emrUser.name?.split(' ')[0] || '',
                    lastName: emrUser.name?.split(' ').slice(1).join(' ') || '',
                    name: emrUser.name,
                    phone: emrUser.phoneNumber || '',
                    organizationName: emrUser.organizationName,
                    roles: [
                      {
                        id: emrUser.roleId,
                        name: mappedRoleName,
                        description: emrUser.userRole,
                        permissions:
                          emrUser.permissionKeys?.map((key: string) => ({
                            id: key,
                            key,
                            api: `/${key.replace(/\./g, '/')}`,
                            methods: ['GET', 'POST', 'PUT', 'DELETE'],
                            module:
                              (key.split('.')[0] as 'EMR' | 'MRD' | 'ADMIN') ||
                              'ADMIN',
                            actions: ['read'],
                          })) || [],
                        organizationId: emrUser.organizationId,
                        isSystem: true,
                        departmentId: '',
                        isDefault: false,
                        createdAt: new Date(),
                      },
                    ],
                    userRole: emrUser.userRole,
                    organizationId: emrUser.organizationId,
                    status: emrUser.isActive ? 'active' : 'inactive',
                    mustResetPassword: false,
                    createdAt: new Date(emrUser.createdAt || new Date()),
                    lastLogin: new Date(emrUser.lastLogin || new Date()),
                    subscriptionExpiryDate: emrUser.subscriptionExpiryDate
                      ? new Date(emrUser.subscriptionExpiryDate)
                      : null,
                  };

                  // Persist user to localStorage
                  localStorage.setItem('user', JSON.stringify(user));

                  // Update Redux state
                  dispatch(
                    setAuthState({
                      user,
                      selectedOrganization: null,
                      currentOrganization: null,
                    })
                  );

                  // DONE PROCESSING - Clear flags
                  sessionStorage.removeItem('preLoginUrl');
                  sessionStorage.removeItem('cognito_processing_callback');

                  // --- REDIRECT LOGIC ---
                  if (isSuperAdmin) {
                    console.log(
                      'User is Super Admin, redirecting to Organizations'
                    );
                    navigate(PATHS.ORGANIZATIONS, { replace: true });
                  } else {
                    console.log(
                      'User is Admin/Organization Admin, redirecting to Users'
                    );
                    navigate(PATHS.USERS, { replace: true });
                  }
                  return;
                } else {
                  console.error('Fetch user by email returned no user');
                  toast.error('User profile not found.');
                  sessionStorage.removeItem('cognito_processing_callback'); // Clear flag
                  navigate('/unauthorized');
                }
              } else {
                console.error('No email found in Cognito user info');
                toast.error('Authentication error: Email missing.');
                sessionStorage.removeItem('cognito_processing_callback'); // Clear flag
                navigate('/login');
              }
            }
          } catch (callbackError) {
            console.error('Cognito callback error:', callbackError);
            toast.error('Authentication failed. Please try again.');
            sessionStorage.removeItem('cognito_processing_callback'); // Clear flag
            navigate('/login');
          }
        } else if (isCognitoAuthenticated()) {
          // ... existing logic for already authenticated ...
          // We can probably leave this as redirecting to root,
          // but strictly speaking we should check role here too if we want to be safe.
          // For now, let's just use ROOT so CognitoAuthHandler can pick it up.
          const redirectUrl =
            sessionStorage.getItem('preLoginUrl') || PATHS.ROOT;
          sessionStorage.removeItem('preLoginUrl');
          navigate(redirectUrl);
        }
      } catch (error) {
        console.error('Error checking authentication status:', error);
        toast.error('An error occurred during authentication check.');
      } finally {
        if (shouldStopLoading) {
          setIsCheckingAuth(false);
        }
      }
    };

    checkAuth();
  }, [navigate, dispatch]);

  // Show loading state while checking auth status
  if (isCheckingAuth) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner size='lg' text='Completing sign in...' />
      </div>
    );
  }

  const handleLogin = async () => {
    try {
      // Remove any pre-set redirect to ensure clean slate
      sessionStorage.removeItem('preLoginUrl');

      // Initiate Cognito login with PKCE flow
      await cognitoLogin();
    } catch (error) {
      if (error instanceof Error) {
        console.error('Login error details:', error);
        toast.error('Failed to initiate login. Please try again.');
      } else {
        console.error('Unexpected login error:', error);
        toast.error('An unexpected error occurred during login.');
      }
    }
  };

  return (
    <div className='flex min-h-screen bg-white'>
      {/* Left side with blue pattern */}
      <div className='hidden md:flex md:w-4/6 items-center justify-center bg-[#f0f7ff] relative overflow-hidden'>
        <div className="absolute inset-0 bg-[url('/images/auth-background.png')] bg-cover bg-center"></div>
        <div className='relative z-10 text-center p-8'>
          <img
            src='/images/auth-logo.png'
            alt='ARCA HEALTH SPHERE'
            className='h-34 w-auto mx-auto mb-6'
          />
        </div>
      </div>

      {/* Right side with form */}
      <div className='flex-1 flex flex-col justify-center items-center p-8 max-w-md mx-auto'>
        <div className='w-full space-y-8'>
          <div className='text-center space-y-2'>
            <h2 className='text-3xl font-bold text-gray-900'>
              Welcome to ArcaAI Admin
            </h2>
            <p className='text-gray-500 text-sm'>Sign in to continue</p>
          </div>

          <div className='mt-8 flex flex-col items-center'>
            <button
              onClick={handleLogin}
              className='w-full flex justify-center items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors'
            >
              Sign In
            </button>
            <p className='mt-4 text-sm text-gray-600'>
              Don't have an account?{' '}
              <span
                onClick={() => {
                  const baseUrl =
                    (typeof window !== 'undefined'
                      ? (window as any)._env_?.VITE_EMR_URL
                      : '') ||
                    import.meta.env.VITE_EMR_URL ||
                    '';
                  window.location.href = `${baseUrl}/subscription/signup`;
                }}
                className='font-semibold text-black cursor-pointer hover:opacity-80'
              >
                Signup
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
