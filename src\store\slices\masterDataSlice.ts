import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import {
  generateMockApiResponse,
  mockBanks,
  mockBranches,
  mockDepartments,
  mockLanguages,
  mockVitalTypes,
} from '../../data/mockData';
import { Bank, Branch, Department, Language, VitalType } from '../../types';

interface MasterDataState {
  departments: Department[];
  branches: Branch[];
  banks: Bank[];
  languages: Language[];
  vitalTypes: VitalType[];
  loading: boolean;
  error: string | null;
}

const initialState: MasterDataState = {
  departments: [],
  branches: [],
  banks: [],
  languages: [],
  vitalTypes: [],
  loading: false,
  error: null,
};

// Department actions
export const fetchDepartments = createAsyncThunk(
  'masterData/fetchDepartments',
  async (organizationId: string) => {
    const filtered = mockDepartments.filter(
      (dept) => dept.organizationId === organizationId
    );
    return await generateMockApiResponse(filtered, 500);
  }
);

export const createDepartment = createAsyncThunk(
  'masterData/createDepartment',
  async ({
    name,
    description,
    organizationId,
  }: {
    name: string;
    description?: string;
    organizationId: string;
  }) => {
    const newDepartment: Department = {
      id: uuidv4(),
      name,
      ...(description !== undefined && { description }),
      status: 'active',
      organizationId,
      createdAt: new Date(),
    };
    return await generateMockApiResponse(newDepartment, 600);
  }
);

// Branch actions
export const fetchBranches = createAsyncThunk(
  'masterData/fetchBranches',
  async (organizationId: string) => {
    const filtered = mockBranches.filter(
      (branch) => branch.organizationId === organizationId
    );
    return await generateMockApiResponse(filtered, 500);
  }
);

export const createBranch = createAsyncThunk(
  'masterData/createBranch',
  async ({
    name,
    address,
    contactPhone,
    organizationId,
  }: {
    name: string;
    address: string;
    contactPhone: string;
    organizationId: string;
  }) => {
    const newBranch: Branch = {
      id: uuidv4(),
      name,
      address,
      contactPhone,
      status: 'active',
      organizationId,
      createdAt: new Date(),
    };
    return await generateMockApiResponse(newBranch, 600);
  }
);

// Bank actions
export const fetchBanks = createAsyncThunk(
  'masterData/fetchBanks',
  async (organizationId: string) => {
    const filtered = mockBanks.filter(
      (bank) => bank.organizationId === organizationId
    );
    return await generateMockApiResponse(filtered, 500);
  }
);

export const createBank = createAsyncThunk(
  'masterData/createBank',
  async (bankData: {
    bankName: string;
    accountNumber: string;
    ifscCode: string;
    branchName: string;
    accountHolderName: string;
    organizationId: string;
  }) => {
    const newBank: Bank = {
      id: uuidv4(),
      ...bankData,
      status: 'active',
      createdAt: new Date(),
    };
    return await generateMockApiResponse(newBank, 600);
  }
);

// Language actions
export const fetchLanguages = createAsyncThunk(
  'masterData/fetchLanguages',
  async (organizationId: string) => {
    const filtered = mockLanguages.filter(
      (lang) => lang.organizationId === organizationId
    );
    return await generateMockApiResponse(filtered, 500);
  }
);

export const createLanguage = createAsyncThunk(
  'masterData/createLanguage',
  async ({
    name,
    isoCode,
    organizationId,
  }: {
    name: string;
    isoCode: string;
    organizationId: string;
  }) => {
    const newLanguage: Language = {
      id: uuidv4(),
      name,
      isoCode,
      status: 'active',
      organizationId,
      createdAt: new Date(),
    };
    return await generateMockApiResponse(newLanguage, 600);
  }
);

// Vital Type actions
export const fetchVitalTypes = createAsyncThunk(
  'masterData/fetchVitalTypes',
  async (organizationId: string) => {
    const filtered = mockVitalTypes.filter(
      (vital) => vital.organizationId === organizationId
    );
    return await generateMockApiResponse(filtered, 500);
  }
);

export const createVitalType = createAsyncThunk(
  'masterData/createVitalType',
  async (vitalData: {
    name: string;
    unit: string;
    dataType: 'numeric' | 'numeric-range' | 'text' | 'boolean';
    organizationId: string;
  }) => {
    const newVitalType: VitalType = {
      id: uuidv4(),
      ...vitalData,
      status: 'active',
      createdAt: new Date(),
    };
    return await generateMockApiResponse(newVitalType, 600);
  }
);

const masterDataSlice = createSlice({
  name: 'masterData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Departments
      .addCase(fetchDepartments.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchDepartments.fulfilled, (state, action) => {
        state.loading = false;
        state.departments = action.payload.data;
      })
      .addCase(fetchDepartments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch departments';
      })
      .addCase(createDepartment.fulfilled, (state, action) => {
        state.departments.push(action.payload.data);
      })

      // Branches
      .addCase(fetchBranches.fulfilled, (state, action) => {
        state.branches = action.payload.data;
      })
      .addCase(createBranch.fulfilled, (state, action) => {
        state.branches.push(action.payload.data);
      })

      // Banks
      .addCase(fetchBanks.fulfilled, (state, action) => {
        state.banks = action.payload.data;
      })
      .addCase(createBank.fulfilled, (state, action) => {
        state.banks.push(action.payload.data);
      })

      // Languages
      .addCase(fetchLanguages.fulfilled, (state, action) => {
        state.languages = action.payload.data;
      })
      .addCase(createLanguage.fulfilled, (state, action) => {
        state.languages.push(action.payload.data);
      })

      // Vital Types
      .addCase(fetchVitalTypes.fulfilled, (state, action) => {
        state.vitalTypes = action.payload.data;
      })
      .addCase(createVitalType.fulfilled, (state, action) => {
        state.vitalTypes.push(action.payload.data);
      });
  },
});

export const { clearError } = masterDataSlice.actions;
export default masterDataSlice.reducer;
