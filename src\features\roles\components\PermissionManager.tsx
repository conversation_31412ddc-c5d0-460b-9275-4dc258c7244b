import {
  Box,
  Checkbox,
  Collapse,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import React, { memo, useCallback, useMemo, useState } from 'react';

import { ModuleType, Permission } from '../../../types';

interface PermissionManagerProps {
  assignedPermissions: Permission[];
  onPermissionsChange: (newPermissions: Permission[]) => void;
}

interface Feature {
  name: string;
  subFeatures?: string[];
}

const allModules: { name: ModuleType; features: Feature[] }[] = [
  {
    name: 'MRD',
    features: [
      { name: 'Dashboard' },
      { name: 'Manage Patient', subFeatures: ['Patient Queue'] },
    ],
  },
  {
    name: 'E<PERSON>',
    features: [
      { name: 'Dashboard' },
      { name: 'Patient Info' },
      { name: 'Consultation' },
      { name: 'Prescription', subFeatures: ['Department Package'] },
      { name: 'Reports', subFeatures: ['Department Package'] },
      { name: 'Doctor <PERSON>' },
    ],
  },
  {
    name: 'Billing',
    features: [
      { name: 'Patient Registration' },
      { name: 'Appointment Booking' },
      { name: 'Prescription Master' },
      { name: 'Lab Master' },
    ],
  },
];

const PermissionManager: React.FC<PermissionManagerProps> = memo(
  ({ assignedPermissions, onPermissionsChange }) => {
    const [openModules, setOpenModules] = useState<ModuleType[]>([]);

    // Memoize permission lookup for performance
    const permissionMap = useMemo(() => {
      const map = new Map<string, boolean>();
      assignedPermissions.forEach((permission) => {
        const key = `${permission.module}-${permission.feature}`;
        map.set(key, true);
      });
      return map;
    }, [assignedPermissions]);

    const handleModuleToggle = useCallback((moduleName: ModuleType) => {
      setOpenModules((prev) =>
        prev.includes(moduleName)
          ? prev.filter((m) => m !== moduleName)
          : [...prev, moduleName]
      );
    }, []);

    const handleFeatureChange = useCallback(
      (moduleName: ModuleType, featureName: string, checked: boolean) => {
        let newPermissions = [...assignedPermissions];
        if (checked) {
          if (
            !newPermissions.some(
              (p) => p.module === moduleName && p.feature === featureName
            )
          ) {
            newPermissions.push({
              id: '',
              api: `/api/${featureName.toLowerCase().replace(/\s+/g, '-')}`,
              methods: ['GET'],
              module: moduleName,
              feature: featureName,
              actions: ['read'],
            });
          }
        } else {
          newPermissions = newPermissions.filter(
            (p) => !(p.module === moduleName && p.feature === featureName)
          );
        }
        onPermissionsChange(newPermissions);
      },
      [assignedPermissions, onPermissionsChange]
    );

    const getSubFeatureName = useCallback(
      (parentFeature: string, subFeature: string) =>
        `${parentFeature} - ${subFeature}`,
      []
    );

    const isFeatureChecked = useCallback(
      (moduleName: ModuleType, featureName: string) => {
        const key = `${moduleName}-${featureName}`;
        return permissionMap.has(key);
      },
      [permissionMap]
    );

    return (
      <Box sx={{ p: 2 }}>
        {allModules.map((module) => {
          const isModuleOpen = openModules.includes(module.name);

          return (
            <Box key={module.name} sx={{ mb: 2 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={isModuleOpen}
                    onChange={() => handleModuleToggle(module.name)}
                  />
                }
                label={<Typography variant='h6'>{module.name}</Typography>}
              />
              <Collapse in={isModuleOpen}>
                <Box sx={{ display: 'flex', flexDirection: 'column', ml: 3 }}>
                  <FormGroup>
                    {module.features.map((feature) => (
                      <Box key={feature.name}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={isFeatureChecked(
                                module.name,
                                feature.name
                              )}
                              onChange={(e) =>
                                handleFeatureChange(
                                  module.name,
                                  feature.name,
                                  e.target.checked
                                )
                              }
                            />
                          }
                          label={feature.name}
                        />
                        {feature.subFeatures && (
                          <Box
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              ml: 3,
                            }}
                          >
                            {feature.subFeatures.map((subFeature) => {
                              const subFeatureName = getSubFeatureName(
                                feature.name,
                                subFeature
                              );
                              return (
                                <FormControlLabel
                                  key={subFeatureName}
                                  control={
                                    <Checkbox
                                      checked={isFeatureChecked(
                                        module.name,
                                        subFeatureName
                                      )}
                                      onChange={(e) =>
                                        handleFeatureChange(
                                          module.name,
                                          subFeatureName,
                                          e.target.checked
                                        )
                                      }
                                    />
                                  }
                                  label={subFeature}
                                />
                              );
                            })}
                          </Box>
                        )}
                      </Box>
                    ))}
                  </FormGroup>
                </Box>
              </Collapse>
            </Box>
          );
        })}
      </Box>
    );
  }
);

PermissionManager.displayName = 'PermissionManager';

export default PermissionManager;
