import CloseIcon from '@mui/icons-material/Close';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import { AlertTriangle, Trash2 } from 'lucide-react';
import React from 'react';

import AppButton from './AppButton';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  loading?: boolean;
  itemName?: string;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'danger',
  loading = false,
  itemName,
}) => {
  const getTypeStyles = () => {
    switch (type) {
      case 'danger':
        return {
          icon: <Trash2 className='w-6 h-6' style={{ color: '#d32f2f' }} />,
          confirmKind: 'primary',
        };
      case 'warning':
        return {
          icon: (
            <AlertTriangle className='w-6 h-6' style={{ color: '#ed6c02' }} />
          ),
          confirmKind: 'primary', // Changed from 'secondary' to 'primary' for blue button
        };
      case 'info':
        return {
          icon: (
            <AlertTriangle className='w-6 h-6' style={{ color: '#0288d1' }} />
          ),
          confirmKind: 'primary',
        };
      default:
        return {
          icon: (
            <AlertTriangle className='w-6 h-6' style={{ color: '#d32f2f' }} />
          ),
          confirmKind: 'primary',
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth='xs' fullWidth>
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        {styles.icon}
        <span style={{ flex: 1 }}>{title}</span>
        <IconButton onClick={onClose} disabled={loading} size='small'>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        <div style={{ marginBottom: 12 }}>{message}</div>
        {itemName && (
          <div
            style={{
              background: '#f3f4f6',
              borderRadius: 4,
              padding: 8,
              marginBottom: 12,
            }}
          >
            <span style={{ fontWeight: 500 }}>Item:</span> {itemName}
          </div>
        )}
        <div
          style={{
            background: '#fffbe6',
            border: '1px solid #ffe58f',
            borderRadius: 4,
            padding: 8,
          }}
        >
          <span style={{ color: '#ad8b00', fontWeight: 500 }}>Warning:</span>{' '}
          This action cannot be undone.
        </div>
      </DialogContent>
      <DialogActions>
        <AppButton kind='secondary' onClick={onClose} disabled={loading}>
          {cancelText}
        </AppButton>
        <AppButton
          kind={styles.confirmKind as any}
          onClick={onConfirm}
          disabled={loading}
        >
          {loading ? 'Processing...' : confirmText}
        </AppButton>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationModal;
