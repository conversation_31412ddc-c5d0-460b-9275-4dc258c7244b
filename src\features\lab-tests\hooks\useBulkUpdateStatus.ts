import { useEffect, useRef, useState } from 'react';

import { BulkUpdateStatus } from '../types/bulk-update.types';

const POLLING_INTERVAL = 5000; // 5 seconds

export const useBulkUpdateStatus = () => {
  const [status, setStatus] = useState<BulkUpdateStatus | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const pollingInterval = useRef<NodeJS.Timeout>();

  const startPolling = async (url: string) => {
    if (isPolling) return;

    setIsPolling(true);

    const fetchStatus = async () => {
      try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Failed to fetch status');

        const data: BulkUpdateStatus = await response.json();
        setStatus(data);

        // Stop polling if the job is completed or failed
        if (data.status === 'COMPLETED' || data.status === 'FAILED') {
          stopPolling();
        }
      } catch (error) {
        console.error('Error fetching bulk update status:', error);
        stopPolling();
      }
    };

    // Initial fetch
    await fetchStatus();

    // Set up polling interval
    pollingInterval.current = setInterval(fetchStatus, POLLING_INTERVAL);
  };

  const stopPolling = () => {
    if (pollingInterval.current) {
      clearInterval(pollingInterval.current);
      pollingInterval.current = undefined;
    }
    setIsPolling(false);
  };

  // Clean up interval on unmount
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, []);

  return {
    status,
    isPolling,
    startPolling,
    stopPolling,
  };
};
