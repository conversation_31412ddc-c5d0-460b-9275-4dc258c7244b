/**
 * Unified Authentication Utilities
 * Provides a unified interface to work with both B2C and Cognito authentication
 */

import {
  clearCognitoTokens,
  cognitoLogout,
  getCognitoAccessToken,
  isCognitoAuthenticated,
} from './cognitoAuth';
import { cognitoStorageKeys } from './cognitoConfig';
// B2C imports commented out - using only Cognito
// import {
//   getAccessToken as getB2CToken,
//   isAuthenticated as isB2CAuthenticated,
//   logout as b2cLogout,
// } from './msal';

// export type AuthProvider = 'b2c' | 'cognito' | null;
export type AuthProvider = 'cognito' | null;

/**
 * Get the current authentication provider
 */
export function getAuthProvider(): AuthProvider {
  const provider = sessionStorage.getItem(cognitoStorageKeys.authProvider);

  // Force Cognito if environment variables are present
  const cognitoClientId =
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_CLIENT_ID
      : '') ||
    import.meta.env.VITE_COGNITO_CLIENT_ID ||
    '';
  const cognitoDomain =
    (typeof window !== 'undefined'
      ? (window as any)._env_?.VITE_COGNITO_DOMAIN
      : '') ||
    import.meta.env.VITE_COGNITO_DOMAIN ||
    '';

  if (cognitoClientId && cognitoDomain) {
    return 'cognito';
  }

  if (provider === 'cognito' && isCognitoAuthenticated()) {
    return 'cognito';
  }

  // B2C check commented out - using only Cognito
  // if (isB2CAuthenticated()) {
  //   return 'b2c';
  // }

  return null;
}

/**
 * Check if user is authenticated with any provider
 */
export function isAuthenticated(): boolean {
  // B2C auth check commented out - using only Cognito
  return isCognitoAuthenticated(); // || isB2CAuthenticated();
}

/**
 * Get access token from the active provider
 */
export async function getAccessToken(): Promise<string | null> {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    return getCognitoAccessToken();
  }

  // B2C token logic commented out - using only Cognito
  // if (provider === 'b2c') {
  //   try {
  //     return await getB2CToken();
  //   } catch (error) {
  //     console.error('Failed to get B2C token:', error);
  //     return null;
  //   }
  // }

  return null;
}

/**
 * Logout from the active provider
 */
export async function logout(): Promise<void> {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    await cognitoLogout();
  } else {
    // B2C logout commented out - using only Cognito
    // } else if (provider === 'b2c') {
    //   await b2cLogout();
    // } else {
    // Clear everything just in case
    clearCognitoTokens();
    localStorage.clear();
    sessionStorage.clear();
    window.location.href = '/login';
  }
}

/**
 * Get user email from the active provider
 */
export function getUserEmail(): string | null {
  const provider = getAuthProvider();

  if (provider === 'cognito') {
    const userInfoStr = sessionStorage.getItem(cognitoStorageKeys.userInfo);
    if (userInfoStr) {
      try {
        const userInfo = JSON.parse(userInfoStr);
        return userInfo.email || null;
      } catch (e) {
        console.error('Failed to parse Cognito user info:', e);
      }
    }
  }

  // B2C user email logic commented out - using only Cognito
  // if (provider === 'b2c') {
  //   const accountStr = sessionStorage.getItem('msal.account');
  //   if (accountStr) {
  //     try {
  //       const account = JSON.parse(accountStr);
  //       return account.username || account.email || null;
  //     } catch (e) {
  //       console.error('Failed to parse B2C account:', e);
  //     }
  //   }
  // }

  return null;
}

/**
 * Switch authentication provider (for testing/development)
 */
// B2C switch functionality commented out - using only Cognito
// export function switchAuthProvider(provider: 'b2c' | 'cognito'): void {
export function switchAuthProvider(provider: 'cognito'): void {
  // Clear current auth state
  clearCognitoTokens();
  sessionStorage.clear();
  localStorage.removeItem('token');

  // Redirect to appropriate login page
  if (provider === 'cognito') {
    window.location.href = '/cognito-login';
  } else {
    // B2C redirect commented out - using only Cognito
    // window.location.href = '/login';
    window.location.href = '/cognito-login';
  }
}

/**
 * Get authentication status for both providers
 */
export function getAuthStatus(): {
  provider: AuthProvider;
  isAuthenticated: boolean;
  // b2cAuthenticated: boolean;
  cognitoAuthenticated: boolean;
} {
  return {
    provider: getAuthProvider(),
    isAuthenticated: isAuthenticated(),
    // b2cAuthenticated: isB2CAuthenticated(), // Commented out - using only Cognito
    cognitoAuthenticated: isCognitoAuthenticated(),
  };
}
