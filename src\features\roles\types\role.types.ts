import { Permission } from '../../../types';

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
  organizationId: string;
  departmentId?: string;
  isSystem: boolean;
  isDefault?: boolean;
  createdAt: Date;
}

export interface CreateRoleData {
  name: string;
  description?: string;
  organizationId: string;
}

export interface UpdateRoleData {
  id: string;
  name?: string;
  description?: string;
  organizationId: string;
}

export interface RoleFormData {
  name: string;
  description: string;
  permissions: Permission[];
}

export interface RoleFilters {
  search: string;
  organizationId: string;
}

export interface RolePagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}
