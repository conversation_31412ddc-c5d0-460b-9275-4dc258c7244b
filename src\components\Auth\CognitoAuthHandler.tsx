/**
 * Cognito Authentication Handler
 * Handles Cognito authentication state and user data fetching
 * Similar to MsalAuthHandler but for AWS Cognito
 */

import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { ROLES } from '../../constants/roles';
import { setAuthState } from '../../store/features/auth/auth.slice';
import userService from '../../store/features/users/user.service';
import { User } from '../../types';
import {
  getCognitoUserInfo,
  isCognitoAuthenticated,
} from '../../utils/cognitoAuth';
import { handlePostLoginRedirect } from '../../utils/redirect-utils';
import LoadingSpinner from '../Common/LoadingSpinner';

const CognitoAuthHandler: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const dispatch = useDispatch();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [hasFetchedUser, setHasFetchedUser] = useState(false);
  const [fetchAttempted, setFetchAttempted] = useState(false);

  const fetchAndSetUser = useCallback(async () => {
    // Prevent multiple fetch attempts
    if (fetchAttempted) {
      console.log('CognitoAuthHandler: Fetch already attempted, skipping');
      return null;
    }

    try {
      setIsAuthenticating(true);
      setFetchAttempted(true);

      // Get Cognito user info
      const cognitoUser = getCognitoUserInfo();

      if (!cognitoUser || !cognitoUser.email) {
        console.warn('CognitoAuthHandler: No Cognito user info available');
        return null;
      }

      // Fetch EMR user info
      const emrUser = await userService.fetchUserByEmail(cognitoUser.email);

      if (emrUser) {
        // Map EMR user role to our expected role names
        const mapRoleName = (role: string): string => {
          const normalized = role.toLowerCase().trim();
          if (normalized.includes('organization')) {
            return ROLES.ORGANIZATION_ADMIN;
          }
          if (
            normalized === 'super admin' ||
            normalized === 'super_admin' ||
            normalized === 'superadmin'
          ) {
            return ROLES.SUPER_ADMIN;
          }
          if (normalized === 'admin' || normalized === 'administrator') {
            return ROLES.ORGANIZATION_ADMIN;
          }
          return role;
        };

        const mappedRoleName = mapRoleName(emrUser.userRole);

        // Map EMR user to our User type
        const user: User = {
          id: emrUser.id,
          email: emrUser.email,
          firstName: emrUser.name?.split(' ')[0] || '',
          lastName: emrUser.name?.split(' ').slice(1).join(' ') || '',
          name: emrUser.name,
          phone: emrUser.phoneNumber || '',
          organizationName: emrUser.organizationName,
          roles: [
            {
              id: emrUser.roleId,
              name: mappedRoleName,
              description: emrUser.userRole,
              permissions:
                emrUser.permissionKeys?.map((key: string) => ({
                  id: key,
                  key,
                  api: `/${key.replace(/\./g, '/')}`,
                  methods: ['GET', 'POST', 'PUT', 'DELETE'],
                  module:
                    (key.split('.')[0] as 'EMR' | 'MRD' | 'ADMIN') || 'ADMIN',
                  actions: ['read'],
                })) || [],
              organizationId: emrUser.organizationId,
              isSystem: true,
              departmentId: '',
              isDefault: false,
              createdAt: new Date(),
            },
          ],
          userRole: emrUser.userRole,
          organizationId: emrUser.organizationId,
          status: emrUser.isActive ? 'active' : 'inactive',
          mustResetPassword: false,
          createdAt: new Date(emrUser.createdAt || new Date()),
          lastLogin: new Date(emrUser.lastLogin || new Date()),
          subscriptionExpiryDate: emrUser.subscriptionExpiryDate
            ? new Date(emrUser.subscriptionExpiryDate)
            : null,
        };

        // Save user to localStorage for persistence
        localStorage.setItem('user', JSON.stringify(user));

        // Update auth state with the new user
        await dispatch(
          setAuthState({
            user: {
              ...user,
              subscriptionExpiryDate: emrUser.subscriptionExpiryDate
                ? new Date(emrUser.subscriptionExpiryDate)
                : null,
            },
            selectedOrganization: null,
            currentOrganization: null,
          })
        );

        console.log(
          'CognitoAuthHandler: User data fetched and stored successfully'
        );

        // Only handle redirect if we're on the login/root path
        if (pathname === PATHS.LOGIN || pathname === PATHS.ROOT) {
          handlePostLoginRedirect(navigate, pathname);
        }

        setHasFetchedUser(true);
        return user;
      } else {
        console.warn(
          'CognitoAuthHandler: No user data received from EMR service'
        );
        return null;
      }
    } catch (error) {
      console.error('CognitoAuthHandler: Error fetching user info:', error);

      // Clear auth state on error
      const emptyUser: User = {
        id: '',
        email: '',
        name: '',
        firstName: '',
        lastName: '',
        roles: [],
        userRole: '',
        organizationId: '',
        status: 'inactive',
        mustResetPassword: false,
        createdAt: new Date(),
        lastLogin: new Date(),
        phone: '',
        subscriptionExpiryDate: null,
      };

      dispatch(
        setAuthState({
          user: emptyUser,
          selectedOrganization: null,
          currentOrganization: null,
        })
      );

      // Redirect to unauthorized page when fetchUserByEmail fails
      console.log(
        'CognitoAuthHandler: Redirecting to unauthorized page due to fetch error'
      );
      navigate('/unauthorized?reason=user_fetch_failed', { replace: true });

      return null;
    } finally {
      setIsAuthenticating(false);
    }
  }, [dispatch, navigate, pathname]);

  useEffect(() => {
    const initAuth = async () => {
      try {
        // Check if LandingPage is currently processing the callback
        if (sessionStorage.getItem('cognito_processing_callback')) {
          console.log(
            'CognitoAuthHandler: Callback processing in progress elsewhere, pausing init...'
          );
          return;
        }

        // Check if user is authenticated with Cognito
        if (isCognitoAuthenticated()) {
          // Check if user data is already in localStorage (e.g. from LandingPage)
          const storedUserStr = localStorage.getItem('user');
          if (storedUserStr) {
            try {
              const storedUser = JSON.parse(storedUserStr);
              // Hydrate Redux from localStorage
              dispatch(
                setAuthState({
                  user: storedUser,
                  selectedOrganization: null,
                  currentOrganization: null,
                })
              );
              setHasFetchedUser(true);
              setFetchAttempted(true);
              console.log(
                'CognitoAuthHandler: Restored user from localStorage'
              );
              return; // Skip fetching
            } catch (e) {
              console.error('Failed to parse stored user', e);
            }
          }

          console.log(
            'CognitoAuthHandler: User is authenticated, fetching user data...'
          );
          await fetchAndSetUser();
        } else {
          console.log('CognitoAuthHandler: User not authenticated');

          // If not on login page and not authenticated, redirect to login
          if (pathname !== PATHS.LOGIN && pathname !== PATHS.ROOT) {
            sessionStorage.setItem('preLoginUrl', pathname);
            navigate(PATHS.LOGIN, { replace: true });
          }
        }
      } catch (error) {
        console.error(
          'CognitoAuthHandler: Error during initialization:',
          error
        );
      } finally {
        setIsInitialized(true);
      }
    };

    if (!isInitialized) {
      initAuth();
    } else if (
      isCognitoAuthenticated() &&
      !hasFetchedUser &&
      !isAuthenticating &&
      !fetchAttempted
    ) {
      // If initialized, authenticated, but haven't fetched user data yet
      // and haven't attempted to fetch (to prevent unlimited retries)
      // (e.g. after callback handling and navigation), fetch it now
      fetchAndSetUser();
    }

    // Note: Removed automatic 5-minute refresh interval to prevent
    // repeated API calls when the user email API fails
  }, [
    fetchAndSetUser,
    navigate,
    pathname,
    isInitialized,
    hasFetchedUser,
    isAuthenticating,
    fetchAttempted,
  ]);

  // Show loading state while authenticating or initializing
  if (!isInitialized || isAuthenticating) {
    return (
      <div className='flex flex-col items-center justify-center min-h-screen bg-gray-50'>
        <LoadingSpinner />
      </div>
    );
  }

  return <>{children}</>;
};

export default CognitoAuthHandler;
