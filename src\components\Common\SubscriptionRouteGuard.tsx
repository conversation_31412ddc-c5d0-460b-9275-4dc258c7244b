import React from 'react';
import { Navigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { useAuth } from '../../hooks/useAuth';
import LoadingSpinner from './LoadingSpinner';

interface SubscriptionRouteGuardProps {
  children: React.ReactNode;
}

/**
 * Route guard that prevents users from "Subscription - Organization"
 * from accessing the subscription page
 */
const SubscriptionRouteGuard: React.FC<SubscriptionRouteGuardProps> = ({
  children,
}) => {
  const { isSubscriptionOrganization, loading } = useAuth();

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  // Redirect to dashboard if user is from Subscription - Organization
  if (isSubscriptionOrganization()) {
    return <Navigate to={PATHS.DASHBOARD} replace />;
  }

  // Allow access if not from Subscription - Organization
  return <>{children}</>;
};

export default SubscriptionRouteGuard;
