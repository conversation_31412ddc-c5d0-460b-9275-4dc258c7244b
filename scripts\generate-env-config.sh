#!/bin/sh

# Generate environment configuration for the application
# This script creates the env-config.js file with all required environment variables
# Default to empty string if variable is not set

cat <<EOF > ./dist/env-config.js
window._env_ = {
  VITE_API_URL: "${VITE_API_URL:-}",
  VITE_COGNITO_CLIENT_ID: "${VITE_COGNITO_CLIENT_ID:-}",
  VITE_COGNITO_DOMAIN: "${VITE_COGNITO_DOMAIN:-}",
  VITE_COGNITO_REDIRECT_URI: "${VITE_COGNITO_REDIRECT_URI:-}",
  VITE_COGNITO_POST_LOGOUT_REDIRECT_URI: "${VITE_COGNITO_POST_LOGOUT_REDIRECT_URI:-}",
  VITE_EMR_URL: "${VITE_EMR_URL:-}",
  VITE_SUBSCRIPTION_KEY: "${VITE_SUBSCRIPTION_KEY:-}",
};
EOF

echo "Environment configuration generated successfully"
