import { Edit, ToggleOff, ToggleOn } from '@mui/icons-material';
import { GridActionsCellItem, GridColDef } from '@mui/x-data-grid';
import { format } from 'date-fns';

import StatusBadge from '../../components/Common/StatusBadge';
import { Template } from '../../types';

interface ColumnsConfig {
  onEdit: (template: Template) => void;
  onToggleStatus: (template: Template) => void;
}

export const getColumns = ({
  onEdit,
  onToggleStatus,
}: ColumnsConfig): GridColDef[] => [
  {
    field: 'name',
    headerName: 'Name',
    flex: 1,
    minWidth: 200,
  },
  {
    field: 'type',
    headerName: 'Type',
    flex: 1,
    minWidth: 150,
  },
  {
    field: 'status',
    headerName: 'Status',
    width: 120,
    renderCell: (params) => <StatusBadge status={params.row.status} />,
  },
  {
    field: 'createdAt',
    headerName: 'Created At',
    flex: 1,
    minWidth: 180,
    valueFormatter: (params) => format(new Date(params.value), 'PPpp'),
  },
  {
    field: 'actions',
    type: 'actions',
    headerName: 'Actions',
    width: 100,
    getActions: (params) => [
      <GridActionsCellItem
        icon={<Edit />}
        label='Edit'
        onClick={() => onEdit(params.row)}
        showInMenu
      />,
      <GridActionsCellItem
        icon={params.row.status === 'active' ? <ToggleOff /> : <ToggleOn />}
        label={params.row.status === 'active' ? 'Deactivate' : 'Activate'}
        onClick={() => onToggleStatus(params.row)}
        showInMenu
      />,
    ],
  },
];
