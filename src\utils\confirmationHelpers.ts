import { useConfirmation } from '../hooks/useConfirmation';

// Define the confirmation configuration type for better type safety
interface ConfirmationConfig {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  itemName?: string;
}

// Common confirmation configurations for different entity types
export const getDeleteConfirmation = (
  entityType: string,
  entityName: string
): ConfirmationConfig => ({
  title: `Delete ${entityType}`,
  message: `Are you sure you want to delete "${entityName}"? This action cannot be undone and will permanently remove all associated data.`,
  confirmText: 'Delete',
  cancelText: 'Cancel',
  type: 'danger' as const,
  itemName: entityName,
});

export const getBulkDeleteConfirmation = (
  entityType: string,
  count: number
): ConfirmationConfig => ({
  title: `Delete ${count} ${entityType}${count > 1 ? 's' : ''}`,
  message: `Are you sure you want to delete ${count} ${entityType.toLowerCase()}${count > 1 ? 's' : ''}? This action cannot be undone.`,
  confirmText: `Delete ${count} item${count > 1 ? 's' : ''}`,
  cancelText: 'Cancel',
  type: 'danger' as const,
});

export const getStatusChangeConfirmation = (
  entityType: string,
  entityName: string,
  newStatus: string
): ConfirmationConfig => ({
  title: `Change ${entityType} Status`,
  message: `Are you sure you want to change the status of "${entityName}" to ${newStatus}?`,
  confirmText: 'Change Status',
  cancelText: 'Cancel',
  type: 'warning' as const,
  itemName: entityName,
});

export const getArchiveConfirmation = (
  entityType: string,
  entityName: string
): ConfirmationConfig => ({
  title: `Archive ${entityType}`,
  message: `Are you sure you want to archive "${entityName}"? Archived items can be restored later.`,
  confirmText: 'Archive',
  cancelText: 'Cancel',
  type: 'warning' as const,
  itemName: entityName,
});

// Example usage patterns for different entities
export const useEntityConfirmations = () => {
  const confirmation = useConfirmation();

  const confirmDeleteUser = (userName: string, onConfirm: () => void): void => {
    const config = getDeleteConfirmation('User', userName);
    confirmation.showConfirmation(config, onConfirm);
  };

  const confirmDeleteRole = (roleName: string, onConfirm: () => void): void => {
    const config = getDeleteConfirmation('Role', roleName);
    confirmation.showConfirmation(config, onConfirm);
  };

  const confirmDeleteOrganization = (
    orgName: string,
    onConfirm: () => void
  ): void => {
    const config = getDeleteConfirmation('Organization', orgName);
    confirmation.showConfirmation(config, onConfirm);
  };

  const confirmBulkDelete = (
    entityType: string,
    count: number,
    onConfirm: () => void
  ): void => {
    const config = getBulkDeleteConfirmation(entityType, count);
    confirmation.showConfirmation(config, onConfirm);
  };

  const confirmStatusChange = (
    entityType: string,
    entityName: string,
    newStatus: string,
    onConfirm: () => void
  ): void => {
    const config = getStatusChangeConfirmation(
      entityType,
      entityName,
      newStatus
    );
    confirmation.showConfirmation(config, onConfirm);
  };

  const confirmArchive = (
    entityType: string,
    entityName: string,
    onConfirm: () => void
  ): void => {
    const config = getArchiveConfirmation(entityType, entityName);
    confirmation.showConfirmation(config, onConfirm);
  };

  return {
    ...confirmation,
    confirmDeleteUser,
    confirmDeleteRole,
    confirmDeleteOrganization,
    confirmBulkDelete,
    confirmStatusChange,
    confirmArchive,
  };
};
