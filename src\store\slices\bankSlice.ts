import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import { generateMockApiResponse, mockBanks } from '../../data/mockData';
import { Bank, PaginatedResponse } from '../../types';

interface BankState {
  banks: Bank[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: BankState = {
  banks: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

export const fetchBanks = createAsyncThunk(
  'banks/fetchBanks',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    let filteredBanks = mockBanks.filter(
      (bank) => bank.organizationId === params.organizationId
    );

    if (params.search) {
      filteredBanks = filteredBanks.filter(
        (bank) =>
          bank.bankName.toLowerCase().includes(params.search!.toLowerCase()) ||
          bank.accountHolderName
            .toLowerCase()
            .includes(params.search!.toLowerCase())
      );
    }

    if (params.status) {
      filteredBanks = filteredBanks.filter(
        (bank) => bank.status === params.status
      );
    }

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredBanks.slice(startIndex, endIndex);

    const response: PaginatedResponse<Bank> = {
      data: paginatedData,
      total: filteredBanks.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 500);
  }
);

export const createBank = createAsyncThunk(
  'banks/createBank',
  async (bankData: Omit<Bank, 'id' | 'createdAt'>) => {
    const newBank: Bank = {
      id: uuidv4(),
      ...bankData,
      createdAt: new Date(),
    };
    mockBanks.unshift(newBank);
    return await generateMockApiResponse(newBank, 800);
  }
);

export const updateBank = createAsyncThunk(
  'banks/updateBank',
  async ({ id, data }: { id: string; data: Partial<Bank> }) => {
    const index = mockBanks.findIndex((bank) => bank.id === id);
    if (index === -1) {
      throw new Error('Bank not found');
    }
    const updatedBank = { ...mockBanks[index], ...data } as Bank;
    mockBanks[index] = updatedBank;
    return await generateMockApiResponse(updatedBank, 800);
  }
);

const bankSlice = createSlice({
  name: 'banks',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchBanks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchBanks.fulfilled, (state, action) => {
        state.loading = false;
        state.banks = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchBanks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch banks';
      })
      .addCase(createBank.fulfilled, (state, action) => {
        state.banks.unshift(action.payload.data);
        state.total += 1;
      })
      .addCase(updateBank.fulfilled, (state, action) => {
        const index = state.banks.findIndex(
          (bank) => bank.id === action.payload.data.id
        );
        if (index !== -1) {
          state.banks[index] = action.payload.data;
        }
      });
  },
});

export default bankSlice.reducer;
