import React from 'react';

interface OrganizationRequiredProps {
  /** The feature/module name that requires organization selection */
  feature: string;
  /** Optional custom message instead of the default */
  customMessage?: string;
  /** Optional custom title instead of the default */
  customTitle?: string;
}

const OrganizationRequired: React.FC<OrganizationRequiredProps> = ({
  feature,
  customMessage,
  customTitle = 'Organization Required',
}) => {
  const defaultMessage = `Please select an organization from the header to view and manage ${feature}.`;

  return (
    <div className='flex flex-col items-center justify-center h-96 text-center p-8'>
      <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-8 max-w-md'>
        <h2 className='text-xl font-semibold text-gray-900 mb-4'>
          {customTitle}
        </h2>
        <p className='text-gray-600 mb-6'>{customMessage || defaultMessage}</p>
        <div className='text-sm text-gray-500'>
          Use the organization selector in the top navigation bar to get
          started.
        </div>
      </div>
    </div>
  );
};

export default OrganizationRequired;
