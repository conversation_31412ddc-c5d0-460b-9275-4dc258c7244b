import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';

import { PATHS } from '../constants/paths';
import { AppDispatch } from '../store';
import { fetchUserByEmail } from '../store/features/users/user.slice';
import { useAuth } from './useAuth';

/**
 * Hook to check subscription expiry and show modal from 7 days before expiry until today
 * Only shows for organization admin, admin, or organization super admin (not super admin)
 */
export const useSubscriptionExpiryModal = () => {
  const {
    user,
    isOrganizationAdmin,
    isSuperAdmin,
    isAuthenticated,
    isSubscriptionOrganization,
  } = useAuth();
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [daysRemaining, setDaysRemaining] = useState(0);

  useEffect(() => {
    // Check if popup was already shown this session
    const popupShownThisSession = sessionStorage.getItem(
      'subscriptionPopupShown'
    );
    if (popupShownThisSession === 'true') {
      return;
    }
    if (isSubscriptionOrganization()) {
      return;
    }

    // Only check if user is authenticated and is organization admin (not super admin)
    const shouldCheck =
      isAuthenticated && !isSuperAdmin && isOrganizationAdmin && !!user?.email;

    if (!shouldCheck) {
      return;
    }

    const checkSubscriptionExpiry = async () => {
      try {
        // Fetch the latest user data
        const result = await dispatch(fetchUserByEmail(user.email));

        const fetchedUser = fetchUserByEmail.fulfilled.match(result)
          ? result.payload
          : null;

        type ExpiryCarrier = {
          subscriptionExpiry?: string;
          subscriptionExpiryDate?: string;
        };

        const getExpiry = (u: unknown): string | undefined => {
          const candidate = u as ExpiryCarrier;
          return (
            candidate?.subscriptionExpiryDate ?? candidate?.subscriptionExpiry
          );
        };

        // Prefer auth-state user first, then fresh fetch as fallback
        const expirySource = getExpiry(user) ?? getExpiry(fetchedUser);

        if (expirySource) {
          const expiryDate = new Date(expirySource);
          const now = new Date();

          // Get current time to check if past midnight
          const currentHour = now.getHours();

          // Only check dates, not time
          now.setHours(0, 0, 0, 0); // Reset time to start of day
          expiryDate.setHours(0, 0, 0, 0); // Reset time to start of day

          // Calculate days until expiry
          const diffTime = expiryDate.getTime() - now.getTime();
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          // Show modal if subscription expires within 7 days (from 7 days before until today)
          // But only show "today" message if it's still before midnight
          // If expired today and past midnight, don't show popup
          if (diffDays > 0 && diffDays <= 7) {
            setDaysRemaining(diffDays);
            setShowModal(true);
            sessionStorage.setItem('subscriptionPopupShown', 'true');
          } else if (diffDays === 0 && currentHour < 24) {
            // Show "expires today" only until midnight
            setDaysRemaining(0);
            setShowModal(true);
            sessionStorage.setItem('subscriptionPopupShown', 'true');
          }
        }
      } catch {
        // ignore
      }
    };

    checkSubscriptionExpiry();
  }, [
    user?.email,
    user,
    isOrganizationAdmin,
    isSuperAdmin,
    isAuthenticated,
    isSubscriptionOrganization,
    dispatch,
  ]);

  const handleClose = () => {
    setShowModal(false);
  };

  const handleUpgrade = () => {
    navigate(PATHS.SUBSCRIPTION);
    setShowModal(false);
  };

  return {
    showModal,
    daysRemaining,
    onClose: handleClose,
    onUpgrade: handleUpgrade,
  };
};
