import React, { memo } from 'react';
import { Controller } from 'react-hook-form';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import { useRoleForm } from '../hooks/useRoleForm';
import { RoleFormSchema } from '../schemas/role.schema';
import { Role } from '../types/role.types';

interface RoleFormProps {
  onSubmit: (roleData: RoleFormSchema) => void | Promise<void>;
  role: Role | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const RoleForm: React.FC<RoleFormProps> = memo(
  ({ onSubmit, role, onSuccess, onCancel }) => {
    const {
      control,
      handleSubmit,
      errors,
      isSubmitting,
      isValid,
      isDirty,
      isSystemRole,
      isEditing,
    } = useRoleForm({
      role,
      onSubmit,
      onSuccess,
    });

    return (
      <div className='space-y-6 p-6'>
        <form onSubmit={handleSubmit} className='space-y-4'>
          <Controller
            name='name'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Role Name'
                error={!!errors.name}
                helperText={
                  errors.name?.message ||
                  (isSystemRole ? 'System role names cannot be modified' : '')
                }
                placeholder='Enter role name'
                required
                disabled={Boolean(isSystemRole)}
              />
            )}
          />
          <Controller
            name='description'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Description'
                error={!!errors.description}
                helperText={errors.description?.message}
                placeholder='Enter description'
                multiline
                rows={4}
              />
            )}
          />
          <div className='flex justify-end space-x-4 pt-4'>
            <AppButton
              type='button'
              kind='secondary'
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </AppButton>
            <AppButton
              type='submit'
              kind='primary'
              disabled={isSubmitting || !isValid || (!isDirty && isEditing)}
            >
              {isSubmitting
                ? 'Saving...'
                : isEditing
                  ? 'Save Changes'
                  : 'Create Role'}
            </AppButton>
          </div>
        </form>
      </div>
    );
  }
);

RoleForm.displayName = 'RoleForm';

export default RoleForm;
