import { useCallback, useEffect, useRef, useState } from 'react';

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Cache manager
class ApiCache {
  private cache = new Map<string, CacheEntry<any>>();
  private defaultTTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const expiresAt = now + (ttl || this.defaultTTL);

    this.cache.set(key, {
      data,
      timestamp: now,
      expiresAt,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check if expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.values());
    const now = Date.now();

    return {
      totalEntries: entries.length,
      validEntries: entries.filter((entry) => now <= entry.expiresAt).length,
      expiredEntries: entries.filter((entry) => now > entry.expiresAt).length,
      cacheSize: JSON.stringify(Array.from(this.cache.entries())).length,
    };
  }
}

// Global cache instance
const apiCache = new ApiCache();

// Request deduplication
const pendingRequests = new Map<string, Promise<any>>();

// Hook options
interface UseOptimizedApiOptions<T> {
  cacheKey?: string;
  cacheTTL?: number;
  enabled?: boolean;
  retryCount?: number;
  retryDelay?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
  deduplicate?: boolean;
}

// Hook return type
interface UseOptimizedApiReturn<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  clearCache: () => void;
}

// Main hook
export function useOptimizedApi<T>(
  apiCall: () => Promise<T>,
  options: UseOptimizedApiOptions<T> = {}
): UseOptimizedApiReturn<T> {
  const {
    cacheKey,
    cacheTTL,
    enabled = true,
    retryCount = 0,
    retryDelay = 1000,
    onSuccess,
    onError,
    deduplicate = true,
  } = options;

  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const mountedRef = useRef(true);
  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  // Generate cache key if not provided
  const finalCacheKey = cacheKey || apiCall.toString();

  // Clear cache function
  const clearCache = useCallback(() => {
    if (finalCacheKey) {
      apiCache.delete(finalCacheKey);
    }
  }, [finalCacheKey]);

  // Execute API call with optimizations
  const executeApiCall = useCallback(
    async (attempt = 0): Promise<void> => {
      if (!enabled || !mountedRef.current) return;

      // Check cache first
      if (finalCacheKey && apiCache.has(finalCacheKey)) {
        const cachedData = apiCache.get<T>(finalCacheKey);
        if (cachedData !== null) {
          setData(cachedData);
          setLoading(false);
          setError(null);
          onSuccess?.(cachedData);
          return;
        }
      }

      // Check for pending request (deduplication)
      if (deduplicate && pendingRequests.has(finalCacheKey)) {
        try {
          const result = await pendingRequests.get(finalCacheKey);
          if (mountedRef.current) {
            setData(result);
            setLoading(false);
            setError(null);
            onSuccess?.(result);
          }
          return;
        } catch (err) {
          // Handle error below
        }
      }

      setLoading(true);
      setError(null);

      try {
        // Create and store the promise for deduplication
        const apiPromise = apiCall();
        if (deduplicate) {
          pendingRequests.set(finalCacheKey, apiPromise);
        }

        const result = await apiPromise;

        if (mountedRef.current) {
          setData(result);
          setLoading(false);
          setError(null);

          // Cache the result
          if (finalCacheKey) {
            apiCache.set(finalCacheKey, result, cacheTTL);
          }

          onSuccess?.(result);
        }
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');

        // Retry logic
        if (attempt < retryCount) {
          retryTimeoutRef.current = setTimeout(
            () => {
              executeApiCall(attempt + 1);
            },
            retryDelay * Math.pow(2, attempt)
          ); // Exponential backoff
          return;
        }

        if (mountedRef.current) {
          setLoading(false);
          setError(error);
          onError?.(error);
        }
      } finally {
        // Clean up pending request
        if (deduplicate) {
          pendingRequests.delete(finalCacheKey);
        }
      }
    },
    [
      apiCall,
      enabled,
      finalCacheKey,
      cacheTTL,
      retryCount,
      retryDelay,
      onSuccess,
      onError,
      deduplicate,
    ]
  );

  // Refetch function
  const refetch = useCallback(async () => {
    clearCache();
    await executeApiCall();
  }, [executeApiCall, clearCache]);

  // Execute on mount and when dependencies change
  useEffect(() => {
    executeApiCall();
  }, [executeApiCall]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    data,
    loading,
    error,
    refetch,
    clearCache,
  };
}

// Hook for paginated data
export function useOptimizedPaginatedApi<T>(
  apiCall: (
    page: number,
    limit: number
  ) => Promise<{ data: T[]; total: number }>,
  initialPage = 1,
  initialLimit = 10,
  options: Omit<
    UseOptimizedApiOptions<{ data: T[]; total: number }>,
    'cacheKey'
  > = {}
) {
  const [page, setPage] = useState(initialPage);
  const [limit, setLimit] = useState(initialLimit);

  const cacheKey = `paginated-${apiCall.toString()}-${page}-${limit}`;

  const result = useOptimizedApi(() => apiCall(page, limit), {
    ...options,
    cacheKey,
  });

  const goToPage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const changeLimit = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  }, []);

  return {
    ...result,
    page,
    limit,
    goToPage,
    changeLimit,
    total: result.data?.total || 0,
    items: result.data?.data || [],
  };
}

// Cache management utilities
export const cacheUtils = {
  clear: () => apiCache.clear(),
  getStats: () => apiCache.getStats(),
  delete: (key: string) => apiCache.delete(key),
};
