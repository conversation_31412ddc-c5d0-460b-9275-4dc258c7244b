import { format } from 'date-fns';
import React from 'react';

import Modal from '../../../components/Common/Modal';
import { InvoiceDetailResponse } from '../../../store/features/invoices/invoice.service';
import { NormalizedInvoice } from '../../../store/features/invoices/invoice.slice';
import { formatRoleName } from '../../../utils/inputUtils';

interface PatientRegistrationBillModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: NormalizedInvoice | null;
  invoiceDetail: InvoiceDetailResponse | null;
  loading?: boolean;
}

const PatientRegistrationBillModal: React.FC<
  PatientRegistrationBillModalProps
> = ({ isOpen, onClose, invoice, invoiceDetail, loading = false }) => {
  // Get patient info from detail response or invoice
  const patientName =
    invoiceDetail?.patient?.name || invoice?.patientName || '';
  const patientId = invoiceDetail?.patientId || invoice?.patientId || '';
  const gender = invoiceDetail?.patient?.sex || invoice?.gender || '';

  // Calculate age from DOB or use age from response
  const getAge = (dob?: string, ageFromResponse?: string): string => {
    // First try to use age from response
    if (ageFromResponse && ageFromResponse !== '-') {
      return `${ageFromResponse} Y`;
    }

    if (!dob || dob === 'yyyy-08-Th') return '-';
    try {
      const birthDate = new Date(dob);
      if (Number.isNaN(birthDate.getTime())) return '-';
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < birthDate.getDate())
      ) {
        age--;
      }
      return `${age} Y`;
    } catch {
      return '-';
    }
  };

  const age = getAge(invoiceDetail?.patient?.dob, invoiceDetail?.patient?.age);

  // Format date
  const formatDate = (date: string | Date | null): string => {
    if (!date) return '-';
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (Number.isNaN(dateObj.getTime())) return '-';
      return format(dateObj, 'dd MMM yyyy');
    } catch {
      return '-';
    }
  };

  const billDate = invoiceDetail?.createdAt
    ? formatDate(invoiceDetail.createdAt)
    : invoice?.invoiceDate
      ? formatDate(invoice.invoiceDate)
      : '-';

  const doctorNameRaw =
    invoiceDetail?.doctor?.name ||
    invoiceDetail?.created_by_name ||
    invoice?.doctorName ||
    '-';
  const doctorName =
    doctorNameRaw !== '-' ? formatRoleName(doctorNameRaw) : '-';

  const amount =
    invoiceDetail?.amountInRupees !== undefined
      ? invoiceDetail.amountInRupees
      : invoice?.amount !== undefined
        ? invoice.amount
        : invoiceDetail?.amount !== undefined
          ? invoiceDetail.amount
          : 0;

  const paymentMode =
    invoiceDetail?.razorpayPaymentId || invoiceDetail?.razorpayOrderId
      ? 'Card'
      : invoice?.modeOfPayment || '-';

  const billedBy = invoiceDetail?.created_by_name || '-';
  const billedAt = invoiceDetail?.createdAt
    ? format(new Date(invoiceDetail.createdAt), 'h:mm a')
    : '-';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title='Patient Registration Bill'
      size='lg'
    >
      {loading ? (
        <div className='flex justify-center items-center py-8'>
          <div className='text-gray-500'>Loading...</div>
        </div>
      ) : (
        <div className='space-y-2'>
          <div className='flex flex-wrap gap-y-2 text-sm pt-4'>
            <div className='pr-8'>
              <span className='font-medium '>Patient Name : </span>
              <span className='font-normal'>
                {patientName} {gender ? `(${gender.charAt(0)})` : ''}
              </span>
            </div>
            <div className='pr-8'>
              <span className='font-medium '>Patient ID : </span>
              <span className='font-normal'>{patientId}</span>
            </div>
            {age !== '-' && (
              <div className='pr-8'>
                <span className='font-medium '>Age : </span>
                <span className='font-normal'>{age}</span>
              </div>
            )}
            <div className='pr-7'>
              <span className='font-medium '>Date : </span>
              <span className='font-normal'>{billDate}</span>
            </div>
            <div>
              <span className='font-medium '>Doctor's Name : </span>
              <span className='font-normal'>{doctorName}</span>
            </div>
          </div>

          <div className='border-t  border-gray-200 py-3'>
            <div className='flex items-center gap-2 pt-2 pb-6'>
              <span className='text-gray-700 font-semibold'>
                Registration Fee :
              </span>
              <span className='text-gray-900 font-semibold text-lg'>
                ₹{amount.toFixed(2)}
              </span>
            </div>
          </div>

          <div className='space-y-1 text-sm font-medium '>
            <div>Payment made through {paymentMode}</div>
            {billedBy !== '-' && billedAt !== '-' && (
              <div>
                Billed by {billedBy} at {billedAt}
              </div>
            )}
          </div>

          <div className='flex justify-end border-t border-gray-200 pt-4'>
            <div className='text-right'>
              <span className='text-gray-900 font-semibold text-lg'>
                Total Amount :{' '}
              </span>
              <span className='font-medium'>
                ₹
                {amount.toLocaleString('en-IN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </span>
            </div>
          </div>

          <div className='text-center text-sm text-gray-500 pt-4 border-t border-gray-200'>
            <div className='powered-by flex items-center justify-center gap-2'>
              <span>Powered By</span>
              <img
                src='/images/Vector.png'
                alt='Arca Logo'
                className='arca-logo'
                onError={(e) => {
                  const target = e.currentTarget;
                  target.style.display = 'none';
                  const textSpan = target.nextElementSibling as HTMLElement;
                  if (textSpan) {
                    textSpan.classList.remove('hidden');
                  }
                }}
              />
              <span className='arca-logo-text hidden'>ARCA</span>
            </div>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default PatientRegistrationBillModal;
