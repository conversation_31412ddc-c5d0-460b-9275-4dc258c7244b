/**
 * Defined role constants for the application.
 * Mapped internal roles used for logic and permissions.
 */
export const ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  ORGANIZATION_ADMIN: 'ORGANIZATION_ADMIN',
} as const;

/**
 * List of role names that are considered Organization Admins.
 * These are the various strings that could come from the API/Identity provider.
 */
export const ORG_ADMIN_ROLE_NAMES = [
  'ADMIN',
  'admin',
  'administrator',
  'ORGANIZATION_ADMIN',
  'organization admin',
  'organization super admin',
  'organization_super_admin',
];

/**
 * Check if a given role name string matches any of the recognized Organization Admin roles.
 * @param roleName - The role name to check.
 * @returns boolean - True if it's an organization admin role.
 */
export const isOrgAdminRole = (roleName?: string | null): boolean => {
  if (!roleName) return false;
  const normalized = roleName.trim().toLowerCase();
  return ORG_ADMIN_ROLE_NAMES.some((name) => name.toLowerCase() === normalized);
};

/**
 * List of role names that are considered Super Admins.
 */
export const SUPER_ADMIN_ROLE_NAMES = [
  'SUPER_ADMIN',
  'super admin',
  'superadmin',
];

/**
 * Check if a given role name string matches any of the recognized Super Admin roles.
 * @param roleName - The role name to check.
 * @returns boolean - True if it's a super admin role.
 */
export const isSuperAdminRole = (roleName?: string | null): boolean => {
  if (!roleName) return false;
  const normalized = roleName.trim().toLowerCase();
  return SUPER_ADMIN_ROLE_NAMES.some(
    (name) => name.toLowerCase() === normalized
  );
};

/**
 * Roles allowed to access the Admin Panel.
 */
export const ALLOWED_ADMIN_PANEL_ROLES: string[] = [
  ROLES.SUPER_ADMIN,
  ROLES.ADMIN,
  ROLES.ORGANIZATION_ADMIN,
];
/**
 * Check if a role name (internal format) is allowed to access the Admin Panel.
 * @param roleName - The mapped role name (e.g., 'SUPER_ADMIN')
 */
export const isAllowedAdminRole = (roleName: string): boolean => {
  return ALLOWED_ADMIN_PANEL_ROLES.includes(roleName);
};
