/**
 * Handles the onKeyDown event to allow only numeric input
 * @param e - The keyboard event
 */
export const handleNumericInput = (
  e: React.KeyboardEvent<HTMLInputElement>
) => {
  // Allow: backspace, delete, tab, escape, enter, decimal point, and numbers
  if (
    [46, 8, 9, 27, 13, 110, 190].includes(e.keyCode) ||
    // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
    ((e.ctrlKey || e.metaKey) && [65, 67, 86, 88].includes(e.keyCode)) ||
    // Allow: home, end, left, right
    (e.keyCode >= 35 && e.keyCode <= 39) ||
    // Allow: numpad numbers
    (e.keyCode >= 96 && e.keyCode <= 105) ||
    // Allow: numbers on top of keyboard
    (e.keyCode >= 48 && e.keyCode <= 57 && !e.shiftKey)
  ) {
    // Prevent minus key (keyCode 189 for main keyboard, 109 for numpad)
    if (e.keyCode === 189 || e.keyCode === 109) {
      e.preventDefault();
      return;
    }
    // Let it happen
    return;
  }

  // Prevent the default action for all other keys
  e.preventDefault();
};

/**
 * Formats the input value to allow only numbers and a single decimal point
 * @param value - The input value
 * @returns Formatted numeric string
 */
export const formatNumericInput = (value: string): string => {
  // Remove all non-numeric characters except decimal point
  let formatted = value.replace(/[^\d.]/g, '');

  // Remove all but the first decimal point
  const decimalIndex = formatted.indexOf('.');
  if (decimalIndex !== -1) {
    formatted =
      formatted.substring(0, decimalIndex + 1) +
      formatted.substring(decimalIndex).replace(/\./g, '');
  }

  return formatted;
};

/**
 * Capitalizes the first letter of each word in a role name
 * Handles underscores and spaces, converting them to spaces
 * @param roleName - The role name to format (e.g., "doctor", "DOCTOR", "super_admin")
 * @returns Formatted role name with first letter capitalized (e.g., "Doctor", "Super Admin")
 */
export const formatRoleName = (roleName: string | undefined | null): string => {
  if (!roleName || typeof roleName !== 'string') return '';
  const words = roleName.replace(/_/g, ' ').split(/\s+/);

  return words
    .map((word) => {
      if (!word) return '';
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(' ');
};

/**
 * Capitalizes the first letter of a string
 * @param str - The string to capitalize
 * @returns The string with the first letter capitalized
 */
export const capitalizeFirstLetter = (
  str: string | undefined | null
): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
};
