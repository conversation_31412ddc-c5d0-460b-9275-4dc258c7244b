import { ORGANIZATION_MEDICINES_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';
import {
  MedicineListItem,
  MedicineListParams,
  MedicineListResponse,
  UpdateMedicineRequest,
} from '../types/medicine.types';

const MEDICINE_ENDPOINTS = {
  LIST: `${ORGANIZATION_MEDICINES_ENDPOINT}/organization/medicines`,
  UPDATE: `${ORGANIZATION_MEDICINES_ENDPOINT}/organization/medicines/update`,
  REMOVE: `${ORGANIZATION_MEDICINES_ENDPOINT}/organization/medicines/remove`,
} as const;

export const fetchMedicinesList = async (
  params: MedicineListParams
): Promise<MedicineListResponse> => {
  const queryParams = new URLSearchParams();

  if (params.searchText) {
    queryParams.append('searchText', params.searchText.trim());
  }
  if (params.pageSize) {
    queryParams.append('pageSize', params.pageSize.toString());
  }
  if (params.page) {
    queryParams.append('page', params.page.toString());
  }
  if (params.organizationId) {
    queryParams.append('organizationId', params.organizationId);
  }
  if (params.isActive !== undefined) {
    queryParams.append('isActive', params.isActive.toString());
  }
  if (params.continuationToken) {
    queryParams.append('continuationToken', params.continuationToken);
  }

  const response = await api.get<{
    items: any[];
    continuationToken?: string;
    currentPage: number;
    hasMoreResults?: boolean;
    pageSize?: number;
    totalCount?: number;
    totalFetched?: number;
    totalPages?: number;
  }>(`${MEDICINE_ENDPOINTS.LIST}?${queryParams.toString()}`);

  const mappedMedicines: MedicineListItem[] =
    response.data.items?.map((item: any, index: number) => ({
      id: item.id || `medicine-${index}`,
      medicineId: item.id,
      name: item.productName || '',
      productName: item.productName || '',
      defaultCost: item.price || 0,
      price: item.price || 0,
      mrp: item.mrp || 0,
      quantity: item.qty || '',
      qty: item.qty || '',
      form: item.productForm || '',
      productForm: item.productForm || '',
      type: item.medicineType || '',
      medicineType: item.medicineType || '',
      description: item.description || '',
      createdAt: item.createdAt || new Date().toISOString(),
      updatedAt: item.updatedAt || new Date().toISOString(),
      isActive: item.isActive !== undefined ? item.isActive : true,
      organizationPrice: item.organizationCost || 0,
      organizationMedicineId: item.organizationMedicineId,
    })) || [];

  return {
    medicines: mappedMedicines,
    totalRecords: response.data.totalCount || 0,
    currentPage: response.data.currentPage || 1,
    totalPages: response.data.totalPages || 1,
    continuationToken: response.data.continuationToken ?? undefined,
    hasMoreResults: response.data.hasMoreResults ?? undefined,
    pageSize: response.data.pageSize ?? undefined,
    totalFetched: response.data.totalFetched ?? undefined,
  };
};

export const updateMedicines = async (
  data: UpdateMedicineRequest
): Promise<{ success: boolean; message: string }> => {
  const response = await api.post<{ success: boolean; message: string }>(
    MEDICINE_ENDPOINTS.UPDATE,
    data
  );
  return response.data;
};

export const removeMedicines = async (data: {
  organizationId: string;
  medicines?: string[];
  selectAll?: boolean;
}): Promise<{ success: boolean; message: string }> => {
  const response = await api.post<{ success: boolean; message: string }>(
    MEDICINE_ENDPOINTS.REMOVE,
    data
  );
  return response.data;
};

export const medicineService = {
  fetchMedicinesList,
  updateMedicines,
  removeMedicines,
};

export default medicineService;
