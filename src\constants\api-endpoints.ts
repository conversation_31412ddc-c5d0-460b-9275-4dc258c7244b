// Base API URLs from environment variables
export const API_ENDPOINT =
  (typeof window !== 'undefined' ? (window as any)._env_?.VITE_API_URL : '') ||
  import.meta.env.VITE_API_URL;
export const SUBSCRIPTION_KEY =
  (typeof window !== 'undefined'
    ? (window as any)._env_?.VITE_SUBSCRIPTION_KEY
    : '') || import.meta.env.VITE_SUBSCRIPTION_KEY;

export const AUTH_ENDPOINT = '/auth/v0.1/auth';
export const USERS_ENDPOINT = '';
export const ORGANIZATIONS_ENDPOINT = '';
export const ROLES_ENDPOINT = '/role';
export const PERMISSIONS_ENDPOINT = '/permission';
export const DEPARTMENTS_ENDPOINT = '/departments';
export const BRANCHES_ENDPOINT = '/branches';
export const BANKS_ENDPOINT = '/banks';
export const LANGUAGES_ENDPOINT = '/languages';
export const VITALS_ENDPOINT = '/vitals';
export const TEMPLATES_ENDPOINT = '/templates';
export const AUDIT_ENDPOINT = '/audit';
export const LAB_TEST_ENDPOINT = '/lab-test';
export const LAB_TEST_DEPARTMENT_ENDPOINT = '/lab-test';
export const EMR_USER_INFO_ENDPOINT = '/EMR-MS/api/v0.1/user';
export const ORGANIZATION_MEDICINES_ENDPOINT = '';
export const DOCTOR_PROFILE_ENDPOINT = '/EMR-MS/api/v0.1/doctor';
// export const ORGANIZATION_MEDICINES_ENDPOINT = '/organization/medicines';
export const PATIENTS_ENDPOINT = '/organization';
export const SUBSCRIPTION_PLAN_ENDPOINT = '/subscription-plan';
export const ORGANIZATION_PLAN_ENDPOINT = '/organization-plan';
export const SUBSCRIPTION_FEATURE_ENDPOINT = 'feature';
export const SUBSCRIPTION_ORG_FEATURES_ENDPOINT =
  '/subscription/v0.1/subscription/features';
export const SUBSCRIBERS_ENDPOINT = '/subscriber';
export const PATIENT_PROFILE_ENDPOINT =
  '/subscription/v0.1/invoice/patient-profile';
export const DOCTOR_PROFILE_PICTURE_ENDPOINT =
  '/doctor/v0.1/doctor/profile-picture/url';
