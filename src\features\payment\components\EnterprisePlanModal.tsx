import CloseIcon from '@mui/icons-material/Close';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from '@mui/material';
import React, { useEffect, useRef, useState } from 'react';
import ReactQuill from 'react-quill';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import { useToast } from '../../../contexts/ToastContext';
import {
  SubscriptionPlan,
  updateOrganizationPlan,
} from '../../../store/features/subscription/subscription.service';
import {
  applyListItemStyles,
  defaultQuillFormats,
  defaultQuillModules,
  normalizeListFormatting,
} from '../../../utils/richText';

interface EnterprisePlanModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan?: SubscriptionPlan | null;
  onRefreshPlan?: () => Promise<void> | void;
  isRefreshingPlan?: boolean;
}

const EnterprisePlanModal: React.FC<EnterprisePlanModalProps> = ({
  isOpen,
  onClose,
  plan,
  onRefreshPlan,
  isRefreshingPlan,
}) => {
  const [planName, setPlanName] = useState('');
  const [description, setDescription] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { success, error: showError } = useToast();
  const isBusy = isLoading || !!isRefreshingPlan;
  const quillRef = useRef<ReactQuill | null>(null);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setPlanName('');
      setDescription('');
      setIsLoading(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) return;
    if (isRefreshingPlan) return;

    if (plan) {
      setPlanName(plan.planName || '');
      const initialDescription =
        typeof plan.description === 'string' ? plan.description : '';
      setDescription(initialDescription);
    } else {
      setPlanName('');
      setDescription('');
    }
  }, [isOpen, plan, isRefreshingPlan]);

  useEffect(() => {
    if (!isOpen) return;
    const editor = quillRef.current?.getEditor?.();
    if (!editor) return;
    applyListItemStyles(editor.root);
  }, [description, isOpen]);

  // Make images selectable
  useEffect(() => {
    if (!isOpen) return;
    const editor = quillRef.current?.getEditor?.();
    if (!editor) return;

    const root = editor.root;
    const handleImageClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'IMG') {
        e.preventDefault();
        e.stopPropagation();

        // Find the index of the image by creating a range before it
        const range = document.createRange();
        range.setStartBefore(target);
        range.collapse(true);

        // Get the selection and find the index
        const selection = window.getSelection();
        if (selection) {
          selection.removeAllRanges();
          selection.addRange(range);
        }

        // Use Quill's selection to get the index
        setTimeout(() => {
          const quillSelection = editor.getSelection();
          if (quillSelection) {
            editor.setSelection(quillSelection.index, 1, 'user');
          } else {
            // Fallback: find index by counting text nodes before the image
            let index = 0;
            let node: Node | null = root.firstChild;
            while (node && node !== target.parentNode) {
              if (node.nodeType === Node.TEXT_NODE) {
                index += (node.textContent || '').length;
              } else if (node.nodeType === Node.ELEMENT_NODE) {
                const element = node as Element;
                if (element.tagName === 'IMG') {
                  index += 1;
                } else {
                  index += (element.textContent || '').length;
                }
              }
              node = node.nextSibling;
            }
            editor.setSelection(index, 1, 'user');
          }
        }, 0);
      }
    };

    root.addEventListener('click', handleImageClick, true);

    return () => {
      root.removeEventListener('click', handleImageClick, true);
    };
  }, [isOpen, description]);

  useEffect(() => {
    if (!isOpen) return;

    let attempts = 0;
    const applyToolbarLabels = () => {
      const toolbar = (
        quillRef.current?.getEditor?.()?.getModule('toolbar') as {
          container?: HTMLElement;
        } | null
      )?.container;
      if (!toolbar) {
        if (attempts < 5) {
          attempts += 1;
          setTimeout(applyToolbarLabels, 60);
        }
        return;
      }

      // Add tooltips for size picker
      toolbar
        .querySelectorAll<HTMLElement>(
          '.ql-picker.ql-size .ql-picker-label, .ql-picker.ql-size .ql-picker-item'
        )
        .forEach((el) => {
          let value = el.getAttribute('data-value');
          if (!value && el.classList.contains('ql-picker-label')) {
            value =
              toolbar
                .querySelector<HTMLElement>(
                  '.ql-picker.ql-size .ql-picker-item.ql-selected'
                )
                ?.getAttribute('data-value') || null;
          }
          if (!value) return;
          const numeric = value.endsWith('px')
            ? value.replace('px', '')
            : value;
          el.setAttribute('data-label', numeric);
          el.setAttribute('aria-label', `${numeric} font size`);
          el.setAttribute('title', `Font size: ${numeric}px`);
        });

      // Add tooltips for all toolbar buttons
      const tooltips: Record<string, string> = {
        'ql-bold': 'Bold',
        'ql-italic': 'Italic',
        'ql-underline': 'Underline',
        'ql-strike': 'Strikethrough',
        'ql-link': 'Insert Link',
        'ql-image': 'Insert Image',
        'ql-clean': 'Clear Formatting',
      };

      Object.entries(tooltips).forEach(([className, tooltip]) => {
        const button = toolbar.querySelector<HTMLElement>(`.${className}`);
        if (button && !button.getAttribute('title')) {
          button.setAttribute('title', tooltip);
          button.setAttribute('aria-label', tooltip);
        }
      });

      // Add tooltips for list buttons (bullet and ordered list)
      let listAttempts = 0;
      const addListTooltips = () => {
        // Try multiple selectors to catch all possible button structures
        const listSelectors = [
          'button.ql-list[data-value="ordered"]',
          'button.ql-list[data-value="bullet"]',
          '.ql-list[data-value="ordered"]',
          '.ql-list[data-value="bullet"]',
          'button[class*="ql-list"][data-value="ordered"]',
          'button[class*="ql-list"][data-value="bullet"]',
        ];

        let foundOrdered = false;
        let foundBullet = false;

        listSelectors.forEach((selector) => {
          const buttons = toolbar.querySelectorAll<HTMLElement>(selector);
          buttons.forEach((button) => {
            const value = button.getAttribute('data-value');
            if (value === 'ordered' && !button.getAttribute('title')) {
              button.setAttribute('title', 'Numbered List');
              button.setAttribute('aria-label', 'Numbered List');
              foundOrdered = true;
            } else if (value === 'bullet' && !button.getAttribute('title')) {
              button.setAttribute('title', 'Bullet List');
              button.setAttribute('aria-label', 'Bullet List');
              foundBullet = true;
            }
          });
        });

        // Also try finding by iterating through all buttons
        if (!foundOrdered || !foundBullet) {
          const allButtons = toolbar.querySelectorAll<HTMLElement>('button');
          allButtons.forEach((button) => {
            if (button.classList.contains('ql-list')) {
              const value = button.getAttribute('data-value');
              if (value === 'ordered' && !button.getAttribute('title')) {
                button.setAttribute('title', 'Numbered List');
                button.setAttribute('aria-label', 'Numbered List');
                foundOrdered = true;
              } else if (value === 'bullet' && !button.getAttribute('title')) {
                button.setAttribute('title', 'Bullet List');
                button.setAttribute('aria-label', 'Bullet List');
                foundBullet = true;
              }
            }
          });
        }

        // If buttons still not found, retry after a short delay
        if ((!foundOrdered || !foundBullet) && listAttempts < 10) {
          listAttempts += 1;
          setTimeout(addListTooltips, 100);
        }
      };

      addListTooltips();

      // Add tooltips for pickers
      const pickerTooltips: Record<string, string> = {
        'ql-header': 'Text Heading',
        'ql-size': 'Font Size',
        'ql-color': 'Text Color',
        'ql-background': 'Background Color',
        'ql-list': 'List',
        'ql-align': 'Text Alignment',
      };

      Object.entries(pickerTooltips).forEach(([className, tooltip]) => {
        const picker = toolbar.querySelector<HTMLElement>(
          `.ql-picker.${className}`
        );
        if (picker) {
          const label = picker.querySelector<HTMLElement>('.ql-picker-label');
          if (label && !label.getAttribute('title')) {
            label.setAttribute('title', tooltip);
            label.setAttribute('aria-label', tooltip);
          }
        }
      });
    };

    applyToolbarLabels();
  }, [isOpen]);

  const handleSave = async () => {
    if (!planName.trim()) {
      showError('Validation Error', 'Plan name is required');
      return;
    }

    setIsLoading(true);

    try {
      const cleanedDescription = normalizeListFormatting(description);
      await updateOrganizationPlan({
        planName: planName.trim(),
        description: cleanedDescription,
      });
      if (onRefreshPlan) {
        await onRefreshPlan();
      }
      setDescription(cleanedDescription);
      success('Success', 'Organization plan updated successfully');
      onClose();
    } catch (err: unknown) {
      const errorMessage =
        (err as { response?: { data?: { message?: string } } })?.response?.data
          ?.message ||
        (err as { message?: string })?.message ||
        'An error occurred while updating the Organization plan';
      showError('Error', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={() => {
        if (!isBusy) {
          onClose();
        }
      }}
      maxWidth='sm'
      fullWidth
      scroll='body'
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mt: 1,
          pr: 3,
          pl: 3,
          borderBottom: '1px solid #e0e0e0',
        }}
      >
        <span style={{ fontWeight: 600, fontSize: 20 }}>
          Edit Organisation Plan
        </span>
        <IconButton onClick={onClose} size='small' disabled={isBusy}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          maxHeight: 'calc(100vh - 250px)',
          overflowY: 'auto',
          paddingTop: 3,
          paddingBottom: 4,
          pr: 3,
          pl: 3,
        }}
      >
        <div className='space-y-4 font-sans'>
          <div className='mt-3 grid grid-cols-1 gap-4'>
            <TextInput
              label='Plan Name'
              placeholder='Enter Plan Name'
              value={planName}
              onChange={(e) => setPlanName(e.target.value)}
              required
              disabled={isBusy}
              className='mt-2'
            />

            <div>
              <label className='block text-sm font-medium text-gray-700 mb-2'>
                Description
              </label>
              <div className='quill-wrapper' style={{ height: '400px' }}>
                <ReactQuill
                  ref={quillRef}
                  theme='snow'
                  value={description}
                  onChange={(value) => {
                    setDescription(value);
                    const editorInstance = quillRef.current?.getEditor?.();
                    if (editorInstance) {
                      applyListItemStyles(editorInstance.root);
                    }
                  }}
                  placeholder='Enter description'
                  readOnly={isBusy}
                  modules={defaultQuillModules}
                  formats={defaultQuillFormats}
                />
              </div>
              <style>{`
                .quill-wrapper .ql-toolbar.ql-snow {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 4px;
                  padding: 6px 8px;
                  row-gap: 6px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-formats {
                  margin: 0;
                  display: flex;
                  align-items: center;
                  gap: 4px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker,
                .quill-wrapper .ql-toolbar.ql-snow button {
                  height: 28px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker {
                  min-width: auto;
                  border: 1px solid rgba(15, 23, 42, 0.15);
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-header .ql-picker-label,
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-header .ql-picker-item {
                  padding: 0;
                  border-radius: 4px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-header .ql-picker-label::after,
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size .ql-picker-label::after {
                  margin-left: 1px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size .ql-picker-label,
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size .ql-picker-item {
                  line-height: 24px;
                  padding: 0 2px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  column-gap: 1px;
                }
                .quill-wrapper .ql-toolbar.ql-snow .ql-picker.ql-size {
                  margin-left: 2px;
                }
                .quill-wrapper .ql-container {
                  height: calc(400px - 62px) !important;
                }
                .quill-wrapper .ql-editor {
                  min-height: calc(400px - 70px) !important;
                }
                .quill-wrapper .ql-editor ul,
                .quill-wrapper .ql-editor ol {
                  margin: 0.5rem 0;
                  padding-left: 1.25rem;
                  list-style-position: outside;
                }
                .quill-wrapper .ql-editor ul {
                  list-style-type: disc;
                }
                .quill-wrapper .ql-editor ol {
                  list-style-type: decimal;
                }
                .quill-wrapper .ql-editor ul li,
                .quill-wrapper .ql-editor ol li {
                  list-style-type: inherit;
                  margin-bottom: 0.25rem;
                }
                .quill-wrapper .ql-editor ul li::before,
                .quill-wrapper .ql-editor ol li::before {
                  content: none;
                }
                .quill-wrapper .ql-editor ul li::marker,
                .quill-wrapper .ql-editor ol li::marker {
                  color: inherit;
                  font-size: inherit;
                  font-weight: inherit;
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
                  content: '12';
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
                  content: '14';
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
                  content: '16';
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
                  content: '18';
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
                  content: '24';
                }
                .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
                .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
                  content: '32';
                }
                .quill-wrapper .ql-toolbar .ql-direction,
                .quill-wrapper .ql-toolbar button[aria-label*="Tx"],
                .quill-wrapper .ql-toolbar button[title*="Tx"],
                .quill-wrapper .ql-toolbar .ql-picker[data-label*="Tx"] {
                  display: none !important;
                }
                .quill-wrapper .ql-editor img {
                  cursor: pointer;
                  user-select: auto;
                  -webkit-user-select: auto;
                  -moz-user-select: auto;
                  -ms-user-select: auto;
                  display: inline-block;
                  max-width: 100%;
                  height: auto;
                  margin: 0.5rem 0;
                  border: 2px solid transparent;
                  border-radius: 4px;
                  transition: border-color 0.2s ease;
                }
                .quill-wrapper .ql-editor img:hover {
                  border-color: #3b82f6;
                  outline: 2px solid rgba(59, 130, 246, 0.2);
                }
                .quill-wrapper .ql-editor img.ql-selected,
                .quill-wrapper .ql-editor .ql-image.ql-selected img {
                  border-color: #3b82f6;
                  outline: 2px solid rgba(59, 130, 246, 0.4);
                  background-color: rgba(59, 130, 246, 0.05);
                }
                .quill-wrapper .ql-editor .ql-image {
                  display: inline-block;
                  position: relative;
                }
              `}</style>
            </div>
          </div>
        </div>
      </DialogContent>
      <DialogActions
        sx={{
          borderTop: '1px solid #e0e0e0',
          px: 3,
          py: 2,
          gap: 2,
          justifyContent: 'flex-end',
        }}
      >
        <AppButton
          kind='secondary'
          onClick={onClose}
          disabled={isBusy}
          sx={{ minWidth: 160 }}
        >
          Cancel
        </AppButton>

        <AppButton
          onClick={handleSave}
          disabled={isBusy}
          sx={{ minWidth: 160 }}
        >
          {isLoading ? 'Updating...' : 'Update'}
        </AppButton>
      </DialogActions>
    </Dialog>
  );
};

export default EnterprisePlanModal;
