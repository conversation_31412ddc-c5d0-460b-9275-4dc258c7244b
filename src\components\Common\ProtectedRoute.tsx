import { useMsal } from '@azure/msal-react';
import React, { useEffect, useState } from 'react';
import { Navigate, Outlet, useLocation, useNavigate } from 'react-router-dom';

import { PATHS } from '../../constants/paths';
import { useAuth } from '../../hooks/useAuth';
import LoadingSpinner from './LoadingSpinner';

const ProtectedRoute: React.FC = () => {
  const { isAuthenticated, loading } = useAuth();
  const { instance, inProgress } = useMsal();
  const location = useLocation();
  const navigate = useNavigate();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    const checkAuth = async () => {
      try {
        // If MSAL is still initializing, wait
        if (inProgress !== 'none') {
          return;
        }

        // Check if we have an active account
        const account = instance.getActiveAccount();

        // If we have an active account but not authenticated yet, wait a bit
        if (account && !isAuthenticated) {
          timer = setTimeout(() => {
            setIsCheckingAuth(false);
          }, 1000);
          return;
        }

        // If no active account but we're on a protected route, redirect to login
        if (!account && !isAuthenticated) {
          // Store the current path for redirect after login
          sessionStorage.setItem('preLoginUrl', location.pathname);
          navigate(PATHS.LOGIN, { replace: true });
          return;
        }

        setIsCheckingAuth(false);
      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsCheckingAuth(false);
      }
    };

    checkAuth();

    // Cleanup function
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [isAuthenticated, inProgress, instance, location.pathname, navigate]);

  // Show loading spinner while checking auth state
  if (loading || isCheckingAuth || inProgress !== 'none') {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <LoadingSpinner size='lg' />
      </div>
    );
  }

  // If not authenticated, redirect to login with return location
  if (!isAuthenticated) {
    return <Navigate to={PATHS.LOGIN} state={{ from: location }} replace />;
  }

  // If authenticated, render the protected route
  return <Outlet />;
};

export default ProtectedRoute;
