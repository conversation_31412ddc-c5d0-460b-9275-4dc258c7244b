import { toast } from 'react-toastify';

import authService from '../store/features/auth/auth.service';
import {
  cognitoLogin,
  cognitoLogout,
  getCognitoIdToken,
  getCognitoUserInfo,
  isCognitoAuthenticated,
  isTokenExpired,
  refreshCognitoToken,
} from './cognitoAuth';

export async function login() {
  try {
    await cognitoLogin();
  } catch (error) {
    console.error('Login error:', error);
    toast.error('Login failed. Please try again.');
  }
}

export async function logout() {
  try {
    await cognitoLogout();
    localStorage?.clear();
  } catch (error) {
    console.error('Logout error:', error);
    toast.error('Logout failed');
  }
}

export async function getToken(): Promise<string> {
  try {
    // Check if authenticated with Cognito
    if (isCognitoAuthenticated()) {
      if (isTokenExpired()) {
        console.log('getToken: Cognito token expired, attempting refresh');
        const refreshed = await refreshCognitoToken();
        if (!refreshed) {
          console.error('getToken: Cognito refresh failed');
          throw new Error('Authentication expired and refresh failed');
        }
      }

      const cognitoToken = getCognitoIdToken();
      if (cognitoToken) {
        return cognitoToken;
      }
    }

    throw new Error('User is not authenticated with Cognito');
  } catch (error) {
    console.error('getToken: Error getting access token:', {
      error,
      message: error instanceof Error ? error.message : 'Unknown error',
    });
    // Throw error so request interceptor catch block can handle it (redirect to login)
    throw error;
  }
}

export function getActiveAccount() {
  try {
    const cognitoUserInfo = getCognitoUserInfo();
    if (cognitoUserInfo) {
      return cognitoUserInfo;
    } else {
      // Since we're only using Cognito, throw error if auth provider is not cognito
      throw new Error('Only Cognito authentication is supported');
    }
  } catch (error) {
    console.error('Error getting active account:', error);
    // Throw error so request interceptor catch block can handle it (redirect to login)
    throw error;
  }
}

// This function is now a wrapper around the auth service's fetchEmrUserInfo
export async function fetchUserInfo() {
  try {
    // Get the active account from Cognito
    const userInfo = getActiveAccount();
    if (!userInfo?.email) {
      throw new Error('No active account found');
    }

    // Use the auth service to fetch EMR user info
    const { user, token } = await authService.fetchEmrUserInfo(userInfo.email);

    return {
      token,
      user,
    };
  } catch (error) {
    console.error('Error in fetchUserInfo:', error);

    // Clear token on error to force re-authentication
    localStorage.removeItem('token');
    sessionStorage.removeItem('cognito.accessToken');
    sessionStorage.removeItem('cognito.idToken');

    throw error;
  }
}
