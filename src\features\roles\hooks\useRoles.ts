import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuth } from '../../../hooks/useAuth';
import { AppDispatch, RootState } from '../../../store';
import {
  CreateRoleData,
  RoleParams,
  UpdateRoleData,
} from '../../../store/features/roles/role.service';
import {
  clearMessages,
  clearUpdateSuccess,
  createRole,
  deleteRole,
  fetchRoles,
  resetFilters,
  setPage,
  setPageSize,
  setSearchName,
  updateRole,
} from '../../../store/features/roles/role.slice';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';
import { RoleFilters } from '../types/role.types';

export const useRoles = (organizationId?: string | null) => {
  const dispatch = useDispatch<AppDispatch>();

  // Get organizationId from parameter or localStorage
  const currentOrgId = organizationId ?? getCurrentOrganizationId();
  const { selectedOrganization } = useAuth();

  const {
    roles,
    loading,
    updating,
    updateSuccess,
    total,
    totalPages,
    page,
    limit,
    searchName,
    error,
    errorMessage,
    successMessage,
  } = useSelector((state: RootState) => state.roles);

  // Memoized selectors
  const rolesList = useMemo(() => roles, [roles]);
  const isLoading = useMemo(() => loading, [loading]);
  const isUpdating = useMemo(() => updating, [updating]);

  // Debounce timer ref
  const debounceTimerRef = useRef<NodeJS.Timeout>();

  // Debounced dispatch function
  const debouncedDispatch = useCallback(
    (params: RoleParams) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        dispatch(fetchRoles(params));
      }, 300); // 300ms debounce delay
    },
    [dispatch]
  );

  // Fetch roles with current filters
  const fetchRolesList = useCallback(
    (filters?: Partial<RoleFilters>, forDropdown?: boolean) => {
      const orgId = selectedOrganization?.id || currentOrgId;

      // Don't make API call if no organization is selected
      // This prevents unnecessary API calls when super admin hasn't selected an organization
      if (!orgId) {
        return;
      }

      let params: RoleParams;
      if (forDropdown) {
        params = { organizationId: orgId, pageSize: 10000 }; // Fetch all roles for dropdown
      } else {
        params = {
          page,
          pageSize: limit,
          searchText: filters?.search || searchName,
          organizationId: orgId,
        };
      }

      // Use debounced dispatch instead of direct dispatch
      debouncedDispatch(params);
    },
    [
      debouncedDispatch,
      currentOrgId,
      selectedOrganization,
      page,
      limit,
      searchName,
    ]
  );

  // Create role
  const handleCreateRole = useCallback(
    async (data: CreateRoleData) => {
      if (!currentOrgId) throw new Error('Organization ID is required');

      const result = await dispatch(
        createRole({ ...data, organizationId: currentOrgId })
      );
      return result;
    },
    [dispatch, currentOrgId]
  );

  // Update role
  const handleUpdateRole = useCallback(
    async (data: UpdateRoleData) => {
      if (!currentOrgId) throw new Error('Organization ID is required');

      const updateData = { ...data, organizationId: currentOrgId };
      const result = await dispatch(updateRole(updateData));
      return result;
    },
    [dispatch, currentOrgId]
  );

  // Delete role
  const handleDeleteRole = useCallback(
    async (id: string) => {
      const result = await dispatch(deleteRole(id));
      return result;
    },
    [dispatch]
  );

  // Search roles with debouncing
  const handleSearch = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchName(searchTerm));
      // Search is automatically debounced through fetchRolesList
      fetchRolesList({ search: searchTerm });
    },
    [dispatch, fetchRolesList]
  );

  // Pagination
  const handlePageChange = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const handlePageSizeChange = useCallback(
    (newPageSize: number) => {
      dispatch(setPageSize(newPageSize));
    },
    [dispatch]
  );

  // Clear messages
  const handleClearMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  const handleClearUpdateSuccess = useCallback(() => {
    dispatch(clearUpdateSuccess());
  }, [dispatch]);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    dispatch(resetFilters());
  }, [dispatch]);

  // Auto-fetch roles when organizationId changes (for organization selection changes)
  // This will trigger when super admin changes organization selection
  useEffect(() => {
    // Clear any existing messages when organization changes
    handleClearMessages();

    // Only fetch if we have a valid organization context change
    const orgId = selectedOrganization?.id || currentOrgId;
    if (orgId) {
      fetchRolesList();
    }

    // Cleanup function to clear messages and cancel pending debounced calls
    return () => {
      handleClearMessages();
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [
    fetchRolesList,
    selectedOrganization?.id,
    currentOrgId,
    handleClearMessages,
  ]);

  return {
    // Data
    roles: rolesList,
    total,
    totalPages,
    page,
    limit,
    searchName,

    // States
    loading: isLoading,
    updating: isUpdating,
    updateSuccess,
    error,
    errorMessage,
    successMessage,

    // Actions
    fetchRoles: fetchRolesList,
    createRole: handleCreateRole,
    updateRole: handleUpdateRole,
    deleteRole: handleDeleteRole,
    search: handleSearch,
    changePage: handlePageChange,
    changePageSize: handlePageSizeChange,
    clearMessages: handleClearMessages,
    clearUpdateSuccess: handleClearUpdateSuccess,
    resetFilters: handleResetFilters,
  };
};
