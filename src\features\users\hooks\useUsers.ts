import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { useAuth } from '../../../hooks/useAuth';
import { AppDispatch, RootState } from '../../../store';
import {
  CreateUserData,
  UpdateUserData,
} from '../../../store/features/users/user.service';
import {
  clearMessages,
  clearUpdateSuccess,
  createUser,
  deleteUser,
  fetchUsers,
  resetFilters,
  resetUserPassword,
  setPage,
  setPageSize,
  setSearchName,
  setStatusFilter,
  updateUser,
} from '../../../store/features/users/user.slice';
import { UserFilters } from '../types/user.types';

export const useUsers = () => {
  const dispatch = useDispatch<AppDispatch>();
  const location = useLocation();

  // Get organizationId from parameter or localStorage
  // For super admin: organizationId can be null (show all) or a specific org ID
  // For non-super admin: always use the organization ID from auth
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  // For super admins, be more defensive about organization selection
  // Don't allow "null" (all organizations) unless explicitly intended
  const currentOrgId = isSuperAdmin
    ? selectedOrganization?.id || null
    : isOrganizationAdmin
      ? userOrganizationId || null
      : null;

  // Check if we should prevent API calls during organization context establishment
  const shouldPreventApiCalls =
    isSuperAdmin &&
    !selectedOrganization &&
    localStorage.getItem('selectedOrganization'); // If there's a saved org but not loaded yet
  const {
    users,
    loading,
    updating,
    updateSuccess,
    total,
    totalPages,
    page,
    limit,
    searchName,
    statusFilter,
    error,
    errorMessage,
    successMessage,
  } = useSelector((state: RootState) => state.users);

  // Memoized selectors
  const usersList = useMemo(() => users, [users]);
  const isLoading = useMemo(() => loading, [loading]);
  const isUpdating = useMemo(() => updating, [updating]);

  // Clear messages when component unmounts or when location changes
  useEffect(() => {
    return () => {
      // This will run when the component unmounts or when location changes
      dispatch(clearMessages());
    };
  }, [dispatch, location.pathname]);

  // Track if this is the initial render
  const isInitialRender = useRef(true);
  const prevFetchParams = useRef<Record<string, unknown> | null>(null);
  const prevOrgId = useRef<string | null | undefined>(currentOrgId);

  // Memoize the fetch params to prevent unnecessary API calls
  const fetchParams = useMemo(() => {
    // Prevent API calls if we're waiting for organization context to be established
    if (shouldPreventApiCalls) {
      return null;
    }

    const params: {
      page: number;
      pageSize: number;
      search?: string;
      organizationId?: string | null;
      isActive?: boolean;
    } = {
      page,
      pageSize: limit,
      search: searchName,
    };

    // For non-super admin, always include organization ID
    // For super admin, include organization ID only if explicitly provided
    if (!isSuperAdmin) {
      if (!currentOrgId) {
        return null;
      }
      params.organizationId = currentOrgId;
    } else if (currentOrgId) {
      params.organizationId = currentOrgId;
    }

    // Add status filter if provided
    if (statusFilter && statusFilter !== 'all') {
      params.isActive = statusFilter === 'active';
    }

    return params;
  }, [
    currentOrgId,
    page,
    limit,
    searchName,
    statusFilter,
    isSuperAdmin,
    shouldPreventApiCalls,
  ]);

  useEffect(() => {
    if (isInitialRender.current) {
      prevOrgId.current = currentOrgId;
      return;
    }

    if (prevOrgId.current !== currentOrgId) {
      prevOrgId.current = currentOrgId;
      dispatch(setPage(1));
    }
  }, [currentOrgId, dispatch]);

  // Single source of truth for fetching users
  useEffect(() => {
    // Skip if we don't have valid params
    if (!fetchParams) return;

    // Skip if params haven't changed and it's not the initial render
    if (
      !isInitialRender.current &&
      JSON.stringify(prevFetchParams.current) === JSON.stringify(fetchParams)
    ) {
      return;
    }

    // Store current params for comparison
    prevFetchParams.current = { ...fetchParams };

    // For initial render, fetch immediately
    if (isInitialRender.current) {
      isInitialRender.current = false;
      dispatch(fetchUsers(fetchParams));
      return;
    }

    // For subsequent changes, set loading immediately and then debounce the API call
    dispatch(fetchUsers(fetchParams));
  }, [dispatch, fetchParams]);

  // Wrapper function for search that updates the search term
  const search = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchName(searchTerm));
      // Reset to first page when searching
      dispatch(setPage(1));
    },
    [dispatch]
  );

  // Wrapper for filtering by status
  const filterByStatus = useCallback(
    (status: string) => {
      dispatch(setStatusFilter(status));
      // Reset to first page when changing filters
      dispatch(setPage(1));
    },
    [dispatch]
  );

  // Memoized fetchUsersList for external use
  const fetchUsersList = useCallback(
    (filters?: Partial<UserFilters>) => {
      if (filters?.search !== undefined) {
        search(filters.search);
      }
      if (filters?.status !== undefined) {
        filterByStatus(filters.status);
      }
    },
    [search, filterByStatus]
  );

  // Create user
  const handleCreateUser = useCallback(
    async (data: CreateUserData) => {
      if (!currentOrgId) throw new Error('Organization ID is required');

      const result = await dispatch(
        createUser({
          ...data,
          organizationId: currentOrgId,
          // Ensure userType is always set to the same value as userRole
          userType: data.userRole,
        })
      );
      return result;
    },
    [dispatch, currentOrgId]
  );

  // Update user
  const handleUpdateUser = useCallback(
    async (data: UpdateUserData) => {
      try {
        // Ensure userType is always set to the same value as userRole if userRole is provided
        const updateData = data.userRole
          ? { ...data, userType: data.userRole }
          : data;

        const result = await dispatch(updateUser(updateData)).unwrap();
        if (result) {
          // Create new fetch params with the current state to ensure organizationId is included
          const currentParams: {
            page: number;
            pageSize: number;
            search: string;
            isActive?: boolean;
            organizationId: string | null;
          } = {
            page,
            pageSize: limit,
            search: searchName,
            organizationId: currentOrgId,
          };

          // Only include isActive if statusFilter is not 'all'
          if (statusFilter !== 'all') {
            currentParams.isActive = statusFilter === 'active';
          }

          // Refetch users after successful update with current params
          dispatch(fetchUsers(currentParams));
          return true;
        }
        return false;
      } catch {
        return false;
      }
    },
    [dispatch, page, limit, searchName, statusFilter, currentOrgId]
  );

  // Delete user
  const handleDeleteUser = useCallback(
    async (id: string) => {
      // Remove the error handling here since it's already handled in the Redux slice
      return dispatch(deleteUser(id)).unwrap();
    },
    [dispatch]
  );

  // Reset password
  const handleResetPassword = useCallback(
    async (id: string) => {
      const result = await dispatch(resetUserPassword(id));
      return result;
    },
    [dispatch]
  );

  // Search users
  const handleSearch = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchName(searchTerm));
      // Trigger API call with new search term
      fetchUsersList({ search: searchTerm });
    },
    [dispatch, fetchUsersList]
  );

  // Filter by status
  const handleStatusFilter = useCallback(
    (status: string) => {
      dispatch(setStatusFilter(status));
      // Trigger API call with new status filter
      fetchUsersList({ status });
    },
    [dispatch, fetchUsersList]
  );

  // Pagination
  const handlePageChange = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const handlePageSizeChange = useCallback(
    (newPageSize: number) => {
      dispatch(setPageSize(newPageSize));
    },
    [dispatch]
  );

  // Clear messages
  const handleClearMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  const handleClearUpdateSuccess = useCallback(() => {
    dispatch(clearUpdateSuccess());
  }, [dispatch]);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    dispatch(resetFilters());
  }, [dispatch]);

  // Note: Auto-fetch is now handled by useAutoFetch hook in App.tsx
  // This ensures fresh data on every navigation to the users page

  return {
    // Data
    users: usersList,
    total,
    totalPages,
    page,
    limit,
    searchName,
    statusFilter,

    // States
    loading: isLoading,
    updating: isUpdating,
    updateSuccess,
    error,
    errorMessage,
    successMessage,

    // Actions
    fetchUsers: fetchUsersList,
    createUser: handleCreateUser,
    updateUser: handleUpdateUser,
    deleteUser: handleDeleteUser,
    resetPassword: handleResetPassword,
    search: handleSearch,
    filterByStatus: handleStatusFilter,
    changePage: handlePageChange,
    changePageSize: handlePageSizeChange,
    clearMessages: handleClearMessages,
    clearUpdateSuccess: handleClearUpdateSuccess,
    resetFilters: handleResetFilters,
  };
};
