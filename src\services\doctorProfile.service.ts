import {
  DOCTOR_PROFILE_ENDPOINT,
  DOCTOR_PROFILE_PICTURE_ENDPOINT,
} from '../constants/api-endpoints';
import api from './api';

export interface DoctorProfileQualification {
  degree?: string;
  specialization?: string;
  university?: string;
  institute?: string;
  yearOfCompletion?: string;
  duration?: string;
  marks?: string;
  doc1?: string;
  doc2?: string;
  status?: string;
  uuId?: string;
}

export interface DoctorProfileExperience {
  hospitalName?: string;
  department?: string;
  designation?: string;
  from?: string;
  to?: string;
  salary?: string;
  documents?: unknown[];
  doc1?: string;
  doc2?: string;
  status?: string;
  duration?: {
    from?: string;
    to?: string;
  };
  uuId?: string;
}

export interface EmergencyContact {
  name?: string;
  relation?: string;
  city?: string;
  contactNumber?: string;
  email?: string;
  mobile?: string;
  uuId?: string;
}

export interface FamilyMember {
  name?: string;
  relation?: string;
  age?: string;
  occupation?: string;
  dependent?: string;
  dob?: string;
  status?: string;
  aadharNumber?: string;
  documents?: string;
  uuId?: string;
}

export interface LanguageKnown {
  language?: string;
  fluency?: string[];
  uuId?: string;
}

export interface BankDetails {
  ifsc?: string;
  accountNumber?: string;
  description?: string;
  document?: string;
  bank?: string;
  branch?: string;
}

export interface Insurance {
  validTo?: string;
  validFrom?: string;
  policyName?: string;
  policyNumber?: string;
  status?: string;
  uuId?: string;
}

export interface Documents {
  aadhar?: {
    number?: string;
    name?: string;
    description?: string;
    issuedAt?: string;
    url?: string;
  };
  passport?: {
    number?: string;
    name?: string;
    issuedPlace?: string;
    description?: string;
    issuedAt?: string;
    renewedAt?: string;
    url?: string;
  };
  panCard?: {
    number?: string;
    name?: string;
    description?: string;
    issuedAt?: string;
    url?: string;
  };
}

export interface Certification {
  validTo?: string;
  validFrom?: string;
  name?: string;
  regNumber?: string;
  dateOfUpdation?: string;
  status?: string;
  uuId?: string;
}

export interface MedicalRegistration {
  councilName?: string;
  registrationNumber?: string;
  validFrom?: string;
  validTo?: string;
  proof?: {
    description?: string;
    url?: string;
  };
}

export interface DoctorProfileResponse {
  id?: string;
  username?: string;
  created_on?: string;
  updated_on?: string;
  general?: {
    fullName?: string;
    designation?: string;
    department?: string;
    doctorID?: string;
    contactNumber?: string;
    workEmail?: string;
  };
  personal?: {
    age?: string;
    dob?: string;
    bloodGroup?: string;
    height?: string;
    weight?: string;
    isPersonWithDisability?: boolean;
    percentOfDisability?: string;
    identificationMark?: string;
    maritalStatus?: string;
    dateOfWedding?: string;
    nationality?: string;
    religion?: string;
    caste?: string;
    category?: string;
    reservationDetails?: string;
    idProof?: {
      type?: string;
      number?: string;
      description?: string;
      url?: string;
    };
    hometownDetails?: {
      hometown?: string;
      state?: string;
      district?: string;
      country?: string;
    };
    birthDetails?: {
      dateOfBirth?: string;
      placeOfBirth?: string;
      state?: string;
      district?: string;
      country?: string;
    };
    address?: {
      permanent?: {
        home?: string;
        street?: string;
        city?: string;
        pinCode?: string;
        district?: string;
        state?: string;
        country?: string;
        phone?: string;
        mobile?: string;
        email?: string;
        proof?: {
          description?: string;
          url?: string;
        };
      };
      current?: {
        home?: string;
        street?: string;
        city?: string;
        pinCode?: string;
        district?: string;
        state?: string;
        country?: string;
        phone?: string;
        mobile?: string;
        email?: string;
        proof?: {
          description?: string;
          url?: string;
        };
      };
    };
  };
  professionalDetails?: {
    medicalRegistration?: MedicalRegistration;
    specialties?: string[];
    qualifications?: DoctorProfileQualification[];
    certifications?: Certification[];
    experience?: DoctorProfileExperience[];
  };
  emergencyContacts?: EmergencyContact[];
  family?: FamilyMember[];
  languagesKnown?: LanguageKnown[];
  bankDetails?: BankDetails;
  insurance?: Insurance[];
  documents?: Documents;
  researchAndPublications?: unknown[];
  affiliations?: unknown[];
  consultationFee?: number;
}

const fetchDoctorProfileByEmail = async (email: string) => {
  const response = await api.get<DoctorProfileResponse>(
    DOCTOR_PROFILE_ENDPOINT,
    {
      params: { email },
      // Allow multiple concurrent calls (modal + standalone page) without cancellation
      cancelDuplicateRequest: false,
    }
  );

  return response.data;
};

export interface DoctorProfilePictureResponse {
  profilePictureUrl: string;
}

const fetchDoctorProfilePicture = async (doctorId: string) => {
  const response = await api.get<DoctorProfilePictureResponse>(
    DOCTOR_PROFILE_PICTURE_ENDPOINT,
    {
      params: { doctorId },
      cancelDuplicateRequest: false,
    }
  );

  return response.data;
};

const doctorProfileService = {
  fetchDoctorProfileByEmail,
  fetchDoctorProfilePicture,
};

export default doctorProfileService;
