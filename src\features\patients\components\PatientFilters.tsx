import { Search } from 'lucide-react';
import React, { memo } from 'react';

interface PatientFiltersProps {
  searchText: string;
  genderFilter: string;
  fromAge: number | null;
  toAge: number | null;
  fromDate: string;
  toDate: string;
  onSearchChange: (value: string) => void;
  onGenderChange: (value: string) => void;
  onAgeRangeChange: (fromAge: number | null, toAge: number | null) => void;
  onDateRangeChange: (fromDate: string, toDate: string) => void;
  onClearFilters: () => void;
}

const PatientFilters: React.FC<PatientFiltersProps> = memo(
  ({
    searchText,
    genderFilter,
    fromAge,
    toAge,
    fromDate,
    toDate,
    onSearchChange,
    onGenderChange,
    onAgeRangeChange,
    onDateRangeChange,
    onClearFilters,
  }) => {
    return (
      <div className='bg-white rounded-lg shadow p-4 border'>
        {/* Search Bar */}
        <div className='mb-4'>
          <div className='relative'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
            <input
              type='text'
              placeholder='Search by Patient ID, Name, or Phone...'
              value={searchText}
              onChange={(e) => onSearchChange(e.target.value)}
              className='pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full'
            />
          </div>
        </div>

        {/* Filters */}
        <div className='flex flex-wrap gap-4 items-center p-4 bg-gray-50 rounded-lg border'>
          <div className='flex items-center gap-2'>
            <span className='text-sm font-medium text-gray-700'>Filters:</span>
          </div>

          {/* Gender Filter */}
          <div className='flex items-center gap-2'>
            <label className='text-sm text-gray-600'>Gender:</label>
            <select
              value={genderFilter}
              onChange={(e) => onGenderChange(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm min-w-[120px] bg-white'
            >
              <option value=''>All Genders</option>
              <option value='Male'>Male</option>
              <option value='Female'>Female</option>
              <option value='Other'>Other</option>
            </select>
          </div>

          {/* Age Range Filter */}
          <div className='flex items-center gap-2'>
            <label className='text-sm text-gray-600 whitespace-nowrap'>
              Age:
            </label>
            <div className='flex items-center gap-1'>
              <input
                type='number'
                placeholder='From'
                value={fromAge || ''}
                onChange={(e) =>
                  onAgeRangeChange(
                    e.target.value ? parseInt(e.target.value) : null,
                    toAge
                  )
                }
                className='w-16 px-2 py-1 border border-gray-300 rounded text-sm bg-white'
                min='0'
                max='150'
              />
              <span className='text-gray-400 text-sm'>to</span>
              <input
                type='number'
                placeholder='To'
                value={toAge || ''}
                onChange={(e) =>
                  onAgeRangeChange(
                    fromAge,
                    e.target.value ? parseInt(e.target.value) : null
                  )
                }
                className='w-16 px-2 py-1 border border-gray-300 rounded text-sm bg-white'
                min='0'
                max='150'
              />
            </div>
          </div>

          {/* Date Range Filter */}
          <div className='flex items-center gap-2'>
            <label className='text-sm text-gray-600 whitespace-nowrap'>
              Registration:
            </label>
            <div className='min-w-[200px]'>
              {/* Note: ReactDateRangePicker would be imported here if this component is used */}
              <div className='flex items-center px-3 py-2 border border-gray-300 rounded-lg bg-white cursor-pointer hover:border-gray-400 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent'>
                <svg
                  className='w-4 h-4 text-gray-400 mr-2'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
                  />
                </svg>
                <span
                  className={`text-sm ${fromDate || toDate ? 'text-gray-900' : 'text-gray-500'}`}
                >
                  {fromDate && toDate
                    ? `${new Date(fromDate).toLocaleDateString('en-GB')} to ${new Date(toDate).toLocaleDateString('en-GB')}`
                    : fromDate
                      ? `From ${new Date(fromDate).toLocaleDateString('en-GB')}`
                      : toDate
                        ? `Until ${new Date(toDate).toLocaleDateString('en-GB')}`
                        : 'Select date range'}
                </span>
              </div>
              {/* Hidden date inputs for functionality */}
              <div className='hidden'>
                <input
                  type='date'
                  value={fromDate}
                  onChange={(e) => onDateRangeChange(e.target.value, toDate)}
                />
                <input
                  type='date'
                  value={toDate}
                  onChange={(e) => onDateRangeChange(fromDate, e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Clear Filters Button */}
          <button
            onClick={onClearFilters}
            className='px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-100 bg-white transition-colors'
          >
            Clear Filters
          </button>
        </div>
      </div>
    );
  }
);

PatientFilters.displayName = 'PatientFilters';

export default PatientFilters;
