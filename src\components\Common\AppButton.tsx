import MuiButton, { ButtonProps as MuiButtonProps } from '@mui/material/Button';
import React from 'react';

type ButtonKind = 'primary' | 'secondary' | 'default';

interface AppButtonProps extends Omit<MuiButtonProps, 'color' | 'variant'> {
  kind?: ButtonKind;
}

const AppButton: React.FC<AppButtonProps> = ({
  kind = 'primary',
  ...props
}) => {
  let color: MuiButtonProps['color'] = 'primary';
  let variant: MuiButtonProps['variant'] = 'contained';

  if (kind === 'secondary') {
    color = 'inherit';
    variant = 'outlined';
  } else if (kind === 'default') {
    color = 'inherit';
    variant = 'text';
  }

  return (
    <MuiButton
      color={color}
      variant={variant}
      {...props}
      sx={{
        textTransform: 'none', // Preserve original text case
        ...props.sx,
      }}
    />
  );
};

export default AppButton;
