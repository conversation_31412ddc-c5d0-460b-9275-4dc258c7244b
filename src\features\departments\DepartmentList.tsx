import { format } from 'date-fns';
import { Building, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DataTable, { Column } from '../../components/Common/DataTable';
import Modal from '../../components/Common/Modal';
import OrganizationRequired from '../../components/Common/OrganizationRequired';
import SearchBar from '../../components/Common/SearchBar';
import StatusBadge from '../../components/Common/StatusBadge';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import { fetchDepartments } from '../../store/slices/departmentSlice';
import { Department } from '../../types';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import DepartmentForm from './DepartmentForm';

const DepartmentList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization, isSuperAdmin, isOrganizationAdmin } = useAuth();
  const { departments, loading, total, page, limit } = useSelector(
    (state: RootState) => state.departments
  );
  const organizationId = getCurrentOrganizationId();

  const [searchValue, setSearchValue] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedDepartment, setSelectedDepartment] =
    useState<Department | null>(null);

  useEffect(() => {
    if (organizationId) {
      dispatch(
        fetchDepartments({
          organizationId,
          page: 1,
          limit: 10,
        })
      );
    }
  }, [dispatch, organizationId]);

  const handlePageChange = (newPage: number) => {
    if (organizationId) {
      dispatch(
        fetchDepartments({
          organizationId,
          page: newPage,
          limit,
          search: searchValue,
          status: statusFilter,
        })
      );
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (organizationId) {
      dispatch(
        fetchDepartments({
          organizationId,
          page: 1,
          limit,
          search: value,
          status: statusFilter,
        })
      );
    }
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    if (organizationId) {
      dispatch(
        fetchDepartments({
          organizationId,
          page: 1,
          limit,
          search: searchValue,
          status,
        })
      );
    }
  };

  const handleCreate = () => {
    setSelectedDepartment(null);
    setShowModal(true);
  };

  const handleEdit = (department: Department) => {
    setSelectedDepartment(department);
    setShowModal(true);
  };

  const columns: Column<Department>[] = [
    {
      key: 'name',
      label: 'Department Name',
      render: (name, department) => (
        <div className='flex items-center space-x-3'>
          <div className='flex-shrink-0'>
            <div className='w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center'>
              <Building className='w-5 h-5 text-blue-600' />
            </div>
          </div>
          <div>
            <div className='font-medium text-gray-900'>{name}</div>
            <div className='text-sm text-gray-500'>
              {department.description}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (status) => <StatusBadge status={status} />,
    },
    {
      key: 'createdAt',
      label: 'Created At',
      render: (createdAt) => format(new Date(createdAt), 'MMM dd, yyyy HH:mm'),
    },
  ];

  // For super admins, require organization selection
  if (isSuperAdmin && !selectedOrganization) {
    return <OrganizationRequired feature='departments' />;
  }

  // For organization admins or when organizationId is available, proceed
  if (!organizationId) {
    if (isOrganizationAdmin) {
      return (
        <OrganizationRequired
          feature='departments'
          customMessage='Unable to load your organization. Please try logging in again.'
          customTitle='Organization Loading Error'
        />
      );
    } else {
      return <OrganizationRequired feature='departments' />;
    }
  }

  return (
    <div className='space-y-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Departments</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Manage departments for{' '}
            {selectedOrganization?.name || 'Your Organization'}
          </p>
        </div>
        <div className='flex items-center gap-3'>
          <SearchBar
            value={searchValue}
            onChange={handleSearch}
            placeholder='Search departments...'
            size='md'
          />
          <button
            onClick={handleCreate}
            className='inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          >
            <Plus className='w-4 h-4 mr-2' />
            Add Department
          </button>
        </div>
      </div>

      <DataTable
        data={departments}
        columns={columns}
        loading={loading}
        pagination={{
          page,
          limit,
          total,
          onPageChange: handlePageChange,
        }}
        searchFilters={
          <div className='flex items-center space-x-4'>
            <label
              htmlFor='status-filter'
              className='text-sm font-medium text-gray-700'
            >
              Status:
            </label>
            <select
              id='status-filter'
              value={statusFilter}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className='px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm'
            >
              <option value=''>All</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        }
        onEdit={handleEdit}
      />

      <Modal
        isOpen={showModal}
        title={selectedDepartment ? 'Edit Department' : 'Create Department'}
        onClose={() => setShowModal(false)}
      >
        <DepartmentForm
          department={selectedDepartment}
          onSuccess={() => setShowModal(false)}
        />
      </Modal>
    </div>
  );
};

export default DepartmentList;
