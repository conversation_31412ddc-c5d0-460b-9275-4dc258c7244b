import { yupResolver } from '@hookform/resolvers/yup';
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';

import AppButton from '../../../components/Common/AppButton';
import TextInput from '../../../components/Common/MUIInput';
import SelectInput from '../../../components/Common/MUISelect';
import {
  fetchLabTestDepartments,
  LabTestDepartment,
} from '../services/department.service';

// Form schema
const addLabTestFormSchema = yup.object({
  displayName: yup
    .string()
    .max(100, 'Display name must not exceed 100 characters'),
  testName: yup
    .string()
    .required('Required')
    .min(2, 'Test name must be at least 2 characters')
    .max(100, 'Test name must not exceed 100 characters'),
  department: yup
    .string()
    .required('Required')
    .test(
      'not-all',
      'Please select a specific department',
      (value) => value !== 'all'
    ),
  organizationCost: yup
    .number()
    .nullable()
    .typeError('Please enter a valid number')
    .required(' Required')
    .min(0, 'Cost cannot be negative')
    .transform((value, originalValue) => {
      // Handle empty string or null/undefined
      if (
        originalValue === '' ||
        originalValue === null ||
        originalValue === undefined
      ) {
        return undefined;
      }
      return value;
    }),
  isActive: yup.boolean().required('Please select status'),
});

interface AddLabTestFormData {
  displayName?: string;
  testName: string;
  department: string;
  organizationCost: number | null;
  isActive: boolean;
}

interface AddLabTestFormProps {
  onSubmit: (data: AddLabTestFormData) => Promise<void>;
  onSuccess?: () => void;
  onClose: () => void;
  submitting?: boolean;
}

const AddLabTestForm: React.FC<AddLabTestFormProps> = ({
  onSubmit,
  onSuccess,
  onClose,
  submitting = false,
}) => {
  const [departments, setDepartments] = useState<LabTestDepartment[]>([]);
  const [loadingDepartments, setLoadingDepartments] = useState(true);

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<AddLabTestFormData>({
    resolver: yupResolver(addLabTestFormSchema) as any,
    defaultValues: {
      displayName: '',
      testName: '',
      department: '',
      organizationCost: null,
      isActive: true,
    },
  });

  // Load departments on component mount
  useEffect(() => {
    const loadDepartments = async () => {
      try {
        setLoadingDepartments(true);
        const depts = await fetchLabTestDepartments();
        // Filter out 'all' and 'others' options for creation
        const filteredDepts = depts.filter(
          (dept) => dept.value !== 'all' && dept.value !== 'others'
        );
        setDepartments(filteredDepts);
      } catch (error) {
        console.error('Error loading departments:', error);
        setDepartments([]);
      } finally {
        setLoadingDepartments(false);
      }
    };

    loadDepartments();
  }, []);

  const onFormSubmit = useCallback(
    async (data: AddLabTestFormData) => {
      try {
        await onSubmit(data);
        onSuccess?.();
        reset();
      } catch (error) {
        console.error('Error creating lab test:', error);
        // Error is handled by the parent component
      }
    },
    [onSubmit, onSuccess, reset]
  );

  const statusOptions = [
    { label: 'Active', value: 'true' },
    { label: 'Inactive', value: 'false' },
  ];

  return (
    <div className='space-y-6'>
      <form onSubmit={handleSubmit(onFormSubmit)} className='space-y-4'>
        <div className='mt-4'>
          <Controller
            name='testName'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Test Name *'
                error={!!errors.testName}
                helperText={errors.testName?.message}
                placeholder='Enter test name'
                disabled={isSubmitting || submitting}
              />
            )}
          />
        </div>
        <div className='mt-4'>
          <Controller
            name='displayName'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Display Name'
                error={!!errors.displayName}
                helperText={errors.displayName?.message}
                placeholder='Enter display name (optional)'
                disabled={isSubmitting || submitting}
              />
            )}
          />
        </div>
        <div className='mt-4'>
          <Controller
            name='department'
            control={control}
            render={({ field }) => (
              <SelectInput
                {...field}
                label='Department*'
                error={!!errors.department}
                helperText={errors.department?.message}
                disabled={loadingDepartments || isSubmitting || submitting}
                options={
                  loadingDepartments
                    ? [{ label: '', value: '' }]
                    : [
                        { label: '', value: '' },
                        ...departments.map((dept) => ({
                          label: dept.label,
                          value: dept.value,
                        })),
                      ]
                }
              />
            )}
          />
        </div>

        {/* Organization Cost */}
        <div className='mt-4'>
          <Controller
            name='organizationCost'
            control={control}
            render={({ field }) => (
              <TextInput
                {...field}
                label='Organization Cost *'
                value={field.value === null ? '' : field.value}
                onChange={(e) => {
                  const value = e.target.value;
                  if (value === '') {
                    field.onChange(null);
                  } else {
                    const numericValue = Number(value);
                    field.onChange(numericValue);
                  }
                }}
                error={!!errors.organizationCost}
                helperText={errors.organizationCost?.message}
                placeholder='Enter cost amount'
                type='number'
                inputProps={{
                  min: 0,
                  step: 0.01,
                  style: {
                    MozAppearance: 'textfield', // Firefox
                  },
                }}
                sx={{
                  '& input[type=number]': {
                    MozAppearance: 'textfield', // Firefox
                  },
                  '& input[type=number]::-webkit-outer-spin-button': {
                    WebkitAppearance: 'none', // Chrome, Safari, Edge
                    margin: 0,
                  },
                  '& input[type=number]::-webkit-inner-spin-button': {
                    WebkitAppearance: 'none', // Chrome, Safari, Edge
                    margin: 0,
                  },
                }}
                disabled={isSubmitting || submitting}
              />
            )}
          />
        </div>

        <div className='mt-4'>
          <Controller
            name='isActive'
            control={control}
            render={({ field }) => (
              <SelectInput
                {...field}
                value={field.value ? 'true' : 'false'}
                onChange={(e) => field.onChange(e.target.value === 'true')}
                label='Status *'
                error={!!errors.isActive}
                helperText={errors.isActive?.message}
                disabled={isSubmitting || submitting}
                options={statusOptions}
              />
            )}
          />
        </div>

        {/* Form Actions */}
        <div className='flex justify-end space-x-4 pt-4'>
          <AppButton
            type='button'
            kind='secondary'
            onClick={onClose}
            disabled={isSubmitting || submitting}
          >
            Cancel
          </AppButton>
          <AppButton
            type='submit'
            kind='primary'
            disabled={isSubmitting || submitting}
          >
            {isSubmitting || submitting ? 'Creating...' : 'Save'}
          </AppButton>
        </div>
      </form>
    </div>
  );
};

export default AddLabTestForm;
