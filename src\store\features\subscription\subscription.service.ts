import {
  ORGANIZATION_PLAN_ENDPOINT,
  SUBSCRIPTION_FEATURE_ENDPOINT,
  SUBSCRIPTION_PLAN_ENDPOINT,
} from '../../../constants/api-endpoints';
import api from '../../../services/api';

const apiUrl = SUBSCRIPTION_PLAN_ENDPOINT;
const organizationPlanUrl = ORGANIZATION_PLAN_ENDPOINT;

export interface SubscriptionPlanFeature {
  featureId?: string;
  featureName?: string;
  monthlyAmount: number;
  yearlyAmount: number;
}

export interface SubscriptionPlanFeaturePayload {
  featureId: string;
  monthlyAmount: number;
  yearlyAmount: number;
}

export interface SubscriptionPlanFeatures {
  MRD: SubscriptionPlanFeature[];
  EMR: SubscriptionPlanFeature[];
  Billing: SubscriptionPlanFeature[];
}

export interface SubscriptionPlanFeaturesPayload {
  MRD: SubscriptionPlanFeaturePayload[];
  EMR: SubscriptionPlanFeaturePayload[];
  Billing: SubscriptionPlanFeaturePayload[];
}

export type SubscriptionPlanAddOnFeatures = SubscriptionPlanFeatures;
export type SubscriptionPlanAddOnFeaturesPayload =
  SubscriptionPlanFeaturesPayload;

export interface CreateSubscriptionPlanRequest {
  planName: string;
  description: string;
  validity: 'Both' | 'Monthly' | 'Yearly';
  features: SubscriptionPlanFeaturesPayload;
  addOnFeatures: SubscriptionPlanAddOnFeaturesPayload;
  isActive: boolean;
}

export interface SubscriptionPlanResponse {
  success: boolean;
  message: string;
  data?: unknown;
}

export interface SubscriptionPlan {
  id?: string;
  planName: string;
  description: string;
  validity: 'Both' | 'Monthly' | 'Yearly';
  features: SubscriptionPlanFeatures;
  addOnFeatures: SubscriptionPlanAddOnFeatures;
  isActive: boolean;
  monthlyTotal?: number;
  yearlyTotal?: number;
  totalMonthlyBasicAmount?: number;
  totalYearlyBasicAmount?: number;
  created_on?: string;
  updated_on?: string;
  created_by?: string;
  updated_by?: string;
}

export type SubscriptionFeatureType = 'MRD' | 'EMR' | 'Billing';

export interface SubscriptionFeature {
  id: string;
  featureName: string;
  description: string;
  type: SubscriptionFeatureType;
  subType?: string;
  permissionKeys: string[];
  isActive: boolean;
  created_by: string;
  created_on: string;
  updated_on: string;
  updated_by: string;
}

export interface SubscriptionFeatureListResponse {
  features: SubscriptionFeature[];
  count: number;
}

export interface UpdateSubscriptionPlanRequest {
  planName?: string;
  description?: string;
  validity?: 'Both' | 'Monthly' | 'Yearly';
  features?: SubscriptionPlanFeaturesPayload;
  addOnFeatures?: SubscriptionPlanAddOnFeaturesPayload;
  isActive?: boolean;
}

export interface UpdateOrganizationPlanRequest {
  planName?: string;
  description?: string;
}

export interface SubscriptionPlanListResponse {
  plans: SubscriptionPlan[];
  count: number;
}

const createSubscriptionPlan = async (
  data: CreateSubscriptionPlanRequest
): Promise<SubscriptionPlanResponse> => {
  const response = await api.post<SubscriptionPlanResponse>(apiUrl, data);
  return response.data;
};

const getAllSubscriptionPlans =
  async (): Promise<SubscriptionPlanListResponse> => {
    const response = await api.get<SubscriptionPlanListResponse>(apiUrl, {
      params: { includeInactive: true },
    });

    if (response.data && Array.isArray(response.data.plans)) {
      response.data.plans.sort((a, b) => {
        const dateA = a.created_on ? new Date(a.created_on).getTime() : 0;
        const dateB = b.created_on ? new Date(b.created_on).getTime() : 0;
        return dateB - dateA;
      });
    }

    return response.data;
  };

const getSubscriptionPlanById = async (
  planId: string
): Promise<SubscriptionPlan> => {
  const response = await api.get<SubscriptionPlan>(apiUrl, {
    params: { id: planId },
  });
  return response.data;
};

const getOrganizationPlan = async (): Promise<SubscriptionPlan> => {
  const response = await api.get<SubscriptionPlan>(organizationPlanUrl);
  return response.data;
};

let subscriptionFeaturesCache: SubscriptionFeatureListResponse | null = null;
let subscriptionFeaturesRequest: Promise<SubscriptionFeatureListResponse> | null =
  null;

const getSubscriptionFeatures = async (
  options: { forceRefresh?: boolean } = {}
): Promise<SubscriptionFeatureListResponse> => {
  const { forceRefresh = false } = options;

  if (!forceRefresh) {
    if (subscriptionFeaturesCache) {
      return subscriptionFeaturesCache;
    }
    if (subscriptionFeaturesRequest) {
      return subscriptionFeaturesRequest;
    }
  }

  subscriptionFeaturesRequest = api
    .get<SubscriptionFeatureListResponse>(SUBSCRIPTION_FEATURE_ENDPOINT)
    .then((response) => {
      const data = response.data;
      let features: SubscriptionFeature[] = [];
      let count = 0;

      if (Array.isArray(data)) {
        features = data;
        count = data.length;
      } else if (data && typeof data === 'object' && 'features' in data) {
        features = (data as any).features ?? [];
        count = (data as any).count ?? features.length;
      }

      subscriptionFeaturesCache = {
        features,
        count,
      };
      return subscriptionFeaturesCache;
    })
    .catch((error) => {
      if (!forceRefresh) {
        subscriptionFeaturesCache = null;
      }
      throw error;
    })
    .finally(() => {
      subscriptionFeaturesRequest = null;
    });

  return subscriptionFeaturesRequest;
};

const clearSubscriptionFeaturesCache = () => {
  subscriptionFeaturesCache = null;
};

const updateSubscriptionPlan = async (
  planId: string,
  data: UpdateSubscriptionPlanRequest
): Promise<SubscriptionPlanResponse> => {
  const response = await api.patch<SubscriptionPlanResponse>(apiUrl, data, {
    params: { id: planId },
  });
  return response.data;
};

const updateOrganizationPlan = async (
  data: UpdateOrganizationPlanRequest
): Promise<SubscriptionPlanResponse> => {
  const response = await api.patch<SubscriptionPlanResponse>(
    organizationPlanUrl,
    data
  );
  return response.data;
};

/**
 * Delete a subscription plan
 */
const deleteSubscriptionPlan = async (
  planId: string
): Promise<SubscriptionPlanResponse> => {
  const response = await api.delete<SubscriptionPlanResponse>(apiUrl, {
    params: { id: planId },
  });
  return response.data;
};

export {
  clearSubscriptionFeaturesCache,
  createSubscriptionPlan,
  deleteSubscriptionPlan,
  getAllSubscriptionPlans,
  getOrganizationPlan,
  getSubscriptionFeatures,
  getSubscriptionPlanById,
  updateOrganizationPlan,
  updateSubscriptionPlan,
};
