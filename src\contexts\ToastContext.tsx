import 'react-toastify/dist/ReactToastify.css';

import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
} from 'react';
import { Slide, toast, ToastContainer, ToastOptions } from 'react-toastify';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  message?: string;
  duration?: number;
}

interface ToastContextType {
  toasts: Toast[];
  showToast: (toast: Omit<Toast, 'id'>) => void;
  hideToast: (id: string) => void;
  success: (title: string, message?: string) => void;
  error: (title: string, message?: string) => void;
  warning: (title: string, message?: string) => void;
  info: (title: string, message?: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  // Use react-toastify for all toasts
  const showToast = useCallback((toastObj: Omit<Toast, 'id'>) => {
    const options: ToastOptions = {
      type: toastObj.type,
      autoClose: toastObj.duration || 5000,
      position: 'top-right',
      transition: Slide,
    };
    toast(toastObj.message ? toastObj.message : toastObj.title, options);
  }, []);

  const success = useCallback(
    (title: string, message?: string) =>
      showToast(
        message !== undefined
          ? { type: 'success', title, message }
          : { type: 'success', title }
      ),
    [showToast]
  );
  const error = useCallback(
    (title: string, message?: string) =>
      showToast(
        message !== undefined
          ? { type: 'error', title, message }
          : { type: 'error', title }
      ),
    [showToast]
  );
  const warning = useCallback(
    (title: string, message?: string) =>
      showToast(
        message !== undefined
          ? { type: 'warning', title, message }
          : { type: 'warning', title }
      ),
    [showToast]
  );
  const info = useCallback(
    (title: string, message?: string) =>
      showToast(
        message !== undefined
          ? { type: 'info', title, message }
          : { type: 'info', title }
      ),
    [showToast]
  );

  return (
    <ToastContext.Provider
      value={{
        toasts: [], // not used
        showToast,
        hideToast: toast.dismiss,
        success,
        error,
        warning,
        info,
      }}
    >
      {children}
      <ToastContainer
        position='top-right'
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme='light'
      />
    </ToastContext.Provider>
  );
};
