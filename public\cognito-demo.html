/**
 * Standalone Cognito Login Demo
 * A complete, self-contained HTML file demonstrating Cognito authentication
 * This matches the sample script provided and can be used for testing
 */

<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <title>Cognito Login Demo - ArcaAI Admin</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
    }

    .container {
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
      padding: 40px;
      max-width: 600px;
      width: 100%;
    }

    h1 {
      color: #333;
      margin-bottom: 10px;
      font-size: 28px;
    }

    h3 {
      color: #666;
      margin-top: 30px;
      margin-bottom: 10px;
      font-size: 18px;
    }

    button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      border-radius: 6px;
      padding: 12px 30px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      margin: 10px 5px;
      transition: transform 0.2s, box-shadow 0.2s;
    }

    button:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    button:active {
      transform: translateY(0);
    }

    button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    pre {
      background: #f5f5f5;
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 20px;
      overflow-x: auto;
      font-size: 13px;
      line-height: 1.6;
      color: #333;
      max-height: 400px;
      overflow-y: auto;
    }

    .status-box {
      background: #f9f9f9;
      border-left: 4px solid #667eea;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }

    .error {
      border-left-color: #e74c3c;
      background: #fee;
    }

    .success {
      border-left-color: #2ecc71;
      background: #efe;
    }

    .info {
      background: #e3f2fd;
      border-left: 4px solid #2196f3;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
      font-size: 14px;
    }

    .button-group {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .config-info {
      background: #fff3cd;
      border: 1px solid #ffc107;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
      font-size: 13px;
    }

    .config-info strong {
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔐 Cognito Login Demo</h1>
    <p style="color: #666; margin-bottom: 20px;">AWS Cognito Authentication for ArcaAI Admin</p>

    <div class="config-info">
      <strong>Configuration:</strong><br>
      Client ID: 1h1b5fgsdure1c30i2l82hbft5<br>
      Domain: arca-dev-829876691474.auth.ap-south-1.amazoncognito.com<br>
      Redirect: <span id="currentUrl"></span>
    </div>

    <div class="button-group">
      <button onclick="login()">🚀 Login with Cognito</button>
      <button onclick="logout()">🚪 Logout</button>
      <button onclick="getUserInfo()">👤 Get User Info</button>
      <button onclick="clearStorage()">🗑️ Clear Storage</button>
    </div>

    <h3>Status:</h3>
    <pre id="status">Not logged in. Click "Login with Cognito" to start.</pre>

    <div class="info">
      <strong>ℹ️ How it works:</strong><br>
      1. Click "Login with Cognito" to start OAuth flow<br>
      2. You'll be redirected to AWS Cognito login page<br>
      3. After login, you'll be redirected back with tokens<br>
      4. Tokens are stored in sessionStorage for security
    </div>
  </div>

  <script>
    // ========== CONFIG - Replace with your values ==========
    const CLIENT_ID = '3a59nceivtu4virh7s06jkkpfu';
    const COGNITO_DOMAIN = 'arca-dev-829876691474.auth.ap-south-1.amazoncognito.com';
    const REDIRECT_URI = window.location.origin + window.location.pathname;
    // =======================================================

    // Display current URL
    document.getElementById('currentUrl').textContent = REDIRECT_URI;

    // Generate cryptographically random string
    function generateRandom(length) {
      const array = new Uint8Array(length);
      crypto.getRandomValues(array);
      return btoa(String.fromCharCode(...array))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
    }

    // Create SHA256 hash for PKCE
    async function sha256(str) {
      const buffer = new TextEncoder().encode(str);
      const hash = await crypto.subtle.digest('SHA-256', buffer);
      return btoa(String.fromCharCode(...new Uint8Array(hash)))
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
    }

    // Update status display
    function updateStatus(message, type = 'info') {
      const statusEl = document.getElementById('status');
      statusEl.textContent = message;
      statusEl.className = type;
    }

    // Login function
    async function login() {
      try {
        updateStatus('Initiating login... Generating PKCE parameters...');

        const verifier = generateRandom(32);
        const challenge = await sha256(verifier);
        const state = generateRandom(16);

        // Save for later
        sessionStorage.setItem('cognito.verifier', verifier);
        sessionStorage.setItem('cognito.state', state);

        updateStatus('Redirecting to Cognito login page...');

        // Redirect to Cognito
        const params = new URLSearchParams({
          response_type: 'code',
          client_id: CLIENT_ID,
          redirect_uri: REDIRECT_URI,
          scope: 'openid email profile',
          state: state,
          code_challenge: challenge,
          code_challenge_method: 'S256'
        });

        window.location.href = `https://${COGNITO_DOMAIN}/oauth2/authorize?${params}`;
      } catch (error) {
        updateStatus('Login error: ' + error.message, 'error');
      }
    }

    // Logout function
    function logout() {
      sessionStorage.clear();
      localStorage.removeItem('token');
      updateStatus('Logged out successfully. All tokens cleared.', 'success');
    }

    // Clear storage
    function clearStorage() {
      sessionStorage.clear();
      localStorage.clear();
      updateStatus('All storage cleared.', 'success');
    }

    // Get user info from stored tokens
    async function getUserInfo() {
      const accessToken = sessionStorage.getItem('cognito.accessToken');

      if (!accessToken) {
        updateStatus('No access token found. Please login first.', 'error');
        return;
      }

      try {
        updateStatus('Fetching user info from Cognito...');

        const response = await fetch(`https://${COGNITO_DOMAIN}/oauth2/userInfo`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        });

        if (response.ok) {
          const userInfo = await response.json();
          updateStatus('User Info:\n\n' + JSON.stringify(userInfo, null, 2), 'success');
        } else {
          const error = await response.text();
          updateStatus('Failed to fetch user info:\n' + error, 'error');
        }
      } catch (error) {
        updateStatus('Error fetching user info: ' + error.message, 'error');
      }
    }

    // Handle redirect after login
    window.onload = async function() {
      const params = new URLSearchParams(window.location.search);
      const code = params.get('code');
      const state = params.get('state');
      const error = params.get('error');

      // Check for errors
      if (error) {
        const errorDesc = params.get('error_description') || 'Unknown error';
        updateStatus('Authentication error: ' + errorDesc, 'error');
        window.history.replaceState({}, document.title, window.location.pathname);
        return;
      }

      // If no code, just show default status
      if (!code) {
        const accessToken = sessionStorage.getItem('cognito.accessToken');
        if (accessToken) {
          updateStatus('Already logged in! Click "Get User Info" to see your details.', 'success');
        } else {
          updateStatus('Not logged in. Click "Login with Cognito" to start.');
        }
        return;
      }

      // Verify state
      const savedState = sessionStorage.getItem('cognito.state');
      if (state !== savedState) {
        updateStatus('Error: State mismatch (possible CSRF attack)', 'error');
        window.history.replaceState({}, document.title, window.location.pathname);
        return;
      }

      updateStatus('Processing authentication callback...');

      // Exchange code for tokens
      const verifier = sessionStorage.getItem('cognito.verifier');
      if (!verifier) {
        updateStatus('Error: No code verifier found', 'error');
        window.history.replaceState({}, document.title, window.location.pathname);
        return;
      }

      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        client_id: CLIENT_ID,
        code: code,
        redirect_uri: REDIRECT_URI,
        code_verifier: verifier
      });

      try {
        updateStatus('Exchanging authorization code for tokens...');

        const response = await fetch(`https://${COGNITO_DOMAIN}/oauth2/token`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          body: body
        });

        if (response.ok) {
          const tokens = await response.json();

          // Store tokens
          sessionStorage.setItem('cognito.accessToken', tokens.access_token);
          sessionStorage.setItem('cognito.idToken', tokens.id_token);
          if (tokens.refresh_token) {
            sessionStorage.setItem('cognito.refreshToken', tokens.refresh_token);
          }

          // Store in localStorage for compatibility
          localStorage.setItem('token', tokens.access_token);

          // Calculate expiry
          const expiryTime = Date.now() + (tokens.expires_in * 1000);
          sessionStorage.setItem('cognito.tokenExpiry', expiryTime.toString());

          updateStatus(
            '✅ Logged in successfully!\n\n' +
            'Tokens received:\n' +
            JSON.stringify(tokens, null, 2) +
            '\n\nClick "Get User Info" to fetch your profile.',
            'success'
          );

          // Clean URL
          window.history.replaceState({}, document.title, window.location.pathname);

          // Clean up temporary storage
          sessionStorage.removeItem('cognito.verifier');
          sessionStorage.removeItem('cognito.state');
        } else {
          const error = await response.text();
          updateStatus('Token exchange failed:\n' + error, 'error');
          window.history.replaceState({}, document.title, window.location.pathname);
        }
      } catch (err) {
        updateStatus('Error: ' + err.message, 'error');
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    };
  </script>
</body>
</html>
