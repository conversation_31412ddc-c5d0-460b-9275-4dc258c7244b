import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { DataGrid, GridPaginationModel, GridToolbar } from '@mui/x-data-grid';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import Modal from '../../components/Common/Modal';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import {
  fetchTemplates,
  updateTemplate,
} from '../../store/slices/templateSlice';
import { Template } from '../../types';
import { getOrganizationId } from '../../utils/organization-utils';
import { getColumns } from './columns.tsx';
import TemplateForm from './TemplateForm.tsx';

const TemplateList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const { templates, loading, total } = useSelector(
    (state: RootState) => state.templates
  );

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: 10,
  });

  useEffect(() => {
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchTemplates({
          organizationId: orgId,
          page: paginationModel.page + 1,
          limit: paginationModel.pageSize,
          search: searchTerm,
          status: filterStatus,
        })
      );
    }
  }, [
    dispatch,
    selectedOrganization,
    paginationModel,
    searchTerm,
    filterStatus,
  ]);

  const handleEdit = (template: Template) => {
    setSelectedTemplate(template);
    setIsModalOpen(true);
  };

  const handleToggleStatus = (template: Template) => {
    const newStatus = template.status === 'active' ? 'inactive' : 'active';
    dispatch(
      updateTemplate({
        id: template.id,
        data: { ...template, status: newStatus },
      })
    );
  };

  const columns = useMemo(
    () =>
      getColumns({ onEdit: handleEdit, onToggleStatus: handleToggleStatus }),
    []
  );

  return (
    <Box sx={{ p: 3, height: '100%', width: '100%' }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
        }}
      >
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            label='Search Templates'
            variant='outlined'
            size='small'
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <FormControl size='small' sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filterStatus}
              label='Status'
              onChange={(e) => setFilterStatus(e.target.value)}
            >
              <MenuItem value='all'>All</MenuItem>
              <MenuItem value='active'>Active</MenuItem>
              <MenuItem value='inactive'>Inactive</MenuItem>
            </Select>
          </FormControl>
        </Box>
        <Button
          variant='contained'
          onClick={() => {
            setSelectedTemplate(null);
            setIsModalOpen(true);
          }}
        >
          Add New Template
        </Button>
      </Box>
      <Box sx={{ height: 650, width: '100%' }}>
        <DataGrid
          rows={templates}
          columns={columns}
          loading={loading}
          rowCount={total}
          pageSizeOptions={[5, 10, 25]}
          paginationModel={paginationModel}
          onPaginationModelChange={setPaginationModel}
          paginationMode='server'
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
            },
          }}
          getRowId={(row) => row.id}
        />
      </Box>
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedTemplate ? 'Edit Template' : 'Add New Template'}
        size='md'
      >
        <TemplateForm
          template={selectedTemplate}
          onFinished={() => {
            setIsModalOpen(false);
          }}
        />
      </Modal>
    </Box>
  );
};

export default TemplateList;
