export interface BulkUpdateStatus {
  id: string;
  type: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  progress: number;
  totalItems: number;
  processedItems: number;
  startTime: string | null;
  endTime: string | null;
  result: any;
  error: string | null;
  message: string;
  statusUrl?: string; // URL to check the status of the bulk update
}

export interface BulkUpdateStartResponse {
  async: boolean;
  jobId: string;
  message: string;
  statusUrl: string;
}
