import * as yup from 'yup';

import { DEPARTMENT_OPTIONS } from '../types/labTest.types';

// Create dynamic lab test filter form schema
export const createLabTestFilterSchema = (departmentValues: string[]) =>
  yup.object({
    searchText: yup.string().trim(),
    department: yup.string().oneOf(['all', ...departmentValues]),
    isActive: yup.string().oneOf(['all', 'active', 'inactive']),
  });

// Default schema for backward compatibility
export const labTestFilterSchema = yup.object({
  searchText: yup.string().trim(),
  department: yup.string(),
  isActive: yup.string().oneOf(['all', 'active', 'inactive']),
});

export type LabTestFilterSchema = yup.InferType<typeof labTestFilterSchema>;

// Bulk update form schema
export const bulkUpdateSchema = yup.object({
  selectedTests: yup
    .array()
    .of(yup.string().required())
    .min(1, 'Please select at least one test'),
  action: yup
    .string()
    .oneOf(['activate', 'deactivate', 'updatePrice', 'updateDepartments'])
    .required(),
  price: yup
    .number()
    .min(0, 'Price must be non-negative')
    .when('action', {
      is: 'updatePrice',
      then: (schema) => schema.required('Price is required'),
      otherwise: (schema) => schema.optional(),
    }),
  departments: yup
    .array()
    .of(yup.string().oneOf(DEPARTMENT_OPTIONS))
    .when('action', {
      is: 'updateDepartments',
      then: (schema) => schema.min(1, 'Please select at least one department'),
      otherwise: (schema) => schema.optional(),
    }),
});

export type BulkUpdateSchema = yup.InferType<typeof bulkUpdateSchema>;

// Individual lab test update schema
export const labTestUpdateSchema = yup.object({
  testId: yup.string().required(),
  isActive: yup.boolean().required(),
  price: yup.number().min(0, 'Price must be non-negative').optional(),
  departments: yup
    .array()
    .of(yup.string().oneOf(DEPARTMENT_OPTIONS))
    .default([]),
});

export type LabTestUpdateSchema = yup.InferType<typeof labTestUpdateSchema>;

// Price update schema for inline editing
export const priceUpdateSchema = yup.object({
  price: yup
    .number()
    .min(0, 'Price must be non-negative')
    .max(999999, 'Price is too high')
    .required('Price is required'),
});

export type PriceUpdateSchema = yup.InferType<typeof priceUpdateSchema>;

// Department selection schema
export const departmentSelectionSchema = yup.object({
  departments: yup
    .array()
    .of(yup.string().oneOf(DEPARTMENT_OPTIONS))
    .min(1, 'Please select at least one department'),
});

export type DepartmentSelectionSchema = yup.InferType<
  typeof departmentSelectionSchema
>;
