import { LAB_TEST_DEPARTMENT_ENDPOINT } from '../../../constants/api-endpoints';
import api from '../../../services/api';

export interface LabTestDepartment {
  value: string;
  label: string;
}

// Fetch departments from API
export const fetchLabTestDepartments = async (): Promise<
  LabTestDepartment[]
> => {
  try {
    const response = await api.get<string[]>(
      `${LAB_TEST_DEPARTMENT_ENDPOINT}/departments`
    );

    const departments: LabTestDepartment[] = [
      ...response.data.map((dept: string) => ({ value: dept, label: dept })),
      { value: 'others', label: 'Others' },
    ];

    return departments;
  } catch {
    // Return default departments if API fails
    return [
      { value: 'Clinical Chemistry', label: 'Clinical Chemistry' },
      { value: 'Hematology / Blood Count', label: 'Hematology / Blood Count' },
      { value: 'Microbiology', label: 'Microbiology' },
      { value: 'Serology', label: 'Serology' },
      { value: 'Surgical/Anatomic Path', label: 'Surgical/Anatomic Path' },
      { value: 'Radiology / Imaging Obs', label: 'Radiology / Imaging Obs' },
      { value: 'others', label: 'Others' },
    ];
  }
};

// Get department filter value for API
export const getDepartmentFilterValue = (
  selectedDepartment: string
): string => {
  if (!selectedDepartment || selectedDepartment === '') {
    return 'ALL';
  }
  if (selectedDepartment === 'others') {
    return 'Others';
  }
  return selectedDepartment;
};
