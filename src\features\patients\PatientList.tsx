import React from 'react';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import OrganizationRequired from '../../components/Common/OrganizationRequired';
import { useAuth } from '../../hooks/useAuth';
import { getCurrentOrganizationId } from '../../utils/auth-utils';
import PatientListComponent from './components/PatientListComponent';

const PatientList: React.FC = () => {
  const {
    selectedOrganization,
    isSuperAdmin,
    isOrganizationAdmin,
    userOrganizationId,
  } = useAuth();

  // For super admins, require organization selection
  if (isSuperAdmin) {
    // If organization is selected from header, use the selected organization ID
    if (selectedOrganization) {
      return (
        <PatientListComponent
          organizationName={selectedOrganization.name}
          organizationId={selectedOrganization.id ?? null}
        />
      );
    } else {
      // If no organization selected, show organization selection required
      return (
        <OrganizationRequired
          feature='patients'
          customMessage='Please select an organization to view patients.'
          customTitle='Organization Required'
        />
      );
    }
  }

  // For organization admins, use the organizationId from the user's auth info
  if (isOrganizationAdmin) {
    const orgId =
      userOrganizationId ||
      selectedOrganization?.id ||
      getCurrentOrganizationId();

    if (orgId) {
      const organizationName =
        selectedOrganization?.name || 'Your Organization';

      return (
        <PatientListComponent
          organizationName={organizationName}
          organizationId={orgId}
        />
      );
    } else {
      // If no organizationId, show error message
      return (
        <div className='p-4 text-center text-red-500'>
          Organization not found. Please contact support.
        </div>
      );
    }
  }

  // Show loading state while checking permissions
  return (
    <div className='flex h-64 items-center justify-center'>
      <LoadingSpinner text='Loading patients...' />
    </div>
  );
};

export default PatientList;
