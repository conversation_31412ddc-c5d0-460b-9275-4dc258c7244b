import React, { forwardRef } from 'react';
import { FieldError } from 'react-hook-form';

interface FormTextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  error?: FieldError;
  required?: boolean;
  helperText?: string;
}

const FormTextarea = forwardRef<HTMLTextAreaElement, FormTextareaProps>(
  ({ label, error, required, helperText, className = '', ...props }, ref) => {
    const baseClasses =
      'mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500';
    const errorClasses = error
      ? 'border-red-300 focus:border-red-300 focus:ring-red-500'
      : 'border-gray-300';
    const textareaClasses = `${baseClasses} ${errorClasses} ${className}`;

    return (
      <div className='w-full'>
        <label className='block text-sm font-medium text-gray-700'>
          {label}
          {required && <span className='text-red-500 ml-1'>*</span>}
        </label>
        <textarea ref={ref} className={textareaClasses} {...props} />
        {error && <p className='mt-1 text-sm text-red-600'>{error.message}</p>}
        {helperText && !error && (
          <p className='mt-1 text-sm text-gray-500'>{helperText}</p>
        )}
      </div>
    );
  }
);

FormTextarea.displayName = 'FormTextarea';

export default FormTextarea;
