// filepath: src/config/api.ts

// Azure AD B2C Configuration - DISABLED - Using only Cognito
/*
const CONFIG = {
  // Client and Tenant
  CLIENT_ID: 'f22ad9c9-3fe2-4921-b825-ed8b887f3ab7',
  TENANT_NAME: 'erm20240520',

  // B2C Policy Names
  SIGN_UP_SIGN_IN_POLICY: 'B2C_1_emrapp',
  PASSWORD_RESET_POLICY: 'B2C_1_reset_v3',
  EDIT_PROFILE_POLICY: 'B2C_1_edit_profile_v2',

  // Authority URLs
  AUTHORITY_DOMAIN: 'erm20240520.b2clogin.com',
  TENANT_ID: 'erm20240520.onmicrosoft.com',

  // API Scopes
  API_SCOPE: 'f22ad9c9-3fe2-4921-b825-ed8b887f3ab7',
  OPENID_SCOPE: 'openid',
  PROFILE_SCOPE: 'profile',
  OFFLINE_ACCESS_SCOPE: 'offline_access',
};

export default CONFIG;
*/

// Export empty config since we're not using B2C
export const CONFIG = {};
