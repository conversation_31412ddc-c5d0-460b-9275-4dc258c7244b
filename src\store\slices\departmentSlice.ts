import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import { generateMockApiResponse, mockDepartments } from '../../data/mockData';
import { Department, PaginatedResponse } from '../../types';

interface DepartmentState {
  departments: Department[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: DepartmentState = {
  departments: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

export const fetchDepartments = createAsyncThunk(
  'departments/fetchDepartments',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    let filteredDepartments = mockDepartments.filter(
      (dep) => dep.organizationId === params.organizationId
    );

    if (params.search) {
      filteredDepartments = filteredDepartments.filter((dep) =>
        dep.name.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    if (params.status) {
      filteredDepartments = filteredDepartments.filter(
        (dep) => dep.status === params.status
      );
    }

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredDepartments.slice(startIndex, endIndex);

    const response: PaginatedResponse<Department> = {
      data: paginatedData,
      total: filteredDepartments.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 500);
  }
);

export const createDepartment = createAsyncThunk(
  'departments/createDepartment',
  async (departmentData: Omit<Department, 'id' | 'createdAt'>) => {
    const newDepartment: Department = {
      id: uuidv4(),
      ...departmentData,
      createdAt: new Date(),
    };
    mockDepartments.unshift(newDepartment); // Add to the beginning of the mock data array
    return await generateMockApiResponse(newDepartment, 800);
  }
);

export const updateDepartment = createAsyncThunk(
  'departments/updateDepartment',
  async ({
    id,
    data,
  }: {
    id: string;
    data: Partial<Omit<Department, 'id' | 'createdAt'>>;
  }) => {
    const index = mockDepartments.findIndex((dep) => dep.id === id);
    if (index === -1) {
      throw new Error('Department not found');
    }
    const existingDepartment = mockDepartments[index];
    if (!existingDepartment) {
      throw new Error('Department not found');
    }

    const updatedDepartment: Department = {
      id: existingDepartment.id,
      name: data.name ?? existingDepartment.name,
      status: data.status ?? existingDepartment.status,
      organizationId: data.organizationId ?? existingDepartment.organizationId,
      createdAt: existingDepartment.createdAt,
    };

    // Handle optional description field properly
    if (data.description !== undefined) {
      updatedDepartment.description = data.description;
    } else if (existingDepartment.description !== undefined) {
      updatedDepartment.description = existingDepartment.description;
    }
    mockDepartments[index] = updatedDepartment;
    return await generateMockApiResponse(updatedDepartment, 800);
  }
);

const departmentSlice = createSlice({
  name: 'departments',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchDepartments.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDepartments.fulfilled, (state, action) => {
        state.loading = false;
        state.departments = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchDepartments.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch departments';
      })
      .addCase(createDepartment.fulfilled, (state, action) => {
        state.departments.unshift(action.payload.data);
        state.total += 1;
      })
      .addCase(updateDepartment.fulfilled, (state, action) => {
        const index = state.departments.findIndex(
          (dep) => dep.id === action.payload.data.id
        );
        if (index !== -1) {
          state.departments[index] = action.payload.data;
        }
      });
  },
});

export default departmentSlice.reducer;
