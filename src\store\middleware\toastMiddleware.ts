import { Action, Middleware } from '@reduxjs/toolkit';

import { clearSuccessMessage } from '../rootActions';

// Global toast handler - will be set by the ToastProvider
let globalToastHandler: {
  success: (title: string, message?: string) => void;
  error: (title: string, message?: string) => void;
  warning: (title: string, message?: string) => void;
  info: (title: string, message?: string) => void;
} | null = null;

// Track the last shown message to prevent duplicates
let lastMessage: { key: string; timestamp: number } | null = null;
const MESSAGE_DEBOUNCE = 3000; // 3 seconds

export const setGlobalToastHandler = (handler: typeof globalToastHandler) => {
  globalToastHandler = handler;
};

// Enhanced function to check if an error is from a cancelled request
const isCancelledRequestError = (error: unknown): boolean => {
  if (!error) return false;

  // Type guard to check if error has the expected properties
  const errorObj = error as Record<string, unknown>;

  // Check for our custom RequestCancelledError
  if (errorObj?.name === 'RequestCancelledError') {
    return true;
  }

  // Check if it's a subscription expiration error (should not show toast)
  if (errorObj?.isSubscriptionExpired || errorObj?.skipToast) {
    return true;
  }

  // Check for axios cancel token cancellation
  if (errorObj?.name === 'CanceledError' || errorObj?.name === 'Cancel') {
    return true;
  }

  // Check message content (case-insensitive)
  const message = errorObj?.message || error || '';
  if (typeof message === 'string') {
    const lowerMessage = message.toLowerCase();
    if (
      lowerMessage.includes('cancel') ||
      lowerMessage.includes('abort') ||
      lowerMessage.includes('request was cancelled') ||
      lowerMessage.includes('operation was cancelled') ||
      lowerMessage.includes('subscription expired')
    ) {
      return true;
    }
  }

  // Check error code/status
  if (
    errorObj?.code === 'ERR_CANCELED' ||
    errorObj?.code === 'CANCELLED' ||
    errorObj?.status === 'cancelled' ||
    errorObj?.status === 'canceled'
  ) {
    return true;
  }

  // Check if axios.isCancel would return true (for axios errors)
  if (errorObj?.__CANCEL__ === true) {
    return true;
  }

  return false;
};

export const toastMiddleware: Middleware =
  (store) => (next) => (action: unknown) => {
    if (!globalToastHandler || !isAction(action)) {
      return next(action);
    }

    // Get state before the action
    const stateBefore = store.getState();

    // Execute the action
    const result = next(action);

    // Only process fulfilled/rejected actions that just completed
    if (
      !action.type.endsWith('/fulfilled') &&
      !action.type.endsWith('/rejected')
    ) {
      return result;
    }

    // Get state after the action
    const stateAfter = store.getState();
    const actionSlice = action.type.split('/')[0];

    if (!actionSlice || !stateAfter[actionSlice as keyof typeof stateAfter]) {
      return result;
    }

    const sliceStateBefore =
      stateBefore[actionSlice as keyof typeof stateBefore];
    const sliceStateAfter = stateAfter[actionSlice as keyof typeof stateAfter];
    const now = Date.now();

    // Only show toasts for messages that were JUST SET by this action
    // Compare before and after states to detect new messages

    // Handle success messages - only for fulfilled actions
    if (action.type.endsWith('/fulfilled')) {
      const successMessageBefore = sliceStateBefore?.successMessage;
      const successMessageAfter = sliceStateAfter?.successMessage;

      // Only show toast if the success message was just set (changed from null/undefined to a value)
      if (
        successMessageAfter &&
        successMessageAfter !== successMessageBefore &&
        (!successMessageBefore || successMessageBefore === null)
      ) {
        const messageKey = `success-${actionSlice}-${successMessageAfter}`;

        // Only show if this is a new message or the same message after debounce period
        if (
          !lastMessage ||
          lastMessage.key !== messageKey ||
          now - lastMessage.timestamp > MESSAGE_DEBOUNCE
        ) {
          // Show the toast
          globalToastHandler.success('Success', successMessageAfter);

          // Clear the message from state after showing
          setTimeout(() => {
            store.dispatch(clearSuccessMessage({ slice: actionSlice }));
          }, 100);

          // Update last message
          lastMessage = { key: messageKey, timestamp: now };
        }
      }
    }
    // Handle error messages - only for rejected actions
    else if (action.type.endsWith('/rejected')) {
      const errorBefore =
        sliceStateBefore?.error || sliceStateBefore?.errorMessage;
      const errorAfter =
        sliceStateAfter?.error || sliceStateAfter?.errorMessage;

      // Only show toast if the error message was just set (changed from null/undefined to a value)
      if (
        errorAfter &&
        errorAfter !== errorBefore &&
        (!errorBefore || errorBefore === null)
      ) {
        // Skip showing toast for cancelled requests
        if (isCancelledRequestError(errorAfter)) {
          return result;
        }

        // Skip showing toast for subscription expiration errors
        if (
          typeof errorAfter === 'object' &&
          errorAfter !== null &&
          (errorAfter as any).isSubscriptionExpired
        ) {
          return result;
        }

        // Skip showing toast if the error has skipToast flag
        if (
          typeof errorAfter === 'object' &&
          errorAfter !== null &&
          (errorAfter as any).skipToast
        ) {
          return result;
        }

        // Skip showing toast for subscription expiry messages
        if (
          typeof errorAfter === 'string' &&
          (errorAfter.includes('Your subscription expired. Need to upgrade.') ||
            errorAfter.toLowerCase().includes('subscription expired'))
        ) {
          return result;
        }

        // Only show non-cancellation errors
        const messageKey = `error-${actionSlice}-${errorAfter}`;
        if (
          !lastMessage ||
          lastMessage.key !== messageKey ||
          now - lastMessage.timestamp > MESSAGE_DEBOUNCE
        ) {
          globalToastHandler.error('Error', errorAfter);
          lastMessage = { key: messageKey, timestamp: now };
        }
      }
    }

    return result;
  };

// Type guard for action
function isAction(action: unknown): action is Action {
  return (action as Action).type !== undefined;
}

// Helper function to show custom toasts from components
export const showToast = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message?: string
) => {
  if (!globalToastHandler) {
    return;
  }

  const messageKey = `${type}-${title}-${message || ''}`;
  const now = Date.now();

  // Only show if this is a new message or the same message after debounce period
  if (
    !lastMessage ||
    lastMessage.key !== messageKey ||
    now - lastMessage.timestamp > MESSAGE_DEBOUNCE
  ) {
    globalToastHandler[type](title, message);
    lastMessage = { key: messageKey, timestamp: now };
  }
};
