import { useCallback, useState } from 'react';

import doctorProfileService, {
  DoctorProfileResponse,
} from '../../../services/doctorProfile.service';
import invoiceService, {
  InvoiceDetailResponse,
} from '../../../store/features/invoices/invoice.service';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';

export const useDoctorProfile = (organizationId?: string | null) => {
  const [doctorProfile, setDoctorProfile] =
    useState<DoctorProfileResponse | null>(null);
  const [invoiceDetail, setInvoiceDetail] =
    useState<InvoiceDetailResponse | null>(null);
  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const currentOrgId = organizationId ?? getCurrentOrganizationId();

  const fetchDoctorProfile = useCallback(
    async (invoiceId?: string, doctorEmail?: string) => {
      if (!currentOrgId && !doctorEmail) {
        setError('Organization ID is required to fetch doctor details.');
        return;
      }

      setLoading(true);
      setError(null);

      try {
        let email = doctorEmail;
        let fetchedInvoiceDetail: InvoiceDetailResponse | null = null;

        if (!email) {
          if (!invoiceId || !currentOrgId) {
            throw new Error('Doctor email not available.');
          }

          fetchedInvoiceDetail = await invoiceService.fetchInvoiceById({
            organizationId: currentOrgId,
            id: invoiceId,
          });

          email =
            fetchedInvoiceDetail.doctor?.email ||
            fetchedInvoiceDetail.billDetails?.doctorEmail ||
            fetchedInvoiceDetail.created_by_email ||
            '';
        }

        if (!email) {
          throw new Error('Doctor email was not provided in the invoice.');
        }

        const profile =
          await doctorProfileService.fetchDoctorProfileByEmail(email);

        setDoctorProfile(profile);
        setInvoiceDetail(fetchedInvoiceDetail);

        if (profile?.id) {
          try {
            const pictureData =
              await doctorProfileService.fetchDoctorProfilePicture(profile.id);
            setProfilePictureUrl(pictureData.profilePictureUrl || null);
          } catch (pictureError) {
            console.error(
              'Failed to load doctor profile picture',
              pictureError
            );
            setProfilePictureUrl(null);
          }
        }
      } catch (fetchError) {
        console.error('Failed to load doctor profile', fetchError);
        setDoctorProfile(null);
        setInvoiceDetail(null);
        setProfilePictureUrl(null);
        setError('Unable to load doctor profile. Please try again later.');
      } finally {
        setLoading(false);
      }
    },
    [currentOrgId]
  );

  const clearDoctorProfile = useCallback(() => {
    setDoctorProfile(null);
    setInvoiceDetail(null);
    setProfilePictureUrl(null);
    setError(null);
  }, []);

  return {
    doctorProfile,
    invoiceDetail,
    profilePictureUrl,
    loading,
    error,
    fetchDoctorProfile,
    clearDoctorProfile,
  };
};
