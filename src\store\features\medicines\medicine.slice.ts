import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { removeMedicines as removeMedicinesService } from '../../../features/medicines/services/medicine.service';
import {
  MedicineListItem,
  MedicineListParams,
  UpdateMedicineRequest,
} from '../../../features/medicines/types/medicine.types';
import medicineService from './medicine.service';

// Bulk update state interface
interface BulkUpdateState {
  status: any | null; // BulkUpdateStatus type
  isPolling: boolean;
  error: string | null;
  persistentPolling: boolean;
}

interface MedicineState {
  medicines: MedicineListItem[];
  loading: boolean;
  updating: boolean;
  error: string | null;
  successMessage: string | null;
  total: number;
  page: number;
  limit: number;
  searchText: string;
  isActiveFilter: string;
  selectedMedicines: string[];
  isAllSelected: boolean;
  continuationToken: string;
  hasMoreResults: boolean;
  totalFetched: number;
  totalPages: number;
  bulkUpdate: BulkUpdateState;
}

const initialState: MedicineState = {
  medicines: [],
  loading: true, // Start with loading true to show loading initially
  updating: false,
  error: null,
  successMessage: null,
  total: 0,
  page: 1,
  limit: 100,
  searchText: '',
  isActiveFilter: 'all',
  selectedMedicines: [],
  isAllSelected: false,
  continuationToken: '',
  hasMoreResults: false,
  totalFetched: 0,
  totalPages: 1,
  bulkUpdate: {
    status: null,
    isPolling: false,
    error: null,
    persistentPolling: false,
  },
};

export const fetchMedicines = createAsyncThunk(
  'medicines/fetchMedicines',
  async (params: MedicineListParams) => {
    const response = await medicineService.fetchMedicinesList(params);
    return response;
  }
);

export const updateMedicines = createAsyncThunk(
  'medicines/updateMedicines',
  async (data: UpdateMedicineRequest) => {
    const response = await medicineService.updateMedicines(data);
    return response;
  }
);

export const removeMedicines = createAsyncThunk(
  'medicines/removeMedicines',
  async (data: {
    organizationId: string;
    medicines?: string[];
    selectAll?: boolean;
  }) => {
    const response = await removeMedicinesService(data);
    return response;
  }
);

const medicineSlice = createSlice({
  name: 'medicines',
  initialState,
  reducers: {
    setSearchText: (state, action: PayloadAction<string>) => {
      state.searchText = action.payload;
      state.page = 1;
      state.loading = true; // Set loading when search changes
      // Clear existing data to prevent showing old data while loading
      state.medicines = [];
      state.total = 0;
    },
    setIsActiveFilter: (state, action: PayloadAction<string>) => {
      state.isActiveFilter = action.payload;
      state.page = 1;
      state.loading = true; // Set loading when filter changes
      // Clear existing data to prevent showing old data while loading
      state.medicines = [];
      state.total = 0;
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload;
    },
    setLimit: (state, action: PayloadAction<number>) => {
      state.limit = action.payload;
      state.page = 1;
    },
    selectMedicine: (state, action: PayloadAction<string>) => {
      const medicineId = action.payload;
      if (state.selectedMedicines.includes(medicineId)) {
        state.selectedMedicines = state.selectedMedicines.filter(
          (id) => id !== medicineId
        );
      } else {
        state.selectedMedicines.push(medicineId);
      }
    },
    selectAllMedicines: (state) => {
      if (state.isAllSelected) {
        state.selectedMedicines = [];
        state.isAllSelected = false;
      } else {
        state.selectedMedicines = state.medicines.map(
          (medicine) => medicine.medicineId
        );
        state.isAllSelected = true;
      }
    },
    clearSelection: (state) => {
      state.selectedMedicines = [];
      state.isAllSelected = false;
    },
    updateMedicineInList: (
      state,
      action: PayloadAction<{
        medicineId: string;
        updates: Partial<MedicineListItem>;
      }>
    ) => {
      const { medicineId, updates } = action.payload;
      const index = state.medicines.findIndex(
        (medicine) => medicine.medicineId === medicineId
      );
      if (index !== -1) {
        const medicine = state.medicines[index];
        if (medicine) {
          Object.assign(medicine, updates);
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    },
    clearSuccessMessage: (state) => {
      state.successMessage = null;
    },
    clearMessages: (state) => {
      state.error = null;
      state.successMessage = null;
    },
    resetMedicineState: () => initialState,

    // Handle organization changes - set loading state and clear data
    handleOrganizationChange: (state) => {
      state.loading = true;
      state.error = null;
      // Clear existing data to prevent showing old data while loading
      state.medicines = [];
      state.total = 0;
      // Reset selection
      state.selectedMedicines = [];
      state.isAllSelected = false;
    },

    // Manual loading state setter
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Bulk update actions
    startBulkUpdatePolling: (state) => {
      state.bulkUpdate.isPolling = true;
      state.bulkUpdate.persistentPolling = true;
      state.bulkUpdate.error = null;
    },

    updateBulkUpdateStatus: (
      state,
      action: PayloadAction<any> // BulkUpdateStatus
    ) => {
      state.bulkUpdate.status = action.payload;
      // Persist to localStorage for cross-page navigation
      if (action.payload) {
        localStorage.setItem(
          'medicinesBulkStatus',
          JSON.stringify({
            status: action.payload,
            isPolling: state.bulkUpdate.isPolling,
            persistentPolling: state.bulkUpdate.persistentPolling,
            selectedMedicines: state.selectedMedicines,
            isAllSelected: state.isAllSelected,
          })
        );
      }
    },

    stopBulkUpdatePolling: (state) => {
      state.bulkUpdate.isPolling = false;
      state.bulkUpdate.persistentPolling = false;
      localStorage.removeItem('medicinesBulkStatus');
    },

    restoreBulkUpdateStatus: (
      state,
      action: PayloadAction<{
        status: any; // BulkUpdateStatus
        isPolling: boolean;
        persistentPolling: boolean;
        selectedMedicines?: string[];
        isAllSelected?: boolean;
      }>
    ) => {
      state.bulkUpdate.status = action.payload.status;
      state.bulkUpdate.isPolling = action.payload.isPolling;
      state.bulkUpdate.persistentPolling = action.payload.persistentPolling;

      // Restore selection state if bulk operation is in progress
      if (
        action.payload.status?.status === 'PROCESSING' ||
        action.payload.status?.status === 'PENDING'
      ) {
        if (action.payload.selectedMedicines !== undefined) {
          state.selectedMedicines = action.payload.selectedMedicines;
        }
        if (action.payload.isAllSelected !== undefined) {
          state.isAllSelected = action.payload.isAllSelected;
        }
      }
    },

    clearBulkUpdateStatus: (state) => {
      state.bulkUpdate = {
        status: null,
        isPolling: false,
        error: null,
        persistentPolling: false,
      };
      localStorage.removeItem('medicinesBulkStatus');
      // Clear selection state when bulk operation is cleared
      state.selectedMedicines = [];
      state.isAllSelected = false;
    },

    setBulkUpdateError: (state, action: PayloadAction<string>) => {
      state.bulkUpdate.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMedicines.pending, (state) => {
        state.loading = true;
        state.error = null;
        // Clear existing data when starting a new fetch to prevent showing old data
        if (state.medicines.length > 0) {
          state.medicines = [];
          state.total = 0;
        }
      })
      .addCase(fetchMedicines.fulfilled, (state, action) => {
        state.loading = false;
        state.medicines = action.payload.medicines;
        state.total = action.payload.totalRecords;
        state.page = action.payload.currentPage;
        state.continuationToken = action.payload.continuationToken || '';
        state.hasMoreResults = action.payload.hasMoreResults || false;
        state.totalFetched = action.payload.totalFetched || 0;
        state.totalPages = action.payload.totalPages || 1;
        if (state.isAllSelected) {
          state.selectedMedicines = action.payload.medicines.map(
            (medicine: MedicineListItem) => medicine.medicineId
          );
        } else {
          state.selectedMedicines = [];
        }
      })
      .addCase(fetchMedicines.rejected, (state, action) => {
        // Don't set loading to false if the request was cancelled (duplicate request)
        const error = action.payload as any;
        if (
          error?.name === 'RequestCancelledError' ||
          error?.message === 'Request was cancelled'
        ) {
          return; // Keep loading state as is for cancelled requests
        }

        state.loading = false;
        state.error = action.error.message || 'Failed to fetch medicines';
      })
      .addCase(updateMedicines.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(updateMedicines.fulfilled, (state, action) => {
        state.updating = false;
        state.successMessage =
          action.payload.message || 'Medicines updated successfully';
        state.selectedMedicines = [];
      })
      .addCase(updateMedicines.rejected, (state, action) => {
        state.updating = false;
        state.error = action.error.message || 'Failed to update medicines';
      })
      .addCase(removeMedicines.pending, (state) => {
        state.updating = true;
        state.error = null;
      })
      .addCase(removeMedicines.fulfilled, (state, _action) => {
        state.updating = false;
        state.successMessage = 'Medicines removed successfully';
        state.selectedMedicines = [];
        state.isAllSelected = false;
      })
      .addCase(removeMedicines.rejected, (state, action) => {
        state.updating = false;
        state.error = action.error.message || 'Failed to remove medicines';
      });
  },
});

export const {
  setSearchText,
  setIsActiveFilter,
  setPage,
  setLimit,
  selectMedicine,
  selectAllMedicines,
  clearSelection,
  updateMedicineInList,
  clearError,
  clearSuccessMessage,
  clearMessages,
  resetMedicineState,
  handleOrganizationChange,
  setLoading,
  startBulkUpdatePolling,
  stopBulkUpdatePolling,
  updateBulkUpdateStatus,
  restoreBulkUpdateStatus,
  clearBulkUpdateStatus,
  setBulkUpdateError,
} = medicineSlice.actions;

export default medicineSlice.reducer;
