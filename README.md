# EMR Admin Module

A comprehensive Electronic Medical Records (EMR) administration system built with React, TypeScript, and Redux Toolkit.

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn package manager

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd arcaAi-admin

# Install dependencies
npm install

# Start development server
npm run dev
```

### Development Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
npm run lint:fix     # Auto-fix ESLint issues
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking
```

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) folder:

- **[📖 Documentation Index](./docs/INDEX.md)** - Complete overview of all documentation
- **[📊 Application Analysis](./docs/APPLICATION_ANALYSIS.md)** - Technical architecture and feature analysis
- **[🛠️ Code Quality Setup](./docs/CODE_QUALITY_SETUP.md)** - Development tools and standards

## 🏗️ Architecture

- **Frontend**: React 18 with TypeScript
- **State Management**: Redux Toolkit with RTK Query
- **Styling**: Tailwind CSS with Material-UI components
- **Forms**: React Hook Form with Yup validation
- **Build Tool**: Vite
- **Code Quality**: ESLint, Prettier, Husky pre-commit hooks

## ✨ Features

- 👥 User Management (CRUD operations)
- 🏢 Organization Management
- 🏥 Department & Branch Management
- 👨‍⚕️ Role-based Access Control
- 📋 Template Management
- 🩺 Vital Signs Management
- 🏦 Bank Information Management
- 🌐 Multi-language Support
- 📱 Responsive Design

## 🔧 Development

This project includes a comprehensive code quality setup with:

- **Automatic formatting** on save (Prettier)
- **Import organization** and unused import removal
- **Pre-commit hooks** for code validation
- **TypeScript strict mode** for type safety
- **ESLint rules** for code quality

See the [Code Quality Setup](./docs/CODE_QUALITY_SETUP.md) for detailed information.

## 🤝 Contributing

1. Read the [Application Analysis](./docs/APPLICATION_ANALYSIS.md) to understand the project structure
2. Follow the [Code Quality Setup](./docs/CODE_QUALITY_SETUP.md) to configure your environment
3. Create a feature branch from `main`
4. Make your changes with proper testing
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

---

For detailed technical documentation, please visit the [`docs/`](./docs/) folder.
