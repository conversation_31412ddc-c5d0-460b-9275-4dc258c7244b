import { Button } from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router-dom';

interface ErrorPageProps {
  title?: string;
  message?: string;
  error?: Error;
}

const CommonErrorPage: React.FC<ErrorPageProps> = ({
  title = 'Something went wrong',
  message = 'An unexpected error occurred. Please try again later.',
  error,
}) => {
  const navigate = useNavigate();

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-100'>
      <div className='max-w-md w-full px-6 py-8 bg-white rounded-lg shadow-md'>
        <div className='text-center'>
          <svg
            className='mx-auto h-16 w-16 text-red-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'
            />
          </svg>
          <h2 className='mt-4 text-2xl font-semibold text-gray-800'>{title}</h2>
          <p className='mt-2 text-gray-600'>{message}</p>
          {error && process.env.NODE_ENV === 'development' && (
            <pre className='mt-4 p-4 bg-gray-100 rounded text-left text-sm overflow-auto'>
              {error.message}
            </pre>
          )}
          <div className='mt-6 space-x-4'>
            <Button
              variant='contained'
              color='primary'
              onClick={() => navigate('/')}
            >
              Return to Home
            </Button>
            <Button variant='outlined' onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommonErrorPage;
