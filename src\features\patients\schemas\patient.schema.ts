import * as yup from 'yup';

export const patientFilterSchema = yup.object().shape({
  searchText: yup.string().optional(),
  gender: yup.string().oneOf(['', 'Male', 'Female', 'Other']).optional(),
  fromAge: yup.number().min(0).max(150).nullable().optional(),
  toAge: yup.number().min(0).max(150).nullable().optional(),
  fromDate: yup.string().optional(),
  toDate: yup.string().optional(),
});

export type PatientFilterSchema = yup.InferType<typeof patientFilterSchema>;
