import { SVGProps } from 'react';

const YellowLockIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='27'
    viewBox='0 0 24 27'
    fill='none'
    {...props}
  >
    <path
      d='M10.208 0C8.9257 0 7.69591 0.509393 6.78918 1.41612C5.88244 2.32284 5.37305 3.55263 5.37305 4.83493V6.98379H6.9847V4.83493C6.9847 3.98006 7.3243 3.16021 7.92879 2.55572C8.53327 1.95124 9.35314 1.61164 10.208 1.61164C11.0629 1.61164 11.8827 1.95124 12.4872 2.55572C13.0917 3.16021 13.4313 3.98006 13.4313 4.83493V6.98379H15.043V4.83493C15.043 3.55263 14.5336 2.32284 13.6268 1.41612C12.7201 0.509393 11.4903 0 10.208 0Z'
      fill='#D9D9D9'
    />
    <path
      d='M10.208 0C8.9257 0 7.69591 0.509393 6.78918 1.41612C5.88244 2.32284 5.37305 3.55263 5.37305 4.83493V6.98379H6.9847V4.83493C6.9847 3.98006 7.3243 3.16021 7.92879 2.55572C8.53327 1.95124 9.35314 1.61164 10.208 1.61164C11.0629 1.61164 11.8827 1.95124 12.4872 2.55572C13.0917 3.16021 13.4313 3.98006 13.4313 4.83493V6.98379H15.043V4.83493C15.043 3.55263 14.5336 2.32284 13.6268 1.41612C12.7201 0.509393 11.4903 0 10.208 0Z'
      fill='url(#paint0_linear_5816_79643)'
    />
    <path
      d='M20.4143 10.4759C20.4143 9.4073 19.9898 8.38248 19.2342 7.62688C18.4786 6.87127 17.4538 6.44678 16.3852 6.44678H4.02914C2.96054 6.44678 1.93572 6.87127 1.18011 7.62688C0.424497 8.38248 0 9.4073 0 10.4759V20.683C0 21.7516 0.424497 22.7764 1.18011 23.532C1.93572 24.2876 2.96054 24.7121 4.02914 24.7121H16.3852C17.4538 24.7121 18.4786 24.2876 19.2342 23.532C19.9898 22.7764 20.4143 21.7516 20.4143 20.683V10.4759Z'
      fill='url(#paint1_linear_5816_79643)'
    />
    <path
      d='M10.2074 17.1906C10.6348 17.1906 11.0447 17.0208 11.347 16.7185C11.6492 16.4163 11.819 16.0064 11.819 15.5789C11.819 15.1515 11.6492 14.7416 11.347 14.4393C11.0447 14.1371 10.6348 13.9673 10.2074 13.9673C9.77992 13.9673 9.36999 14.1371 9.06775 14.4393C8.7655 14.7416 8.5957 15.1515 8.5957 15.5789C8.5957 16.0064 8.7655 16.4163 9.06775 16.7185C9.36999 17.0208 9.77992 17.1906 10.2074 17.1906Z'
      fill='url(#paint2_radial_5816_79643)'
    />
    <path
      d='M20.4143 10.4759C20.4143 9.4073 19.9898 8.38248 19.2342 7.62688C18.4786 6.87127 17.4538 6.44678 16.3852 6.44678H4.02914C2.96054 6.44678 1.93572 6.87127 1.18011 7.62688C0.424497 8.38248 0 9.4073 0 10.4759V20.683C0 21.7516 0.424497 22.7764 1.18011 23.532C1.93572 24.2876 2.96054 24.7121 4.02914 24.7121H16.3852C17.4538 24.7121 18.4786 24.2876 19.2342 23.532C19.9898 22.7764 20.4143 21.7516 20.4143 20.683V10.4759Z'
      fill='url(#paint3_radial_5816_79643)'
    />
    <path
      d='M18.3703 13.1401C19.2277 13.9674 20.8748 15.2803 22.9001 15.55C23.3041 15.6037 23.6372 15.9121 23.6372 16.2978V19.9025C23.6372 24.7482 18.9365 26.5071 17.9212 26.8305C17.7959 26.8696 17.6617 26.8696 17.5365 26.8305C16.5201 26.506 11.8184 24.7482 11.8184 19.9025V16.2978C11.8184 15.9121 12.1514 15.6037 12.5554 15.55C14.5797 15.2814 16.2278 13.9663 17.0842 13.1401C17.2606 12.9811 17.4897 12.8931 17.7272 12.8931C17.9647 12.8931 18.1938 12.9811 18.3703 13.1401Z'
      fill='url(#paint4_linear_5816_79643)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_5816_79643'
        x1='7.44564'
        y1='-1.33444'
        x2='15.3889'
        y2='11.1526'
        gradientUnits='userSpaceOnUse'
      >
        <stop stop-color='#FFC205' />
        <stop offset='1' stop-color='#FB5937' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_5816_79643'
        x1='21.8723'
        y1='25.8542'
        x2='2.85812'
        y2='8.53642'
        gradientUnits='userSpaceOnUse'
      >
        <stop stop-color='#FF6F47' />
        <stop offset='1' stop-color='#FFCD0F' />
      </linearGradient>
      <radialGradient
        id='paint2_radial_5816_79643'
        cx='0'
        cy='0'
        r='1'
        gradientTransform='matrix(-1.61161 -5.64075 7.82661 -2.2361 11.0132 17.1906)'
        gradientUnits='userSpaceOnUse'
      >
        <stop stop-color='#944600' />
        <stop offset='1' stop-color='#CD8E02' />
      </radialGradient>
      <radialGradient
        id='paint3_radial_5816_79643'
        cx='0'
        cy='0'
        r='1'
        gradientTransform='matrix(-6.0245 4.87973 -5.42929 -6.7029 16.7677 20.9062)'
        gradientUnits='userSpaceOnUse'
      >
        <stop stop-color='#EB4824' />
        <stop offset='0.99' stop-color='#EB4824' stop-opacity='0' />
      </radialGradient>
      <linearGradient
        id='paint4_linear_5816_79643'
        x1='14.0349'
        y1='12.893'
        x2='24.5364'
        y2='25.6433'
        gradientUnits='userSpaceOnUse'
      >
        <stop stop-color='#62BE55' />
        <stop offset='1' stop-color='#1E794A' />
      </linearGradient>
    </defs>
  </svg>
);

export default YellowLockIcon;
