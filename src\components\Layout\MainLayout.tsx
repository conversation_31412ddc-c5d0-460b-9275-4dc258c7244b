import React, { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';

import { useNavigationApiTrigger } from '../../hooks/useNavigationApiTrigger';
import { useSubscriptionCheck } from '../../hooks/useSubscriptionCheck';
import Header from './Header';
import Sidebar from './Sidebar';

const MainLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  // Navigation-based API triggering - calls APIs when user navigates to specific pages
  useNavigationApiTrigger();

  // Check subscription expiry for organization admins
  useSubscriptionCheck();

  // Check if current route is doctor profile page
  const isDoctorProfilePage = location.pathname.includes(
    '/invoices/doctor-profile'
  );

  return (
    <>
      <div className='h-screen bg-gray-50 flex overflow-hidden'>
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        <div className='flex-1 flex flex-col min-w-0'>
          <Header onMenuToggle={() => setSidebarOpen(!sidebarOpen)} />

          <main
            className={`flex-1 overflow-y-auto ${
              isDoctorProfilePage ? 'pt-0 px-6 pb-6' : 'p-6'
            }`}
          >
            <Outlet />
          </main>
        </div>
      </div>
    </>
  );
};

export default MainLayout;
