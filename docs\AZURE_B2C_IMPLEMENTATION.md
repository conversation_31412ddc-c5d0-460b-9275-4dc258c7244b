# Azure B2C Implementation Documentation

## Table of Contents

1. [Overview](#overview)
2. [Core Configuration](#core-configuration)
3. [Authentication Flow](#authentication-flow)
4. [Key Components](#key-components)
5. [API Integration](#api-integration)
6. [Storage](#storage)
7. [Error Handling](#error-handling)
8. [Dependencies](#dependencies)
9. [Migration Notes for AWS Cognito](#migration-notes-for-aws-cognito)

## Overview

This document details the Azure B2C implementation in the ArcaAI Admin application. The implementation uses Microsoft Authentication Library (MSAL) for React to handle authentication flows with Azure B2C.

## Related Files

Here's a list of all files related to the Azure B2C implementation:

### Core B2C Configuration Files

- `src/utils/msalConfig.ts` - Core MSAL and B2C configuration
- `src/config/api.ts` - B2C tenant and policy configurations
- `src/utils/msal.ts` - MSAL instance and B2C authentication utilities

### B2C React Components

- `src/components/Auth/MsalAuthHandler.tsx` - Main B2C authentication handler
- `src/pages/LandingPage.tsx` - B2C login page implementation

## Core Configuration

### 1. MSAL Configuration (`src/utils/msalConfig.ts`)

- **B2C Policies**:
  - Sign-up/Sign-in: `B2C_1_emrapp`
  - Password Reset: `B2C_1_reset_v3`
  - Profile Edit: `B2C_1_edit_profile_v2`
- **Tenant**: `erm20240520.onmicrosoft.com`
- **Client ID**: `f22ad9c9-3fe2-4921-b825-ed8b887f3ab7`
- **Authority Domain**: `erm20240520.b2clogin.com`

### 2. API Configuration (`src/config/api.ts`)

- Defines client and tenant information
- Specifies policy names and authority URLs
- Configures API scopes and permissions

## Complete Authentication Flow

1. **User Initiates Login**

   - User navigates to the application and clicks the login button on `LandingPage.tsx`
   - The `handleLogin` function in `LandingPage.tsx` is triggered
   - MSAL's `loginRedirect` is called with the B2C policy `B2C_1_emrapp`

2. **Azure B2C Authentication**

   - User is redirected to Azure B2C's login page: `https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/B2C_1_emrapp`
   - User enters credentials or signs up if new
   - B2C validates credentials and creates an authentication session

3. **Redirect Back to Application**

   - After successful authentication, Azure B2C redirects back to the application's redirect URI with an authorization code
   - MSAL handles the redirect and exchanges the code for tokens

4. **Token Handling**

   - MSAL stores the received tokens in session storage:
     - Access Token: Used for API authorization
     - ID Token: Contains user information
   - The token's signature and expiration are validated

5. **User Session Initialization**

   - `MsalAuthHandler` component detects the successful authentication
   - The `inProgress` state changes to `none` and `accounts` array is populated
   - `useEffect` hook in `MsalAuthHandler` triggers the post-login flow

6. **User Information Retrieval**

   - The application calls `fetchUserInfo()` to get user details
   - User roles and permissions are extracted from the token
   - User session is established with the received information

7. **Redux State Update**

   - User information is stored in the Redux store via `setAuthState`
   - Organization and role information is processed
   - UI updates to reflect the authenticated state

8. **Navigation**
   - User is redirected to the originally requested URL or the default authenticated route
   - The application loads with the user's authenticated context

## Authentication Flow

1. **Initialization**

   - MSAL instance is created with configuration
   - Active session is checked on app load

2. **Login Process**

   - User initiates login via `login()` function
   - Redirects to Azure B2C login page
   - After authentication, user is redirected back to the app
   - MSAL handles token exchange and storage

3. **Token Management**

   - Access tokens stored in `localStorage` and `sessionStorage`
   - ID tokens stored in `sessionStorage`
   - Automatic token renewal before expiration

4. **Logout**
   - Clears all authentication data
   - Redirects to Azure B2C logout endpoint
   - Returns to login page

## Key Components

### 1. MsalAuthHandler (`src/components/Auth/MsalAuthHandler.tsx`)

- Wraps the application
- Manages authentication state
- Handles redirects and user info fetching

### 2. Authentication Utilities (`src/utils/authUtils.ts`)

- `login()`: Handles login flow
- `logout()`: Handles logout flow
- `getToken()`: Manages token retrieval and refresh

### 3. Auth Service (`src/store/features/auth/auth.service.ts`)

- Handles API calls for authentication
- Manages user sessions
- Integrates with Redux store

## API Integration

- All API requests include the access token in the Authorization header
- Token refresh is handled automatically by MSAL
- API endpoints are protected by Azure B2C authentication

## Storage

- **Access Token**: `localStorage` (persistent) and `sessionStorage` (session-only)
- **ID Token**: `sessionStorage`
- **Account Info**: `sessionStorage`

## Error Handling

- Handles token expiration
- Manages authentication errors
- Provides user feedback for auth-related issues

## Dependencies

- `@azure/msal-browser`: Core MSAL library
- `@azure/msal-react`: React bindings for MSAL
- `react-router-dom`: Navigation
- `redux`: State management
