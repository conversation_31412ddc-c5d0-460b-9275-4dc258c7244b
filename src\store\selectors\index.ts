import { createSelector } from '@reduxjs/toolkit';

import { PermissionAction } from '../../types';
import { RootState } from '../index';

// Auth selectors
export const selectAuth = (state: RootState) => state.auth;
export const selectUser = createSelector([selectAuth], (auth) => auth.user);
export const selectIsAuthenticated = createSelector(
  [selectAuth],
  (auth) => auth.isAuthenticated
);
export const selectSelectedOrganization = createSelector(
  [selectAuth],
  (auth) => auth.selectedOrganization
);
export const selectCurrentOrganization = createSelector(
  [selectAuth],
  (auth) => auth.currentOrganization
);
export const selectAuthLoading = createSelector(
  [selectAuth],
  (auth) => auth.loading
);
export const selectAuthError = createSelector(
  [selectAuth],
  (auth) => auth.error
);

// Users selectors
export const selectUsers = (state: RootState) => state.users;
export const selectUsersList = createSelector(
  [selectUsers],
  (users) => users.users
);
export const selectUsersLoading = createSelector(
  [selectUsers],
  (users) => users.loading
);
export const selectUsersError = createSelector(
  [selectUsers],
  (users) => users.error
);
export const selectUsersPagination = createSelector([selectUsers], (users) => ({
  page: users.page,
  limit: users.limit,
  total: users.total,
}));

// Organizations selectors
export const selectOrganizations = (state: RootState) => state.organizations;
export const selectOrganizationsList = createSelector(
  [selectOrganizations],
  (organizations) => organizations.organizations
);
export const selectOrganizationsLoading = createSelector(
  [selectOrganizations],
  (organizations) => organizations.loading
);
export const selectOrganizationsError = createSelector(
  [selectOrganizations],
  (organizations) => organizations.errorMessage
);
export const selectOrganizationsPagination = createSelector(
  [selectOrganizations],
  (organizations) => ({
    page: organizations.page,
    limit: organizations.limit,
    total: organizations.total,
  })
);

// Roles selectors
export const selectRoles = (state: RootState) => state.roles;
export const selectRolesList = createSelector(
  [selectRoles],
  (roles) => roles.roles
);
export const selectRolesLoading = createSelector(
  [selectRoles],
  (roles) => roles.loading
);
export const selectRolesError = createSelector(
  [selectRoles],
  (roles) => roles.error
);
export const selectRolesPagination = createSelector([selectRoles], (roles) => ({
  page: roles.page,
  limit: roles.limit,
  total: roles.total,
}));

// Departments selectors
export const selectDepartments = (state: RootState) => state.departments;
export const selectDepartmentsList = createSelector(
  [selectDepartments],
  (departments) => departments.departments
);
export const selectDepartmentsLoading = createSelector(
  [selectDepartments],
  (departments) => departments.loading
);

// Branches selectors
export const selectBranches = (state: RootState) => state.branches;
export const selectBranchesList = createSelector(
  [selectBranches],
  (branches) => branches.branches
);
export const selectBranchesLoading = createSelector(
  [selectBranches],
  (branches) => branches.loading
);

// Banks selectors
export const selectBanks = (state: RootState) => state.banks;
export const selectBanksList = createSelector(
  [selectBanks],
  (banks) => banks.banks
);
export const selectBanksLoading = createSelector(
  [selectBanks],
  (banks) => banks.loading
);

// Languages selectors
export const selectLanguages = (state: RootState) => state.languages;
export const selectLanguagesList = createSelector(
  [selectLanguages],
  (languages) => languages.languages
);
export const selectLanguagesLoading = createSelector(
  [selectLanguages],
  (languages) => languages.loading
);

// Templates selectors
export const selectTemplates = (state: RootState) => state.templates;
export const selectTemplatesList = createSelector(
  [selectTemplates],
  (templates) => templates.templates
);
export const selectTemplatesLoading = createSelector(
  [selectTemplates],
  (templates) => templates.loading
);

// Vitals selectors
export const selectVitals = (state: RootState) => state.vitals;
export const selectVitalsList = createSelector(
  [selectVitals],
  (vitals) => vitals.vitals
);
export const selectVitalsLoading = createSelector(
  [selectVitals],
  (vitals) => vitals.loading
);

// Master data selectors
export const selectMasterData = (state: RootState) => state.masterData;
export const selectMasterDataLoading = createSelector(
  [selectMasterData],
  (masterData) => masterData.loading
);

// Audit selectors
export const selectAudit = (state: RootState) => state.audit;
export const selectAuditLogs = createSelector(
  [selectAudit],
  (audit) => audit.logs
);
export const selectAuditLoading = createSelector(
  [selectAudit],
  (audit) => audit.loading
);

// Complex selectors for derived data
export const selectUsersByStatus = createSelector(
  [selectUsersList],
  (users) => ({
    active: users.filter((user) => user.status === 'active'),
    inactive: users.filter((user) => user.status === 'inactive'),
  })
);

export const selectOrganizationsByStatus = createSelector(
  [selectOrganizationsList],
  (organizations) => ({
    active: organizations.filter((org) => org.status === 'active'),
    inactive: organizations.filter((org) => org.status === 'inactive'),
  })
);

// Dashboard stats selectors
export const selectDashboardStats = createSelector(
  [selectUsersList, selectOrganizationsList, selectRolesList],
  (users, organizations, roles) => ({
    totalUsers: users.length,
    activeUsers: users.filter((user) => user.status === 'active').length,
    totalOrganizations: organizations.length,
    activeOrganizations: organizations.filter((org) => org.status === 'active')
      .length,
    totalRoles: roles.length,
  })
);

// Search and filter selectors
export const selectFilteredUsers = createSelector(
  [selectUsersList, (_state: RootState, searchTerm: string) => searchTerm],
  (users, searchTerm) => {
    if (!searchTerm) return users;
    const term = searchTerm.toLowerCase();
    return users.filter(
      (user) =>
        user.firstName.toLowerCase().includes(term) ||
        user.lastName.toLowerCase().includes(term) ||
        user.email.toLowerCase().includes(term)
    );
  }
);

export const selectFilteredOrganizations = createSelector(
  [
    selectOrganizationsList,
    (_state: RootState, searchTerm: string) => searchTerm,
  ],
  (organizations, searchTerm) => {
    if (!searchTerm) return organizations;
    const term = searchTerm.toLowerCase();
    return organizations.filter((org) => org.name.toLowerCase().includes(term));
  }
);

// Permission-based selectors
export const selectUserPermissions = createSelector([selectUser], (user) => {
  if (!user?.roles) return [];
  return user.roles.flatMap((role) => role.permissions);
});

export const selectHasPermission = createSelector(
  [
    selectUserPermissions,
    (_state: RootState, permission: string) => permission,
  ],
  (permissions, permission) => {
    return permissions.some((p) =>
      p.actions?.includes(permission as PermissionAction)
    );
  }
);

// Loading state aggregators
export const selectIsAnyLoading = createSelector(
  [
    selectAuthLoading,
    selectUsersLoading,
    selectOrganizationsLoading,
    selectRolesLoading,
    selectDepartmentsLoading,
    selectBranchesLoading,
    selectBanksLoading,
    selectLanguagesLoading,
    selectTemplatesLoading,
    selectVitalsLoading,
    selectMasterDataLoading,
    selectAuditLoading,
  ],
  (...loadingStates) => loadingStates.some(Boolean)
);

// Error state aggregators
export const selectAnyError = createSelector(
  [
    selectAuthError,
    selectUsersError,
    selectOrganizationsError,
    selectRolesError,
  ],
  (...errors) => errors.find(Boolean) || null
);
