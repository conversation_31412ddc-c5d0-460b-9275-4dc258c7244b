# ---- Base Node image ----
FROM public.ecr.aws/docker/library/node:18-alpine AS build
WORKDIR /app
RUN ls -lart
COPY package*.json ./
RUN npm install
COPY . .
# Build without running lint or validate
RUN npx vite build
# ---- Production Image ----
FROM public.ecr.aws/docker/library/node:18-alpine
WORKDIR /app
RUN ls -lart
COPY package*.json ./
RUN npm install --only=production
COPY --from=build /app/dist ./dist
COPY ./scripts ./scripts
RUN chmod +x ./scripts/generate-env-config.sh
EXPOSE 8080
CMD ["/bin/sh", "-c", "./scripts/generate-env-config.sh && npm run start"]
