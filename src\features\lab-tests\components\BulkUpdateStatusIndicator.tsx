import { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import { stopBulkUpdatePolling } from '../../../store/features/lab-tests/labTest.slice';
import { BulkUpdateStatus } from '../types/bulk-update.types';
import { BulkUpdateStatus as BulkUpdateStatusComponent } from './BulkUpdateStatus';

interface BulkUpdateStatusIndicatorProps {
  show?: boolean;
}

export const BulkUpdateStatusIndicator: React.FC<
  BulkUpdateStatusIndicatorProps
> = ({ show = true }) => {
  // Move all hooks to the top, before any conditional logic
  const { status: reduxStatus, isPolling: isReduxPolling } = useSelector(
    (state: RootState) => state.labTests.bulkUpdate
  );

  const dispatch = useDispatch<AppDispatch>();

  const [localStatus, setLocalStatus] = useState<BulkUpdateStatus | null>(
    reduxStatus
  );
  const [isLocalPolling, setIsLocalPolling] = useState(!!reduxStatus);

  const pollingRef = useRef<{
    timeout: NodeJS.Timeout | null;
    isMounted: boolean;
    shouldContinue: boolean;
  }>({ timeout: null, isMounted: true, shouldContinue: true });

  // Restore from localStorage on component mount
  useEffect(() => {
    const savedStatus = localStorage.getItem('labTestsBulkStatus');
    if (savedStatus) {
      try {
        const parsed = JSON.parse(savedStatus);
        if (parsed.status && parsed.persistentPolling) {
          // Only restore if status is still processing
          if (
            parsed.status.status === 'PROCESSING' ||
            parsed.status.status === 'PENDING'
          ) {
            // Restore the bulk update status and selection state
            dispatch({
              type: 'labTests/restoreBulkUpdateStatus',
              payload: parsed,
            });

            // Force local state to show the component
            setLocalStatus(parsed.status);
            setIsLocalPolling(true);

            // Restart global polling service if it's not already running
            import('../../../services/bulkUpdatePollingService').then(
              ({ bulkUpdatePollingService }) => {
                if (
                  !bulkUpdatePollingService.isCurrentlyPolling() &&
                  parsed.status.statusUrl
                ) {
                  bulkUpdatePollingService.startLabTestsPolling(
                    parsed.status.statusUrl
                  );
                }
              }
            );
          } else {
            // Status is completed or failed, clean up
            localStorage.removeItem('labTestsBulkStatus');
          }
        }
      } catch (error) {
        console.error('Failed to restore bulk update status:', error);
        localStorage.removeItem('labTestsBulkStatus');
      }
    }
  }, [dispatch]);

  // Update local status and polling state when redux status changes
  useEffect(() => {
    if (reduxStatus) {
      setLocalStatus(reduxStatus);
      setIsLocalPolling(true);
    }
  }, [reduxStatus]);

  // Sync local polling state with redux
  useEffect(() => {
    if (isReduxPolling !== isLocalPolling) {
      setIsLocalPolling(isReduxPolling);
    }
  }, [isReduxPolling]);

  // Note: Polling is now handled by the global bulkUpdatePollingService
  // This component only displays the status and handles UI interactions

  // Handle close button click
  const handleClose = useCallback(() => {
    if (!localStatus?.id) return;

    // Mark the operation as manually closed in localStorage
    localStorage.setItem(`closedBulkOperation_${localStatus.id}`, 'true');

    // Stop any pending timeouts
    const { current } = pollingRef;
    if (current.timeout) {
      clearTimeout(current.timeout);
      current.timeout = null;
    }
    current.shouldContinue = false;

    // Update local state to close the component
    setIsLocalPolling(false);
    setLocalStatus(null);

    // Stop Redux polling
    dispatch(stopBulkUpdatePolling());

    // Stop the global polling service
    import('../../../services/bulkUpdatePollingService').then(
      ({ bulkUpdatePollingService }) => {
        bulkUpdatePollingService.stopPolling();
      }
    );
  }, [dispatch, localStatus?.id]);

  // Check if this bulk operation was manually closed
  const isManuallyClosed = useCallback((bulkId: string) => {
    return localStorage.getItem(`closedBulkOperation_${bulkId}`) === 'true';
  }, []);

  // Show component if:
  // 1. There is a status to show
  // 2. And we're either:
  //    a. Explicitly told to show it, OR
  //    b. It's polling, there's an active status, and it wasn't manually closed
  const shouldShow =
    localStatus &&
    localStatus.id &&
    !isManuallyClosed(localStatus.id) &&
    (show ||
      (isLocalPolling &&
        (localStatus.status === 'PROCESSING' ||
          localStatus.status === 'PENDING')));

  // Don't render anything if there's no status or if not polling
  if (!shouldShow || !localStatus || !isLocalPolling) {
    return null;
  }

  return (
    <div className='fixed bottom-4 right-4 z-50'>
      <BulkUpdateStatusComponent
        status={localStatus}
        isPolling={isLocalPolling}
        onClose={handleClose}
      />
    </div>
  );
};
