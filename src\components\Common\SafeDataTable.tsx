import { useMemo } from 'react';

import BaseDataTable, { DataTableProps } from './DataTable';

interface SafeDataTableProps<T>
  extends Omit<DataTableProps<{ id: string } & T>, 'data'> {
  data: T[];
  getId: (item: T) => string;
}

export function SafeDataTable<T>({
  data,
  getId,
  ...props
}: SafeDataTableProps<T>) {
  const safeData = useMemo(
    () =>
      data.map((item) => ({
        ...item,
        id: getId(item) || '',
      })),
    [data, getId]
  );

  return <BaseDataTable {...props} data={safeData} />;
}

export default SafeDataTable;
