import {
  Activity,
  Building2,
  FileText,
  Shield,
  TrendingUp,
  Users,
} from 'lucide-react';
import React from 'react';
import { useSelector } from 'react-redux';

import { useAuth } from '../../hooks/useAuth';
import { RootState } from '../../store';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  trend,
  color,
}) => (
  <div className='bg-white rounded-lg shadow-sm border border-gray-200 p-6'>
    <div className='flex items-center justify-between'>
      <div>
        <p className='text-sm font-medium text-gray-600'>{title}</p>
        <p className='text-2xl font-bold text-gray-900 mt-1'>{value}</p>
        {trend && (
          <div
            className={`flex items-center mt-2 text-sm ${
              trend.isPositive ? 'text-green-600' : 'text-red-600'
            }`}
          >
            <TrendingUp
              className={`w-4 h-4 mr-1 ${!trend.isPositive ? 'rotate-180' : ''}`}
            />
            <span>{trend.value}% from last month</span>
          </div>
        )}
      </div>
      <div className={`p-3 rounded-full ${color}`}>{icon}</div>
    </div>
  </div>
);

const Dashboard: React.FC = () => {
  const { isSuperAdmin, selectedOrganization, getDisplayName } = useAuth();
  const { users } = useSelector((state: RootState) => state.users);
  const { organizations } = useSelector(
    (state: RootState) => state.organizations
  );

  // Note: Removed auto-fetch on dashboard mount to prevent unwanted API calls on login
  // Dashboard should show summary data, not trigger API calls immediately
  // useEffect(() => {
  //   const orgId = isSuperAdmin ? 'system' : selectedOrganization?.id;
  //   if (orgId) {
  //     dispatch(fetchUsers({ organizationId: orgId }));
  //   }

  //   // Only fetch organizations if we're super admin and don't have them already
  //   if (isSuperAdmin && organizations.length === 0) {
  //     dispatch(fetchOrganizations({}));
  //   }
  // }, [dispatch, isSuperAdmin, selectedOrganization, organizations.length]);

  const superAdminStats = [
    {
      title: 'Total Organizations',
      value: organizations.length,
      icon: <Building2 className='w-6 h-6 text-white' />,
      color: 'bg-blue-500',
      trend: { value: 12, isPositive: true },
    },
    {
      title: 'Active Organizations',
      value: organizations.filter((org) => org.status === 'active').length,
      icon: <Activity className='w-6 h-6 text-white' />,
      color: 'bg-green-500',
      trend: { value: 8, isPositive: true },
    },
    {
      title: 'Total System Users',
      value: '1,247',
      icon: <Users className='w-6 h-6 text-white' />,
      color: 'bg-purple-500',
      trend: { value: 15, isPositive: true },
    },
  ];

  const orgAdminStats = [
    {
      title: 'Total Users',
      value: users.length,
      icon: <Users className='w-6 h-6 text-white' />,
      color: 'bg-blue-500',
      trend: { value: 8, isPositive: true },
    },
    {
      title: 'Active Users',
      value: users.filter((user) => user.status === 'active').length,
      icon: <Activity className='w-6 h-6 text-white' />,
      color: 'bg-green-500',
    },
    {
      title: 'Roles Configured',
      value: '4',
      icon: <Shield className='w-6 h-6 text-white' />,
      color: 'bg-purple-500',
    },
    {
      title: 'Patients Registered',
      value: '1,856',
      icon: <FileText className='w-6 h-6 text-white' />,
      color: 'bg-indigo-500',
      trend: { value: 23, isPositive: true },
    },
  ];

  const stats = isSuperAdmin ? superAdminStats : orgAdminStats;

  // TODO: Implement real-time activity tracking in future
  // const recentActivities = [
  //   {
  //     id: '1',
  //     action: 'New user created',
  //     user: 'Dr. Sarah Wilson',
  //     timestamp: '2 minutes ago',
  //     type: 'user',
  //   },
  //   {
  //     id: '2',
  //     action: 'Organization updated',
  //     user: 'Metro General Hospital',
  //     timestamp: '15 minutes ago',
  //     type: 'organization',
  //   },
  //   {
  //     id: '3',
  //     action: 'Role permissions modified',
  //     user: 'DOCTOR role',
  //     timestamp: '1 hour ago',
  //     type: 'role',
  //   },
  //   {
  //     id: '4',
  //     action: 'Patient record accessed',
  //     user: 'Dr. John Smith',
  //     timestamp: '2 hours ago',
  //     type: 'patient',
  //   },
  // ];

  return (
    <div className='space-y-6'>
      {/* Welcome Section */}
      <div className='bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg shadow-sm p-6 text-white'>
        <div className='flex items-center justify-between'>
          <div>
            <h1 className='text-2xl font-bold'>
              {isSuperAdmin
                ? 'Welcome back Super Admin!'
                : `Welcome back, ${getDisplayName()}!`}
            </h1>
            <p className='text-blue-100 mt-1'>
              {isSuperAdmin
                ? 'Manage organizations and oversee the entire EMR system'
                : `Managing ${selectedOrganization?.name || 'your organization'}`}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div
        className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${
          isSuperAdmin ? 'lg:grid-cols-3' : 'lg:grid-cols-4'
        }`}
      >
        {stats.map((stat, index) => (
          <StatsCard key={index} {...stat} />
        ))}
      </div>

      {/* Recent Activity - TODO: Implement real-time activity tracking in future */}
      {/* Hide Recent Activity for both Super Admin and Organization Admin temporarily */}
      <div className='grid grid-cols-1 gap-6'>
        {/* Quick Actions */}
        <div className='bg-white rounded-lg shadow-sm border border-gray-200'>
          <div className='p-6 border-b border-gray-200'>
            <h3 className='text-lg font-semibold text-gray-900'>
              Quick Actions
            </h3>
          </div>
          <div className='p-6'>
            <div className='space-y-3'>
              {isSuperAdmin ? (
                <>
                  <button className='w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors'>
                    <div className='flex items-center space-x-3'>
                      <Building2 className='w-5 h-5 text-blue-600' />
                      <span className='font-medium'>
                        Create New Organization
                      </span>
                    </div>
                  </button>
                </>
              ) : (
                <>
                  <button className='w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors'>
                    <div className='flex items-center space-x-3'>
                      <Users className='w-5 h-5 text-blue-600' />
                      <span className='font-medium'>Add New User</span>
                    </div>
                  </button>
                  <button className='w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors'>
                    <div className='flex items-center space-x-3'>
                      <Shield className='w-5 h-5 text-purple-600' />
                      <span className='font-medium'>Manage Roles</span>
                    </div>
                  </button>
                  {/* TODO: Implement View Reports functionality in future */}
                  {/* <button className='w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors'>
                    <div className='flex items-center space-x-3'>
                      <FileText className='w-5 h-5 text-indigo-600' />
                      <span className='font-medium'>View Reports</span>
                    </div>
                  </button> */}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
