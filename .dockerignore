# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*


# Build output
dist
build
.vite

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode
.idea
*.swp
*.swo
*~

# Git
.git
.gitignore
.github

# Testing
coverage
.nyc_output

# Misc
.DS_Store
*.log
.husky

# Documentation
README.md
docs

# Load tests
load-tests

# Grafana
grafana

# TypeScript
*.tsbuildinfo


# Vercel and Azure specific files (not needed in AWS)
vercel.json
staticwebapp.config.json
