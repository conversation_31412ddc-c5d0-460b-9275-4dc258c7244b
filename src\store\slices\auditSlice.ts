import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import { generateMockApiResponse, mockAuditLogs } from '../../data/mockData';
import { AuditLog, PaginatedResponse } from '../../types';

interface AuditState {
  logs: AuditLog[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: AuditState = {
  logs: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 20,
};

export const fetchAuditLogs = createAsyncThunk(
  'audit/fetchAuditLogs',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    dateFrom?: Date;
    dateTo?: Date;
    actionType?: string;
  }) => {
    // Filter logs by organization
    let filteredLogs = mockAuditLogs.filter(
      (log) => log.organizationId === params.organizationId
    );

    // Apply filters
    if (params.search) {
      filteredLogs = filteredLogs.filter(
        (log) =>
          log.userName.toLowerCase().includes(params.search!.toLowerCase()) ||
          log.actionType.toLowerCase().includes(params.search!.toLowerCase()) ||
          log.details.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    if (params.actionType) {
      filteredLogs = filteredLogs.filter(
        (log) => log.actionType === params.actionType
      );
    }

    if (params.dateFrom) {
      filteredLogs = filteredLogs.filter(
        (log) => log.timestamp >= params.dateFrom!
      );
    }

    if (params.dateTo) {
      filteredLogs = filteredLogs.filter(
        (log) => log.timestamp <= params.dateTo!
      );
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    const page = params.page || 1;
    const limit = params.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredLogs.slice(startIndex, endIndex);

    const response: PaginatedResponse<AuditLog> = {
      data: paginatedData,
      total: filteredLogs.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 700);
  }
);

const auditSlice = createSlice({
  name: 'audit',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAuditLogs.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAuditLogs.fulfilled, (state, action) => {
        state.loading = false;
        state.logs = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchAuditLogs.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch audit logs';
      });
  },
});

export const { clearError } = auditSlice.actions;
export default auditSlice.reducer;
