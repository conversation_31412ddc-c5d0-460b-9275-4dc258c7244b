/**
 * Utility functions for authentication and organization management
 */
import { isOrgAdminRole, isSuperAdminRole } from '../constants/roles';

/**
 * Get the current organization ID from localStorage
 * This is useful for API calls that require organizationId
 */
export const getCurrentOrganizationId = (): string | null => {
  try {
    return localStorage.getItem('organizationId');
  } catch (error) {
    console.error('Failed to get organizationId from localStorage:', error);
    return null;
  }
};

/**
 * Get the current user from localStorage
 */
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('Failed to get user from localStorage:', error);
    return null;
  }
};

/**
 * Get the selected organization from localStorage
 */
export const getSelectedOrganization = () => {
  try {
    const orgStr = localStorage.getItem('selectedOrganization');
    return orgStr ? JSON.parse(orgStr) : null;
  } catch (error) {
    console.error(
      'Failed to get selectedOrganization from localStorage:',
      error
    );
    return null;
  }
};

/**
 * Check if the current user is a super admin
 */
export const isSuperAdmin = (): boolean => {
  const user = getCurrentUser();
  if (!user || !user.roles) return false;

  return user.roles.some((role: any) => isSuperAdminRole(role.name));
};

/**
 * Check if the current user is an organization admin or organization super admin
 */
export const isOrganizationAdmin = (): boolean => {
  const user = getCurrentUser();
  if (!user || !user.roles) return false;

  return user.roles.some((role: any) => isOrgAdminRole(role.name));
};
