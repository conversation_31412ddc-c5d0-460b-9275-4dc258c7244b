import { Save, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import LoadingSpinner from '../../components/Common/LoadingSpinner';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch } from '../../store';
import { createBank, updateBank } from '../../store/slices/bankSlice';
import { Bank } from '../../types';
import { getOrganizationId } from '../../utils/organization-utils';

interface BankFormProps {
  bank?: Bank | null;
  onSuccess: () => void;
}

const BankForm: React.FC<BankFormProps> = ({ bank, onSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    bankName: '',
    accountHolderName: '',
    accountNumber: '',
    ifscCode: '',
    branchName: '',
    status: 'active' as 'active' | 'inactive',
  });

  useEffect(() => {
    if (bank) {
      setFormData({
        bankName: bank.bankName,
        accountHolderName: bank.accountHolderName,
        accountNumber: bank.accountNumber,
        ifscCode: bank.ifscCode,
        branchName: bank.branchName,
        status: bank.status,
      });
    }
  }, [bank]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    if (!formData.bankName.trim()) newErrors.bankName = 'Bank name is required';
    if (!formData.accountHolderName.trim())
      newErrors.accountHolderName = 'Account holder name is required';
    if (!formData.accountNumber.trim())
      newErrors.accountNumber = 'Account number is required';
    if (!formData.ifscCode.trim()) newErrors.ifscCode = 'IFSC code is required';
    if (!formData.branchName.trim())
      newErrors.branchName = 'Branch name is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm() || !selectedOrganization) return;

    setLoading(true);
    try {
      const orgId = getOrganizationId(selectedOrganization);
      if (!orgId) {
        throw new Error('No organization selected');
      }

      const bankData = { ...formData, organizationId: orgId };
      if (bank) {
        await dispatch(updateBank({ id: bank.id, data: bankData }));
      } else {
        await dispatch(createBank(bankData));
      }
      onSuccess();
    } catch {
      // Handle error silently or show user feedback
    } finally {
      setLoading(false);
    }
  };

  const handleChange =
    (field: keyof typeof formData) =>
    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
      setFormData((prev) => ({ ...prev, [field]: e.target.value }));
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: '' }));
      }
    };

  return (
    <form onSubmit={handleSubmit} className='p-6 space-y-6'>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
        {/* Form fields */}
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Bank Name *
          </label>
          <input
            type='text'
            value={formData.bankName}
            onChange={handleChange('bankName')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.bankName ? 'border-red-300' : 'border-gray-300'}`}
            placeholder='Enter bank name'
          />
          {errors.bankName && (
            <p className='mt-1 text-sm text-red-600'>{errors.bankName}</p>
          )}
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Account Holder Name *
          </label>
          <input
            type='text'
            value={formData.accountHolderName}
            onChange={handleChange('accountHolderName')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.accountHolderName ? 'border-red-300' : 'border-gray-300'}`}
            placeholder='Enter account holder name'
          />
          {errors.accountHolderName && (
            <p className='mt-1 text-sm text-red-600'>
              {errors.accountHolderName}
            </p>
          )}
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Account Number *
          </label>
          <input
            type='text'
            value={formData.accountNumber}
            onChange={handleChange('accountNumber')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.accountNumber ? 'border-red-300' : 'border-gray-300'}`}
            placeholder='Enter account number'
          />
          {errors.accountNumber && (
            <p className='mt-1 text-sm text-red-600'>{errors.accountNumber}</p>
          )}
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            IFSC Code *
          </label>
          <input
            type='text'
            value={formData.ifscCode}
            onChange={handleChange('ifscCode')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.ifscCode ? 'border-red-300' : 'border-gray-300'}`}
            placeholder='Enter IFSC code'
          />
          {errors.ifscCode && (
            <p className='mt-1 text-sm text-red-600'>{errors.ifscCode}</p>
          )}
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Branch Name *
          </label>
          <input
            type='text'
            value={formData.branchName}
            onChange={handleChange('branchName')}
            className={`mt-1 block w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.branchName ? 'border-red-300' : 'border-gray-300'}`}
            placeholder='Enter branch name'
          />
          {errors.branchName && (
            <p className='mt-1 text-sm text-red-600'>{errors.branchName}</p>
          )}
        </div>
        <div>
          <label className='block text-sm font-medium text-gray-700'>
            Status
          </label>
          <select
            value={formData.status}
            onChange={handleChange('status')}
            className='mt-1 block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500'
          >
            <option value='active'>Active</option>
            <option value='inactive'>Inactive</option>
          </select>
        </div>
      </div>

      <div className='flex justify-end pt-4 space-x-4'>
        <button
          type='button'
          onClick={onSuccess}
          className='inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
        >
          <X className='w-4 h-4 mr-2' />
          Cancel
        </button>
        <button
          type='submit'
          disabled={loading}
          className='inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50'
        >
          {loading ? <LoadingSpinner /> : <Save className='w-4 h-4 mr-2' />}
          {bank ? 'Save Changes' : 'Create Bank'}
        </button>
      </div>
    </form>
  );
};

export default BankForm;
