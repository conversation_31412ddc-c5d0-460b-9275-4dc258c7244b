import {
  Alert,
  Box,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import {
  createTemplate,
  updateTemplate,
} from '../../store/slices/templateSlice';
import { Template } from '../../types';
import { getOrganizationId } from '../../utils/organization-utils';

interface TemplateFormProps {
  template: Template | null;
  onFinished: () => void;
}

const TemplateForm: React.FC<TemplateFormProps> = ({
  template,
  onFinished,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();
  const { loading, error } = useSelector((state: RootState) => state.templates);

  const [name, setName] = useState('');
  const [type, setType] = useState('');
  const [content, setContent] = useState('');
  const [status, setStatus] = useState<'active' | 'inactive'>('active');
  const [formError, setFormError] = useState('');

  useEffect(() => {
    if (template) {
      setName(template.name);
      setType(template.type);
      setContent(template.content);
      setStatus(template.status);
    }
  }, [template]);

  const validateForm = () => {
    if (!name.trim()) return 'Name is required.';
    if (!type.trim()) return 'Type is required.';
    if (!content.trim()) return 'Content is required.';
    return '';
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const validationError = validateForm();
    if (validationError) {
      setFormError(validationError);
      return;
    }

    const orgId = getOrganizationId(selectedOrganization);
    if (!orgId) {
      setFormError('No organization selected.');
      return;
    }

    const templateData: Omit<Template, 'id' | 'createdAt'> = {
      name,
      type,
      content,
      status,
      organizationId: orgId, // This is now guaranteed to be a string
    };

    let resultAction;
    if (template) {
      resultAction = await dispatch(
        updateTemplate({ id: template.id, data: templateData })
      );
    } else {
      resultAction = await dispatch(createTemplate(templateData));
    }

    if (
      updateTemplate.fulfilled.match(resultAction) ||
      createTemplate.fulfilled.match(resultAction)
    ) {
      onFinished();
    }
  };

  return (
    <Box component='form' onSubmit={handleSubmit} sx={{ mt: 1 }}>
      <TextField
        margin='normal'
        required
        fullWidth
        id='name'
        label='Template Name'
        name='name'
        autoComplete='name'
        autoFocus
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
      <TextField
        margin='normal'
        required
        fullWidth
        id='type'
        label='Template Type'
        name='type'
        value={type}
        onChange={(e) => setType(e.target.value)}
      />
      <TextField
        margin='normal'
        required
        fullWidth
        multiline
        rows={8}
        id='content'
        label='Content (HTML with placeholders like {{patientName}})'
        name='content'
        value={content}
        onChange={(e) => setContent(e.target.value)}
      />
      <FormControl fullWidth margin='normal'>
        <InputLabel id='status-select-label'>Status</InputLabel>
        <Select
          labelId='status-select-label'
          id='status'
          value={status}
          label='Status'
          onChange={(e) => setStatus(e.target.value as 'active' | 'inactive')}
        >
          <MenuItem value='active'>Active</MenuItem>
          <MenuItem value='inactive'>Inactive</MenuItem>
        </Select>
      </FormControl>

      {formError && (
        <Alert severity='error' sx={{ mt: 2 }}>
          {formError}
        </Alert>
      )}
      {error && (
        <Alert severity='error' sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      <Box sx={{ mt: 3, position: 'relative' }}>
        <Button type='submit' fullWidth variant='contained' disabled={loading}>
          {template ? 'Save Changes' : 'Create Template'}
        </Button>
        {loading && (
          <CircularProgress
            size={24}
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              marginTop: '-12px',
              marginLeft: '-12px',
            }}
          />
        )}
      </Box>
    </Box>
  );
};

export default TemplateForm;
