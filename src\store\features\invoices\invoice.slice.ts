import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { ApiError, InitialState } from '../../../utils/reducer-utils';
import invoiceService, {
  FetchInvoiceByIdParams,
  InvoiceDetailResponse,
  InvoiceItem,
  InvoiceListResponse,
  InvoiceParams,
} from './invoice.service';

export type NormalizedInvoice = {
  id: string;
  invoiceDate: string | Date | null;
  billType: string;
  patientId: string;
  patientName: string;
  gender?: string;
  amount?: number;
  currency?: string;
  modeOfPayment?: string;
  transactionId?: string;
  doctorName?: string;
};

type InvoiceState = InitialState<NormalizedInvoice> & {
  invoices: NormalizedInvoice[];
  detail: InvoiceDetailResponse | null;
  total: number;
  totalPages: number;
  page: number;
  limit: number;
  searchText: string;
  billTypeFilter: string;
  genderFilter: string;
  fromDate: string;
  toDate: string;
  error: string | null;
};

const initialState: InvoiceState = {
  loading: true,
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  errorMessage: null,
  successMessage: null,
  allEntities: [],
  invoices: [],
  detail: null,
  total: 0,
  totalPages: 0,
  page: 1,
  limit: 20, // Default limit matches API default
  searchText: '',
  billTypeFilter: 'all',
  genderFilter: 'all',
  fromDate: '',
  toDate: '',
  error: null,
};

const normalizeInvoice = (item: InvoiceItem): NormalizedInvoice => {
  // Map API response structure to Invoice type
  // API returns: { date, type, patientId, fullName, gender, amount, modeOfPayment, transactionId, doctorName, paymentId, status, rawPayment }

  const invoiceId = item.paymentId || `invoice-${item.date || Date.now()}`;

  // Parse date - API returns date as "DD/MM/YYYY" format
  let invoiceDate: string | Date | null = null;
  if (item.date) {
    try {
      // Try to parse DD/MM/YYYY format (e.g., "17/11/2025")
      const dateParts = (item.date || '').split('/');
      if (dateParts.length === 3) {
        const day = parseInt(dateParts[0] || '0', 10);
        const month = parseInt(dateParts[1] || '0', 10) - 1; // Month is 0-indexed
        const year = parseInt(dateParts[2] || '0', 10);
        invoiceDate = new Date(year, month, day);
        // Validate the date
        if (Number.isNaN(invoiceDate.getTime())) {
          invoiceDate = item.date; // Fallback to string if invalid
        }
      } else {
        // Try ISO format or other formats
        invoiceDate = new Date(item.date);
        if (Number.isNaN(invoiceDate.getTime())) {
          invoiceDate = item.date;
        }
      }
    } catch {
      invoiceDate = item.date;
    }
  }

  const normalizedAmount =
    item.amount !== undefined && item.amount !== null
      ? typeof item.amount === 'number'
        ? item.amount
        : parseFloat(String(item.amount)) || undefined
      : undefined;

  const result: NormalizedInvoice = {
    id: String(invoiceId),
    invoiceDate: invoiceDate || null,
    billType: item.type || '',
    patientId: item.patientId || '',
    patientName: item.fullName && item.fullName !== 'N/A' ? item.fullName : '-',
    gender: item.gender && item.gender !== 'N/A' ? item.gender : '-',
    currency: '₹', // Default to INR currency symbol
    modeOfPayment: item.modeOfPayment || '-',
    transactionId:
      item.transactionId && item.transactionId !== '-'
        ? item.transactionId
        : '-',
    doctorName:
      item.doctorName && item.doctorName !== 'N/A' ? item.doctorName : '-',
  };

  if (normalizedAmount !== undefined) {
    result.amount = normalizedAmount;
  }

  return result;
};

const normalizeInvoiceDetail = (
  response: InvoiceDetailResponse
): NormalizedInvoice => {
  // Map the detailed invoice response to NormalizedInvoice
  const invoiceId =
    response.id || `invoice-${response.createdAt || Date.now()}`;

  // Parse date from createdAt or created_on
  let invoiceDate: string | Date | null = null;
  const dateString = response.createdAt || response.created_on;
  if (dateString) {
    try {
      invoiceDate = new Date(dateString);
      if (Number.isNaN(invoiceDate.getTime())) {
        invoiceDate = null;
      }
    } catch {
      invoiceDate = null;
    }
  }

  // Get amount - prefer amountInRupees if available, otherwise convert from amount (in paise)
  const amount =
    response.amountInRupees !== undefined
      ? response.amountInRupees
      : response.amount !== undefined
        ? response.amount / 100 // Convert from paise to rupees
        : undefined;

  // Get patient info from nested patient object or from root level
  const patientName = response.fullName || response.patient?.name || '';
  const patientId = response.patientId || response.patient?.id || '';
  const gender = response.patient?.sex || '';

  // Get payment type/mode - infer from razorpay fields
  const modeOfPayment =
    response.razorpayPaymentId || response.razorpayOrderId ? 'Razorpay' : '-';
  const transactionId =
    response.razorpayPaymentId || response.razorpayOrderId || '';

  const result: NormalizedInvoice = {
    id: String(invoiceId),
    invoiceDate: invoiceDate || null,
    billType: response.paymentType || '',
    patientId,
    patientName: patientName || '-',
    gender: gender || '-',
    currency: response.currency === 'INR' ? '₹' : response.currency || '₹',
    modeOfPayment: modeOfPayment || '-',
    transactionId: transactionId || '-',
    doctorName: '-', // Not available in detail response
  };

  if (amount !== undefined) {
    result.amount = amount;
  }

  return result;
};

const extractItems = (payload: InvoiceListResponse) => {
  if (!payload) return [];
  // API returns invoices in the 'invoices' array
  if (Array.isArray(payload.invoices)) return payload.invoices;
  // Fallback for other possible response structures
  if (Array.isArray((payload as unknown as { items?: unknown[] }).items))
    return (payload as unknown as { items: unknown[] }).items;
  if (Array.isArray((payload as unknown as { records?: unknown[] }).records))
    return (payload as unknown as { records: unknown[] }).records;
  if (Array.isArray((payload as unknown as { data?: unknown[] }).data))
    return (payload as unknown as { data: unknown[] }).data;
  return [];
};

export const fetchInvoices = createAsyncThunk(
  'invoices/fetchInvoices',
  async (params: InvoiceParams, { rejectWithValue }) => {
    try {
      return await invoiceService.fetchInvoices(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchInvoiceById = createAsyncThunk(
  'invoices/fetchInvoiceById',
  async (params: FetchInvoiceByIdParams, { rejectWithValue }) => {
    try {
      return await invoiceService.fetchInvoiceById(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const invoiceSlice = createSlice({
  name: 'invoices',
  initialState,
  reducers: {
    setSearchText: (state, action) => {
      state.searchText = action.payload;
      state.page = 1;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
    setBillTypeFilter: (state, action) => {
      state.billTypeFilter = action.payload || 'all';
      state.page = 1;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
    setGenderFilter: (state, action) => {
      state.genderFilter = action.payload || 'all';
      state.page = 1;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
    setDateRange: (state, action) => {
      state.fromDate = action.payload.fromDate;
      state.toDate = action.payload.toDate;
      state.page = 1;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
    setPage: (state, action) => {
      state.page = action.payload;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
    clearFilters: (state) => {
      state.searchText = '';
      state.billTypeFilter = 'all';
      state.genderFilter = 'all';
      state.fromDate = '';
      state.toDate = '';
      state.page = 1;
      state.loading = true;
      state.invoices = [];
      state.total = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchInvoices.fulfilled, (state, action) => {
        const payload = action.payload;
        const rawItems = extractItems(payload);
        state.invoices = rawItems.map((item) =>
          normalizeInvoice(item as InvoiceItem)
        );

        state.total = payload.total ?? rawItems.length;
        state.totalPages =
          payload.totalPages ??
          Math.ceil((payload.total ?? 0) / (payload.limit ?? state.limit));
        state.page = payload.page ?? state.page;

        if (payload.limit && payload.limit !== state.limit) {
          state.limit = payload.limit;
        }

        state.loading = false;
        state.error = null;
        state.errorMessage = null;
      })
      .addCase(fetchInvoiceById.fulfilled, (state, action) => {
        const invoiceDetail = action.payload as InvoiceDetailResponse;
        state.entity = normalizeInvoiceDetail(invoiceDetail);
        state.detail = invoiceDetail;
        state.loading = false;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isPending(fetchInvoices), (state) => {
        state.loading = true;
        state.error = null;
        state.errorMessage = null;
        // Clear invoices when starting a new fetch to prevent showing old data
        state.invoices = [];
        state.total = 0;
      })
      .addMatcher(isPending(fetchInvoiceById), (state) => {
        state.loading = true;
        state.detail = null;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isRejected(fetchInvoices), (state, action) => {
        const error = action.payload as ApiError;
        state.loading = false;
        state.error = error?.message || 'Failed to fetch invoices';
        state.errorMessage = error?.message || 'Failed to fetch invoices';
      })
      .addMatcher(isRejected(fetchInvoiceById), (state, action) => {
        const error = action.payload as ApiError;
        state.loading = false;
        state.error = error?.message || 'Failed to fetch invoice';
        state.errorMessage = error?.message || 'Failed to fetch invoice';
      });
  },
});

export const {
  setSearchText,
  setBillTypeFilter,
  setGenderFilter,
  setDateRange,
  setPage,
  clearFilters,
} = invoiceSlice.actions;

export default invoiceSlice.reducer;
