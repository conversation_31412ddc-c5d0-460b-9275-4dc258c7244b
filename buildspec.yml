version: 0.2
phases:
  pre_build:
      commands:
        # Retrieve Secret manager's secret value
        - echo "Retrieving the secrets value from Secrets Manager..."
        - BUILD_IMAGE_NAME=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."BUILD_IMAGE_NAME")
        - REPO_URL=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."REPO_URL")
        - REPO_LOGIN=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."REPO_LOGIN")
        - DEPLOYMENT_NAME=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."DEPLOYMENT_NAME")
        - REGION=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."REGION")
        - NAMESPACE=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."NAMESPACE")
        - CONTAINER_NAME=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."CONTAINER_NAME")
        - CLUSTER_NAME=$(aws secretsmanager get-secret-value --secret-id  ${SECRETS} | jq --raw-output .SecretString | jq -r ."CLUSTER_NAME")

  build:
      commands:
        # Build Docker Image
        - echo "Building the Docker image..."
        - docker build -t ${BUILD_IMAGE_NAME}:latest .

  post_build:
      commands:
        # Update the kubeconfig on the code build.
        - echo "Updating Kube Config..."
        - aws eks update-kubeconfig --name ${CLUSTER_NAME} --region ${REGION}
        - kubectl get po -n ${NAMESPACE}
        - TAG_ID=$(kubectl get deployment ${DEPLOYMENT_NAME} -n ${NAMESPACE} -o=jsonpath='{.spec.template.spec.containers[0].image}' | awk -F':' '{printf "%.1f", $2 + 0.1}')

        # Push Docker Image to ECR Repository
        - echo "Pushing the Docker image to ECR Repository..."
        - aws ecr get-login-password --region ${REGION} | docker login --username AWS --password-stdin ${REPO_LOGIN}
        - docker tag ${BUILD_IMAGE_NAME}:latest ${REPO_URL}:${TAG_ID}
        - docker push ${REPO_URL}:${TAG_ID}

        # Apply changes to our Application using kubectl
        - echo "Apply changes to kube manifests"
        - kubectl set image deployment/${DEPLOYMENT_NAME} ${CONTAINER_NAME}=${REPO_URL}:${TAG_ID} -n ${NAMESPACE}
        - kubectl rollout restart deployment/${DEPLOYMENT_NAME} -n ${NAMESPACE}
        - echo "Completed applying changes to Kubernetes Objects"
