import {
  Box,
  Button,
  Checkbox,
  Collapse,
  FormControlLabel,
  FormGroup,
  Typography,
} from '@mui/material';
import { ShieldCheck } from 'lucide-react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import LoadingSpinner from '../../../components/Common/LoadingSpinner';
import { permissionKeys } from '../../../constants/permission-keys';
import { useAuth } from '../../../hooks/useAuth';
import organizationService from '../../../store/features/organizations/organization.service';
import { ModuleType } from '../../../types';
import { usePermissions } from '../hooks/usePermissions';

interface RolePermissionManagerProps {
  roleId: string;
  roleName?: string;
  onClose: () => void;
}

interface ApiFeature {
  id: string;
  featureName: string;
  description: string;
  type: string;
  subType?: string;
  permissionKeys: string[];
  isActive: boolean;
}

interface Feature {
  name: string;
  label: string;
  keys: string[];
  subFeatures?: Feature[];
  departmentPackageKeys?: string[];
  queeKeys?: string[];
  id: string;
  isEnabled: boolean;
}

// Dynamic allModules will be built from API

// Helper function to check if a module checkbox should be checked (only when the specific access key is present)
const isModuleChecked = (
  moduleName: string,
  selectedKeys: string[]
): boolean => {
  // Handle billing module's unique access key
  if (moduleName === 'Billing') {
    return selectedKeys.includes(permissionKeys.billing.access);
  }

  const moduleKey = moduleName.toLowerCase();
  // Only return true if the specific module access key exists (e.g., 'emr.access')
  return selectedKeys.includes(`${moduleKey}.access`);
};

const RolePermissionManager: React.FC<RolePermissionManagerProps> = memo(
  ({ roleId, onClose }) => {
    const {
      user,
      selectedOrganization,
      isSuperAdmin,
      isOrganizationAdmin,
      userOrganizationId,
    } = useAuth();

    const {
      rolePermissions,
      allFeatures,
      orgFeatures,
      combinedLoading,
      assignLoading,
      assignSuccess,
      fetchRolePermissions,
      fetchAllFeatures,
      fetchOrganizationFeatures,
      updatePermissions,
      clearMessages,
      clearRolePermissions,
    } = usePermissions();

    const [allModules, setAllModules] = useState<
      { name: ModuleType; features: Feature[] }[]
    >([]);

    const [selectedPermissionKeys, setSelectedPermissionKeys] = useState<
      string[]
    >([]);
    const [openModules, setOpenModules] = useState<ModuleType[]>(['EMR']); // EMR module starts open

    const [subscriberId, setSubscriberId] = useState<string | null>(null);

    // Calculate current org id
    const currentOrgId = useMemo(() => {
      return isSuperAdmin
        ? selectedOrganization?.id
        : isOrganizationAdmin
          ? userOrganizationId || selectedOrganization?.id || null
          : null;
    }, [
      isSuperAdmin,
      isOrganizationAdmin,
      userOrganizationId,
      selectedOrganization,
    ]);

    // Fetch features on mount
    useEffect(() => {
      const fetchFeatures = async () => {
        fetchAllFeatures();

        if (currentOrgId) {
          let subscriberIdToUse: string | null =
            selectedOrganization?.subscriberId || null;

          // If no subscriberId in selectedOrganization, try to fetch it from organization data
          if (!subscriberIdToUse) {
            try {
              const orgData =
                await organizationService.fetchOrganizationById(currentOrgId);
              subscriberIdToUse = orgData.subscriberId || null;
              setSubscriberId(subscriberIdToUse);
            } catch (error) {
              console.error('Failed to fetch organization data:', error);
              subscriberIdToUse = null;
            }
          } else {
            setSubscriberId(subscriberIdToUse);
          }

          // If still no subscriberId from organization, check user data as fallback
          if (!subscriberIdToUse && user?.subscriberId) {
            subscriberIdToUse = user.subscriberId;
            setSubscriberId(subscriberIdToUse);
            console.log(
              'Using subscriberId from user data:',
              subscriberIdToUse
            );
          }

          // Only call fetchOrganizationFeatures if we have a subscriberId
          if (subscriberIdToUse) {
            fetchOrganizationFeatures(subscriberIdToUse);
          } else {
            // No subscriberId, will enable all features
            console.log('No subscriberId found, all features will be enabled');
          }
        }
      };

      fetchFeatures();
    }, [
      fetchAllFeatures,
      fetchOrganizationFeatures,
      currentOrgId,
      selectedOrganization,
      subscriberId,
      user,
    ]);

    // Build allModules when features data changes
    useEffect(() => {
      if (allFeatures.length > 0) {
        const orgFeatureIds = new Set(orgFeatures.map((f: ApiFeature) => f.id));
        // If no subscriberId or no orgFeatures (no subscription), enable all features
        const enableAllFeatures = !subscriberId || orgFeatures.length === 0;
        const modulesMap = new Map<string, Feature[]>();

        // First pass: collect main features
        allFeatures.forEach((feature: ApiFeature) => {
          if (!feature.subType || feature.subType.trim() === '') {
            const moduleName = feature.type as ModuleType;
            if (!modulesMap.has(moduleName)) {
              modulesMap.set(moduleName, []);
            }
            // Filter out module access keys from feature keys
            const moduleAccessKey =
              moduleName === 'EMR'
                ? permissionKeys.emr.access
                : moduleName === 'MRD'
                  ? permissionKeys.mrd.access
                  : moduleName === 'Billing'
                    ? permissionKeys.billing.access
                    : null;
            const filteredKeys = moduleAccessKey
              ? feature.permissionKeys.filter((key) => key !== moduleAccessKey)
              : feature.permissionKeys;
            modulesMap.get(moduleName)!.push({
              name: feature.featureName,
              label: feature.featureName,
              keys: filteredKeys,
              id: feature.id,
              isEnabled: enableAllFeatures || orgFeatureIds.has(feature.id),
            });
          }
        });

        // Second pass: add subfeatures
        allFeatures.forEach((feature: ApiFeature) => {
          if (feature.subType && feature.subType.trim() !== '') {
            const moduleName = feature.type as ModuleType;
            const mainFeatures = modulesMap.get(moduleName) || [];
            const mainFeature = mainFeatures.find(
              (f) =>
                f.name.trim().toLowerCase() ===
                feature.subType!.trim().toLowerCase()
            );
            if (mainFeature) {
              if (!mainFeature.subFeatures) mainFeature.subFeatures = [];
              // Filter out module access keys from subfeature keys
              const moduleAccessKey =
                moduleName === 'EMR'
                  ? permissionKeys.emr.access
                  : moduleName === 'MRD'
                    ? permissionKeys.mrd.access
                    : moduleName === 'Billing'
                      ? permissionKeys.billing.access
                      : null;
              const filteredKeys = moduleAccessKey
                ? feature.permissionKeys.filter(
                    (key) => key !== moduleAccessKey
                  )
                : feature.permissionKeys;
              mainFeature.subFeatures.push({
                name: feature.featureName,
                label: feature.featureName,
                keys: filteredKeys,
                id: feature.id,
                isEnabled: enableAllFeatures || orgFeatureIds.has(feature.id),
              });
            } else {
              // If no matching main feature, add as main feature
              if (!modulesMap.has(moduleName)) {
                modulesMap.set(moduleName, []);
              }
              // Filter out module access keys
              const moduleAccessKey =
                moduleName === 'EMR'
                  ? permissionKeys.emr.access
                  : moduleName === 'MRD'
                    ? permissionKeys.mrd.access
                    : moduleName === 'Billing'
                      ? permissionKeys.billing.access
                      : null;
              const filteredKeys = moduleAccessKey
                ? feature.permissionKeys.filter(
                    (key) => key !== moduleAccessKey
                  )
                : feature.permissionKeys;
              modulesMap.get(moduleName)!.push({
                name: feature.featureName,
                label: feature.featureName,
                keys: filteredKeys,
                id: feature.id,
                isEnabled: enableAllFeatures || orgFeatureIds.has(feature.id),
              });
            }
          }
        });

        const modules = Array.from(modulesMap.entries()).map(
          ([name, features]) => ({
            name: name as ModuleType,
            features,
          })
        );

        // Sort modules in the order: MRD, EMR, Billing
        const moduleOrder = ['MRD', 'EMR', 'Billing'];
        modules.sort((a, b) => {
          const indexA = moduleOrder.indexOf(a.name);
          const indexB = moduleOrder.indexOf(b.name);
          // If module not in order array, put it at the end
          const orderA = indexA === -1 ? moduleOrder.length : indexA;
          const orderB = indexB === -1 ? moduleOrder.length : indexB;
          return orderA - orderB;
        });
        setAllModules(modules);
      }
    }, [allFeatures, orgFeatures]);

    // Fetch role permissions on mount and clear previous state
    useEffect(() => {
      if (roleId) {
        // Clear previous permissions first
        clearRolePermissions();
        setSelectedPermissionKeys([]);
        fetchRolePermissions(roleId);
      }
    }, [roleId, fetchRolePermissions, clearRolePermissions]);

    // Update selected permission keys and open modules when role permissions change
    useEffect(() => {
      // Extract permission keys from role permissions (only those with valid keys)
      const keys: string[] = rolePermissions
        .filter((permission) => permission.key && permission.key !== null)
        .map((permission) => permission.key!)
        .filter((key): key is string => key !== undefined);

      // Only add module access keys if they are explicitly in the role permissions
      // Don't automatically add them based on other permissions
      setSelectedPermissionKeys(Array.from(new Set(keys)));

      // Only expand modules that have their access key explicitly set
      const open: ModuleType[] = [];
      if (keys.includes(permissionKeys.emr.access)) {
        open.push('EMR');
      }
      if (keys.includes(permissionKeys.mrd.access)) {
        open.push('MRD');
      }
      if (keys.includes(permissionKeys.billing.access)) {
        open.push('Billing');
      }
      setOpenModules(open); // Only expand modules that have access
    }, [rolePermissions]);

    // Clear messages and role permissions on unmount
    useEffect(() => {
      return () => {
        clearMessages();
        clearRolePermissions();
        setSelectedPermissionKeys([]);
      };
    }, [clearMessages, clearRolePermissions]);

    // Memoize permission lookup for performance
    const permissionMap = useMemo(() => {
      const map = new Map<string, boolean>();
      selectedPermissionKeys.forEach((key) => {
        map.set(key, true);
      });
      return map;
    }, [selectedPermissionKeys]);

    // Helper to get all keys from a feature and its subfeatures
    const getAllFeatureKeys = (feature: Feature): string[] => {
      let keys = [...feature.keys];
      if (feature.subFeatures) {
        feature.subFeatures.forEach((sub) => {
          keys = keys.concat(getAllFeatureKeys(sub));
        });
      }
      return keys;
    };

    // Handle module checkbox (parent) toggle
    const handleModuleToggle = useCallback(
      (moduleName: ModuleType) => {
        const module = allModules.find((m) => m.name === moduleName);
        if (!module) return;
        const moduleAccessKey =
          moduleName === 'EMR'
            ? permissionKeys.emr.access
            : moduleName === 'MRD'
              ? permissionKeys.mrd.access
              : moduleName === 'Billing'
                ? permissionKeys.billing.access
                : null;
        let newKeys = [...selectedPermissionKeys];
        const isChecked = isModuleChecked(moduleName, selectedPermissionKeys);

        if (isChecked) {
          // Uncheck: remove module access key and all child feature keys
          if (moduleAccessKey) {
            newKeys = newKeys.filter((key) => key !== moduleAccessKey);
          }
          // Remove all child feature keys
          module.features.forEach((feature) => {
            const allKeys = getAllFeatureKeys(feature);
            newKeys = newKeys.filter((key) => !allKeys.includes(key));
          });
        } else {
          // Check: add only the module access key
          if (moduleAccessKey && !newKeys.includes(moduleAccessKey)) {
            newKeys.push(moduleAccessKey);
          }
        }
        setSelectedPermissionKeys(newKeys);
        // Expand module if checked
        setOpenModules((prev) =>
          isChecked
            ? prev.filter((m) => m !== moduleName)
            : [...prev, moduleName]
        );
      },
      [selectedPermissionKeys, allModules]
    );

    const handleFeatureChange = useCallback(
      (feature: Feature, checked: boolean) => {
        console.log('handleFeatureChange', feature.name, checked, feature.keys);
        let newKeys = [...selectedPermissionKeys];
        const originalKeys = [...newKeys];

        if (checked) {
          // Add all permission keys for this feature
          feature.keys.forEach((key: string) => {
            if (!newKeys.includes(key)) {
              newKeys.push(key);
            }
          });
        } else {
          // Remove all permission keys for this feature
          newKeys = newKeys.filter((key) => !feature.keys.includes(key));
        }

        console.log('selectedPermissionKeys before:', originalKeys);
        console.log('selectedPermissionKeys after:', newKeys);
        setSelectedPermissionKeys(newKeys);
      },
      [selectedPermissionKeys]
    );

    const isFeatureChecked = useCallback(
      (feature: Feature) => {
        return feature.keys.every((key) => permissionMap.has(key));
      },
      [permissionMap]
    );

    // Handle save permissions
    const handleSavePermissions = useCallback(async () => {
      try {
        // Filter out any invalid permission keys and only send valid ones
        const validPermissionKeys = selectedPermissionKeys.filter(
          (key) => key && key.trim() !== ''
        );

        await updatePermissions(roleId, validPermissionKeys);
      } catch {
        // Error is handled by the Redux slice
      }
    }, [roleId, selectedPermissionKeys, updatePermissions]);

    // Close modal on successful save
    useEffect(() => {
      if (assignSuccess) {
        // Clear success state and close modal
        clearMessages();
        onClose();
      }
    }, [assignSuccess, clearMessages, onClose]);

    // Show loading spinner when fetching permissions or features
    if (combinedLoading) {
      return (
        <Box
          display='flex'
          justifyContent='center'
          alignItems='center'
          minHeight='60vh'
          sx={{ p: 2 }}
        >
          <LoadingSpinner size='md' text={'Loading features...'} />
        </Box>
      );
    }

    return (
      <Box
        sx={{ display: 'flex', flexDirection: 'column', height: '60vh', p: 2 }}
      >
        <Box sx={{ flex: 1, overflowY: 'auto', pr: 1, mb: 2 }}>
          {allModules.map((module) => {
            const isModuleOpen = openModules.includes(module.name);

            return (
              <Box key={module.name} sx={{ mb: 2 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={isModuleChecked(
                        module.name,
                        selectedPermissionKeys
                      )}
                      onChange={() => handleModuleToggle(module.name)}
                      disabled={assignLoading}
                    />
                  }
                  label={<Typography variant='h6'>{module.name}</Typography>}
                />
                <Collapse in={isModuleOpen}>
                  <Box sx={{ display: 'flex', flexDirection: 'column', ml: 3 }}>
                    <FormGroup>
                      {module.features.map((feature) => (
                        <Box key={feature.name}>
                          <FormControlLabel
                            control={
                              <Checkbox
                                checked={isFeatureChecked(feature)}
                                onChange={(e) =>
                                  handleFeatureChange(feature, e.target.checked)
                                }
                                disabled={assignLoading || !feature.isEnabled}
                              />
                            }
                            label={feature.label}
                          />
                          {/* Render subfeatures */}
                          {feature.subFeatures &&
                            isFeatureChecked(feature) &&
                            feature.subFeatures.map((subFeature) => (
                              <Box key={subFeature.name} sx={{ ml: 4 }}>
                                <FormControlLabel
                                  control={
                                    <Checkbox
                                      checked={isFeatureChecked(subFeature)}
                                      onChange={(e) =>
                                        handleFeatureChange(
                                          subFeature,
                                          e.target.checked
                                        )
                                      }
                                      disabled={
                                        assignLoading || !subFeature.isEnabled
                                      }
                                    />
                                  }
                                  label={subFeature.label}
                                />
                              </Box>
                            ))}
                        </Box>
                      ))}
                    </FormGroup>
                  </Box>
                </Collapse>
              </Box>
            );
          })}
        </Box>

        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            gap: 2,
            pt: 2,
            pb: 2,
            px: 3,
            borderTop: '1px solid',
            borderColor: 'divider',
            flexShrink: 0,
          }}
        >
          <Button variant='outlined' onClick={onClose} disabled={assignLoading}>
            CANCEL
          </Button>
          <Button
            variant='contained'
            onClick={handleSavePermissions}
            disabled={assignLoading}
            startIcon={<ShieldCheck className='w-4 h-4' />}
          >
            {assignLoading ? 'Saving...' : 'SAVE PERMISSIONS'}
          </Button>
        </Box>
      </Box>
    );
  }
);

RolePermissionManager.displayName = 'RolePermissionManager';

export default RolePermissionManager;
