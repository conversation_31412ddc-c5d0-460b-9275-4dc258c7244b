import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Patient } from '../../../types';
import { calculateAge } from '../../../utils/date-utils';
import { ApiError, InitialState } from '../../../utils/reducer-utils';
import patientService, {
  FetchPatientByIdParams,
  PatientParams,
} from './patient.service';

type ExtendedInitialState = InitialState<Patient> & {
  patients: Patient[];
  currentPatient: Patient | null;
  total: number;
  totalPages: number;
  page: number;
  limit: number;
  searchText: string;
  genderFilter: string;
  fromAge: number | null;
  toAge: number | null;
  fromDate: string;
  toDate: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  error: string | null;
};

const initialState: ExtendedInitialState = {
  loading: true, // Start with loading true to show loading initially
  updating: false,
  updateSuccess: false,
  entity: null,
  entities: [],
  errorMessage: null,
  successMessage: null,
  allEntities: [],
  patients: [],
  currentPatient: null,
  total: 0,
  totalPages: 0,
  page: 1,
  limit: 10, // Default page size
  searchText: '',
  genderFilter: '', // Matches genderFilterOptions "All Genders" value
  fromAge: null,
  toAge: null,
  fromDate: '',
  toDate: '',
  sortBy: 'id',
  sortOrder: 'desc',
  error: null,
};

export const fetchPatients = createAsyncThunk(
  'patients/fetchPatients',
  async (params: PatientParams, { rejectWithValue }) => {
    try {
      return await patientService.fetchPatients(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchPatientById = createAsyncThunk(
  'patients/fetchPatientById',
  async (params: FetchPatientByIdParams, { rejectWithValue }) => {
    try {
      return await patientService.fetchPatientById(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const patientSlice = createSlice({
  name: 'patients',
  initialState,
  reducers: {
    setSearchText: (state, action) => {
      state.searchText = action.payload;
      state.page = 1; // Reset to first page when searching
      // Don't set loading here - it will be set when the debounced API call starts
    },
    setGenderFilter: (state, action) => {
      state.genderFilter = action.payload;
      state.page = 1;
      // Gender filter is not debounced, so we can set loading immediately
      state.loading = true;
      // Clear existing data to prevent showing old data while loading
      state.patients = [];
      state.total = 0;
    },
    setAgeRange: (state, action) => {
      state.fromAge = action.payload.fromAge;
      state.toAge = action.payload.toAge;
      state.page = 1;
      // Don't set loading here - it will be set when the debounced API call starts
    },
    setDateRange: (state, action) => {
      state.fromDate = action.payload.fromDate;
      state.toDate = action.payload.toDate;
      state.page = 1;
      // Don't set loading here - it will be set when the debounced API call starts
    },
    setSorting: (state, action) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    setPage: (state, action) => {
      state.page = action.payload;
    },
    clearFilters: (state) => {
      state.searchText = '';
      state.genderFilter = '';
      state.fromAge = null;
      state.toAge = null;
      state.fromDate = '';
      state.toDate = '';
      state.page = 1;
    },
    clearMessages: (state) => {
      state.errorMessage = null;
      state.successMessage = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPatients.fulfilled, (state, action) => {
        state.patients = action.payload.items.map((item: any) => {
          // Extract common fields with fallbacks
          const patientId = item.id || '';
          const patientName = item.name || '';
          const patientDob = item.dob || '';
          // Handle gender as either string or {label, value} object
          const patientSex =
            typeof item.sex === 'object'
              ? item.sex?.value || item.sex?.label || ''
              : item.sex || '';
          const patientContact = item.contact || {};
          const patientAddress =
            typeof item.address === 'string'
              ? {
                  city: item.city || '',
                  state: item.state || '',
                  country: '',
                  street: '',
                }
              : { ...item.address };

          // Calculate age from DOB if available
          const age = patientDob ? calculateAge(patientDob) : 0;

          // Get registration date (created_on) with fallback
          const registrationDate =
            item.created_on || item.createdAt || new Date().toISOString();
          const updatedDate =
            item.updated_on || item.updatedAt || registrationDate;

          // Build the patient object with all possible fields
          return {
            id: patientId,
            firstName: patientName?.split(' ')[0] || '',
            lastName:
              patientName?.split(' ').slice(1).join(' ') ||
              patientName?.split(' ')[1] ||
              '',
            fullName: patientName,
            age: item.age || age,
            dob: patientDob,
            gender: (['Male', 'Female', 'Other'].includes(patientSex)
              ? patientSex
              : 'Other') as 'Male' | 'Female' | 'Other',
            contact: patientContact,
            contactPhone: patientContact.phone || '',
            email: patientContact.email || '',
            address: patientAddress,
            city: patientAddress.city || '',
            state: patientAddress.state || '',
            country: patientAddress.country || '',
            street: patientAddress.street || '',
            organizationId: item.organizationId || '',
            idProof: item.idProof || '',
            insurance: item.insurance || {},
            vitals: item.vitals || [],
            maritalStatus: item.maritalStatus || '',
            height: item.height || '',
            weight: item.weight || '',
            aadhar: item.aadhar || '',
            abha: item.abha || '',
            last_consultation_date: item.last_consultation_date || null,
            registrationDate: new Date(registrationDate),
            createdAt: new Date(registrationDate),
            updatedAt: new Date(updatedDate),
            created_by: item.created_by || '',
            updated_by: item.updated_by || '',
            _attachments: item._attachments || '',
            proof: item.proof || {},
          } as Patient;
        });
        state.total = action.payload.totalItemCount;
        state.totalPages = action.payload.totalPages;
        state.loading = false; // Set loading to false when data is loaded
        state.error = null;
        state.errorMessage = null;
      })
      .addCase(fetchPatientById.fulfilled, (state, action) => {
        const item = action.payload;
        // Normalize patient data similar to fetchPatients
        const patientId = item.id || '';
        const patientName = item.name || '';
        const patientDob = item.dob || '';
        const patientSex =
          typeof item.sex === 'object'
            ? item.sex?.value || item.sex?.label || ''
            : item.sex || '';
        const patientContact = item.contact || {};
        const patientAddress =
          typeof item.address === 'string'
            ? {
                city: item.city || '',
                state: item.state || '',
                country: '',
                street: '',
              }
            : { ...item.address };

        const age = patientDob ? calculateAge(patientDob) : 0;
        const registrationDate =
          item.created_on || item.createdAt || new Date().toISOString();
        const updatedDate =
          item.updated_on || item.updatedAt || registrationDate;

        state.currentPatient = {
          id: patientId,
          firstName: patientName?.split(' ')[0] || '',
          lastName:
            patientName?.split(' ').slice(1).join(' ') ||
            patientName?.split(' ')[1] ||
            '',
          fullName: patientName,
          age: item.age || age,
          dob: patientDob,
          gender: (['Male', 'Female', 'Other'].includes(patientSex)
            ? patientSex
            : 'Other') as 'Male' | 'Female' | 'Other',
          contact: patientContact,
          contactPhone: patientContact.phone || '',
          email: patientContact.email || '',
          address: patientAddress,
          city: patientAddress.city || '',
          state: patientAddress.state || '',
          country: patientAddress.country || '',
          street: patientAddress.street || '',
          organizationId: item.organizationId || '',
          idProof: item.idProof || '',
          insurance: item.insurance || {},
          vitals: item.vitals || [],
          maritalStatus: item.maritalStatus || '',
          height: item.height || '',
          weight: item.weight || '',
          aadhar: item.aadhar || '',
          abha: item.abha || '',
          last_consultation_date: item.last_consultation_date || null,
          registrationDate: new Date(registrationDate),
          createdAt: new Date(registrationDate),
          updatedAt: new Date(updatedDate),
          created_by: item.created_by || '',
          updated_by: item.updated_by || '',
          _attachments: item._attachments || '',
          proof: item.proof || {},
        } as Patient;
        state.loading = false;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isPending(fetchPatients), (state) => {
        state.loading = true;
        state.error = null;
        state.errorMessage = null;
        // Clear existing data when starting a new fetch to prevent showing old data
        if (state.patients.length > 0) {
          state.patients = [];
          state.total = 0;
        }
      })
      .addMatcher(isPending(fetchPatientById), (state) => {
        state.loading = true;
        state.error = null;
        state.errorMessage = null;
      })
      .addMatcher(isRejected(fetchPatients), (state, action) => {
        // Don't set loading to false if the request was cancelled (duplicate request)
        const error = action.payload as any;
        if (
          error?.name === 'RequestCancelledError' ||
          error?.message === 'Request was cancelled'
        ) {
          return; // Keep loading state as is for cancelled requests
        }

        state.loading = false;
        const apiError = error as ApiError;
        state.error = apiError?.message || 'Failed to fetch patients';
        state.errorMessage = apiError?.message || 'Failed to fetch patients';
      })
      .addMatcher(isRejected(fetchPatientById), (state, action) => {
        const error = action.payload as any;
        state.loading = false;
        const apiError = error as ApiError;
        state.error = apiError?.message || 'Failed to fetch patient';
        state.errorMessage = apiError?.message || 'Failed to fetch patient';
      });
  },
});

export const {
  setSearchText,
  setGenderFilter,
  setAgeRange,
  setDateRange,
  setSorting,
  setPage,
  clearFilters,
  clearMessages,
} = patientSlice.actions;

export default patientSlice.reducer;
