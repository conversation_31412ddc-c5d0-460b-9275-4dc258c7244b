import { BulkUpdateStatus } from '../features/lab-tests/types/bulk-update.types';
import { LabTestListParams } from '../features/lab-tests/types/labTest.types';
import { store } from '../store';
import {
  checkBulkUpdateStatus,
  stopBulkUpdatePolling,
  updateBulkUpdateStatus,
} from '../store/features/lab-tests/labTest.slice';

class BulkUpdatePollingService {
  private pollingTimer: NodeJS.Timeout | null = null;
  private isPolling = false;
  private currentStatusUrl: string | null = null;

  // Start polling for lab tests bulk update status
  startLabTestsPolling(statusUrl: string) {
    this.currentStatusUrl = statusUrl;
    this.isPolling = true;
    this.poll();
  }

  // Stop polling
  stopPolling() {
    this.isPolling = false;
    this.currentStatusUrl = null;
    if (this.pollingTimer) {
      clearTimeout(this.pollingTimer);
      this.pollingTimer = null;
    }
  }

  // Check if currently polling
  isCurrentlyPolling(): boolean {
    return this.isPolling;
  }

  // Private polling method
  private async poll() {
    if (!this.isPolling || !this.currentStatusUrl) {
      return;
    }

    try {
      // Dispatch the checkBulkUpdateStatus action
      const result = await store.dispatch(
        checkBulkUpdateStatus(this.currentStatusUrl)
      );

      if (
        result.type === 'labTests/checkBulkUpdateStatus/fulfilled' &&
        result.payload
      ) {
        const payload = result.payload as BulkUpdateStatus;

        // Update the status in Redux
        store.dispatch(updateBulkUpdateStatus(payload));

        // Continue polling if still processing
        if (payload.status === 'PROCESSING' || payload.status === 'PENDING') {
          this.scheduleNextPoll();
        } else {
          // Stop polling if completed or failed
          this.stopPolling();

          // If completed, refresh the lab tests list and clear selection
          if (payload.status === 'COMPLETED') {
            // Import and call fetchLabTests to refresh the list
            import('../store/features/lab-tests/labTest.slice').then(
              ({ fetchLabTests, clearBulkUpdateStatus }) => {
                const state = store.getState();
                const organizationId = state.auth.selectedOrganization?.id;

                if (organizationId) {
                  const labTestsState = state.labTests;
                  // Build params object with conditional isActive
                  const params: LabTestListParams = {
                    organizationId,
                    searchText: labTestsState.searchText,
                    department:
                      labTestsState.departmentFilter === 'all'
                        ? ''
                        : labTestsState.departmentFilter,
                    page: labTestsState.page,
                    pageSize: labTestsState.limit,
                  };

                  // Only add isActive if it's not 'all'
                  if (labTestsState.isActiveFilter !== 'all') {
                    params.isActive = labTestsState.isActiveFilter === 'active';
                  }

                  store.dispatch(fetchLabTests(params));
                }

                // Clear bulk update status and selection after completion
                store.dispatch(clearBulkUpdateStatus());
              }
            );
          }

          // If failed, enable buttons by stopping Redux polling
          if (payload.status === 'FAILED') {
            store.dispatch(stopBulkUpdatePolling());
          }
        }
      } else {
        // If API call failed, stop polling instead of retrying
        console.error('checkBulkUpdateStatus API failed, stopping polling');
        this.stopPolling();
        // Enable buttons by stopping Redux polling
        store.dispatch(stopBulkUpdatePolling());
      }
    } catch (error) {
      console.error('Error in bulk update polling:', error);
      // Stop polling on error instead of retrying
      this.stopPolling();
      // Enable buttons by stopping Redux polling
      store.dispatch(stopBulkUpdatePolling());
    }
  }

  // Schedule next poll
  private scheduleNextPoll() {
    if (this.isPolling) {
      this.pollingTimer = setTimeout(() => this.poll(), 3000); // 3 seconds
    }
  }

  // Get current status URL
  getCurrentStatusUrl(): string | null {
    return this.currentStatusUrl;
  }
}

// Export singleton instance
export const bulkUpdatePollingService = new BulkUpdatePollingService();
