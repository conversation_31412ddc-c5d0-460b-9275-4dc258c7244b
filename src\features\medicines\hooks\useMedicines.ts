import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearError,
  clearMessages,
  clearSelection,
  clearSuccessMessage,
  fetchMedicines,
  removeMedicines as removeMedicinesAction,
  selectAllMedicines,
  selectMedicine,
  setIsActiveFilter,
  setLimit,
  setPage,
  setSearchText,
  updateMedicineInList,
  updateMedicines,
} from '../../../store/features/medicines/medicine.slice';
import {
  MedicineListItem,
  MedicineListParams,
  UpdateMedicineRequest,
} from '../types/medicine.types';

export const useMedicines = (organizationId?: string) => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    medicines,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    isActiveFilter,
    selectedMedicines,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    bulkUpdate,
  } = useSelector((state: RootState) => state.medicines);

  const fetchMedicinesList = useCallback(
    (customParams?: Partial<MedicineListParams>) => {
      if (!organizationId) return;
      const params: MedicineListParams = {
        organizationId,
        page,
        pageSize: limit,
        ...(searchText && { searchText }),
        ...(isActiveFilter !== 'all' && {
          isActive: isActiveFilter === 'active',
        }),
        ...customParams,
      };
      dispatch(fetchMedicines(params));
    },
    [dispatch, organizationId, page, limit, searchText, isActiveFilter]
  );

  const updateMedicinesList = useCallback(
    async (data: UpdateMedicineRequest) => {
      const result = await dispatch(updateMedicines(data));
      if (updateMedicines.fulfilled.match(result)) {
        // Add 2-second delay before refreshing the list
        await new Promise((resolve) => setTimeout(resolve, 2000));
        fetchMedicinesList();
      }
      return result;
    },
    [dispatch, fetchMedicinesList]
  );

  const search = useCallback(
    (searchTerm: string) => {
      dispatch(setSearchText(searchTerm));
    },
    [dispatch]
  );

  const filterByStatus = useCallback(
    (status: string) => {
      dispatch(setIsActiveFilter(status));
    },
    [dispatch]
  );

  const handlePageChange = useCallback(
    (newPage: number) => {
      // Use Redux action for page changes to preserve selection
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const changeLimit = useCallback(
    (newLimit: number) => {
      dispatch(setLimit(newLimit));
    },
    [dispatch]
  );

  const toggleMedicineSelection = useCallback(
    (medicineId: string) => {
      dispatch(selectMedicine(medicineId));
    },
    [dispatch]
  );

  const toggleSelectAll = useCallback(() => {
    dispatch(selectAllMedicines());
  }, [dispatch]);

  const clearMedicineSelection = useCallback(() => {
    dispatch(clearSelection());
  }, [dispatch]);

  const updateMedicineInListLocal = useCallback(
    (medicineId: string, updates: Partial<MedicineListItem>) => {
      dispatch(updateMedicineInList({ medicineId, updates }));
    },
    [dispatch]
  );

  const clearErrorMessage = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const clearSuccessMsg = useCallback(() => {
    dispatch(clearSuccessMessage());
  }, [dispatch]);

  const clearAllMessages = useCallback(() => {
    dispatch(clearMessages());
  }, [dispatch]);

  const removeMedicines = useCallback(async () => {
    if (!organizationId)
      return { success: false, message: 'No organization selected' };

    const request = isAllSelected
      ? { organizationId, selectAll: true }
      : { organizationId, medicines: selectedMedicines };

    const result = await dispatch(removeMedicinesAction(request));

    if (removeMedicinesAction.fulfilled.match(result)) {
      // Add 2-second delay before refreshing the list
      await new Promise((resolve) => setTimeout(resolve, 2000));
      fetchMedicinesList();
    }

    return result.payload;
  }, [
    dispatch,
    organizationId,
    selectedMedicines,
    isAllSelected,
    fetchMedicinesList,
  ]);

  // Track organization changes and prevent duplicate calls
  const prevOrgIdRef = React.useRef<string | undefined>();
  const prevParamsRef = React.useRef<string>('');

  // Set loading state when component mounts and there's no data
  useEffect(() => {
    if (organizationId && medicines.length === 0 && !loading) {
      dispatch({ type: 'medicines/setLoading', payload: true });
    }
  }, [organizationId, medicines.length, loading, dispatch]);

  useEffect(() => {
    if (!organizationId) return;

    // Check if organization changed (not initial render)
    const isOrganizationChange =
      prevOrgIdRef.current && prevOrgIdRef.current !== organizationId;

    if (isOrganizationChange) {
      // Clear data and selections when organization changes
      dispatch({ type: 'medicines/handleOrganizationChange' });
    }

    // Update the ref
    prevOrgIdRef.current = organizationId;

    // Create params object
    const params: MedicineListParams = {
      organizationId,
      page,
      pageSize: limit,
      ...(searchText && { searchText }),
      ...(isActiveFilter !== 'all' && {
        isActive: isActiveFilter === 'active',
      }),
    };

    // Create a string representation of params to check for changes
    const paramsString = JSON.stringify(params);

    // Only make API call if params have actually changed
    if (prevParamsRef.current !== paramsString) {
      prevParamsRef.current = paramsString;
      dispatch(fetchMedicines(params));
    }

    // Clear success message on unmount
    return () => {
      if (successMessage) {
        dispatch(clearSuccessMessage());
      }
    };
  }, [
    organizationId,
    page,
    limit,
    searchText,
    isActiveFilter,
    dispatch,
    successMessage,

    // Note: 'loading' is intentionally excluded to prevent circular dependency
  ]);

  return {
    medicines,
    loading,
    updating,
    error,
    successMessage,
    total,
    page,
    limit,
    searchText,
    isActiveFilter,
    selectedMedicines,
    isAllSelected,
    continuationToken,
    hasMoreResults,
    totalFetched,
    totalPages,
    fetchMedicinesList,
    updateMedicinesList,
    search,
    filterByStatus,
    changePage: handlePageChange,
    changeLimit,
    toggleMedicineSelection,
    toggleSelectAll,
    clearMedicineSelection,
    updateMedicineInListLocal,
    clearErrorMessage,
    clearSuccessMsg,
    clearAllMessages,
    removeMedicines,

    // Computed values
    selectedCount: isAllSelected ? total : selectedMedicines.length,

    // Bulk update status
    bulkUpdateStatus: bulkUpdate.status,
    isBulkUpdatePolling: bulkUpdate.isPolling,
    isBulkUpdateInProgress:
      bulkUpdate.isPolling &&
      (bulkUpdate.status?.status === 'PROCESSING' ||
        bulkUpdate.status?.status === 'PENDING'),
  };
};
