import { Configuration } from '@azure/msal-browser';

// B2C policy names
const policyNames = {
  signUpSignIn: 'B2C_1_emrapp',
  passwordReset: 'B2C_1_reset_v3',
  editProfile: 'B2C_1_edit_profile_v2',
} as const;

// B2C authority configuration
export const b2cPolicies = {
  names: {
    signUpSignIn: policyNames.signUpSignIn,
    forgotPassword: policyNames.passwordReset,
    editProfile: policyNames.editProfile,
  },
  authorities: {
    signUpSignIn: {
      authority: `https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/${policyNames.signUpSignIn}`,
    },
    forgotPassword: {
      authority: `https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/${policyNames.passwordReset}`,
    },
    editProfile: {
      authority: `https://erm20240520.b2clogin.com/erm20240520.onmicrosoft.com/${policyNames.editProfile}`,
    },
  },
  authorityDomain: 'erm20240520.b2clogin.com',
} as const;

// Client ID for the Azure AD B2C application
const clientId = 'f22ad9c9-3fe2-4921-b825-ed8b887f3ab7';

// API Scopes
export const apiScopes = {
  // For Azure AD B2C, the scope should be in the format: "https://{tenant}.onmicrosoft.com/{app-id}/{scope}
  // or simply use the client ID as the scope for the access token
  apiRead: `${clientId}`,
  openid: 'openid',
  profile: 'profile',
  offlineAccess: 'offline_access',
} as const;

// Default scopes to request during login
export const defaultScopes = [
  apiScopes.apiRead,
  apiScopes.openid,
  apiScopes.profile,
  apiScopes.offlineAccess,
];

// MSAL configuration
export const msalConfig: Configuration = {
  auth: {
    clientId: 'f22ad9c9-3fe2-4921-b825-ed8b887f3ab7',
    authority: b2cPolicies.authorities.signUpSignIn.authority,
    knownAuthorities: [b2cPolicies.authorityDomain],
    redirectUri: '/', // Use relative path that matches Azure AD B2C configuration
    postLogoutRedirectUri: '/login', // Use relative path that matches Azure AD B2C configuration
    navigateToLoginRequestUrl: true, // Let MSAL handle the navigation to the original request URL
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false, // Set to true if you have issues with auth redirects in some browsers
  },
  system: {
    loggerOptions: {
      loggerCallback: (level, message, containsPii) => {
        if (containsPii) return;
        switch (level) {
          case 0: // Error
            console.error(message);
            break;
          case 1: // Warning
            console.warn(message);
            break;
          case 2: // Info
            console.info(message);
            break;
          case 3: // Verbose
            console.debug(message);
            break;
        }
      },
      // logLevel: process.env.NODE_ENV === 'development' ? 3 : 0, // Verbose in dev, errors only in prod
    },
    windowHashTimeout: 9000, // 9 seconds
    iframeHashTimeout: 9000,
    loadFrameTimeout: 9000,
  },
} as const;
