// Base Lab Test interface from LOINC database
export interface LabTest {
  id: string;
  testId: string; // LOINC test ID
  testName: string;
  shortName: string;
  displayName?: string; // Display name from API
  defaultCost: number; // System/LOINC provided cost
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// Organization-specific lab test configuration
export interface OrganizationLabTest {
  id: string;
  testId: string; // Reference to LOINC test
  organizationId: string;
  isActive: boolean;
  price?: number; // Organization-specific price (overrides defaultCost)
  departments: string[]; // Departments where this test is available
  createdAt: string;
  updatedAt: string;
  // Include test details for display
  labTest?: LabTest;
}

// Combined view for the lab test list
export interface LabTestListItem extends LabTest {
  // Organization-specific fields
  isActive: boolean;
  /** @deprecated Use organizationCost instead */
  organizationPrice?: number;
  organizationCost?: number;
  departments: string[];
  class?: string; // Lab test class/category from API
  organizationLabTestId?: string; // ID of the organization lab test record
  selection?: boolean; // For DataTable selection column
  isCustomTest?: boolean;
}

// API Request/Response types
export interface LabTestListResponse {
  tests: LabTestListItem[];
  totalRecords: number;
  currentPage: number;
  totalPages: number;
  continuationToken?: string | undefined;
  hasMoreResults?: boolean | undefined;
  pageSize?: number | undefined;
  totalFetched?: number | undefined;
}

export interface LabTestListParams {
  searchText?: string;
  department?: string;
  pageSize?: number;
  page?: number;
  organizationId: string;
  isActive?: boolean; // Made optional with ?
  continuationToken?: string;
}

// Create lab test request
export interface CreateLabTestRequest {
  testName: string;
  displayName?: string;
  department: string;
  organizationCost: number;
  cost: number;
  isActive: boolean;
  organizationId: string;
}

// Update lab test request
export interface UpdateLabTestRequest {
  organizationId: string;
  isChecked?: string; // For backward compatibility
  selectAll?: boolean; // New way to indicate select all
  department?: string; // Department filter for select all
  suppressStatusIndicator?: boolean; // Whether to suppress the status indicator
  tests?: {
    testId: string;
    isActive: boolean;
    price?: number;
    testName?: string;
    displayName?: string | null;
    department: string;
  }[];
}

// Form schemas
export interface LabTestFilterForm {
  searchText: string;
  department: string;
  isActive: string; // 'all', 'active', 'inactive'
}

export interface BulkUpdateForm {
  selectedTests: string[]; // testIds
  action: 'activate' | 'deactivate' | 'updatePrice' | 'updateDepartments';
  price?: number;
  departments?: string[];
}

// Department options
export const DEPARTMENT_OPTIONS = [
  'OTHERS',
  'Cardiology',
  'Clinical Chemistry',
  'Dermatology',
  'Emergency Medicine',
  'Endocrinology',
  'Gastroenterology',
  'General Medicine',
  'Hematology',
  'Nephrology',
  'Neurology',
  'Oncology',
  'Orthopedics',
  'Pediatrics',
  'Psychiatry',
  'Pulmonology',
  'Radiology',
  'Surgery',
  'Urology',
] as const;

export type Department = (typeof DEPARTMENT_OPTIONS)[number];

// Lab test scale types
export const SCALE_TYPES = [
  'Quantitative',
  'Ordinal',
  'Nominal',
  'Narrative',
  'Document',
] as const;

export type ScaleType = (typeof SCALE_TYPES)[number];

// Lab test systems
export const SYSTEMS = [
  'Blood',
  'Serum',
  'Plasma',
  'Urine',
  'CSF',
  'Tissue',
  'Saliva',
  'Stool',
  'Other',
] as const;

export type System = (typeof SYSTEMS)[number];
