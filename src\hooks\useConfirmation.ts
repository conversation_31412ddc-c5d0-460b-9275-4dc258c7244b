import { useState } from 'react';

interface ConfirmationConfig {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info';
  itemName?: string | undefined;
}

interface ConfirmationState extends ConfirmationConfig {
  isOpen: boolean;
  loading: boolean;
  onConfirm: (() => void) | null;
}

export const useConfirmation = () => {
  const [state, setState] = useState<ConfirmationState>({
    isOpen: false,
    loading: false,
    title: '',
    message: '',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'danger',
    itemName: undefined,
    onConfirm: null,
  });

  const showConfirmation = (
    config: ConfirmationConfig,
    onConfirm: () => void
  ) => {
    setState({
      ...config,
      isOpen: true,
      loading: false,
      onConfirm,
    });
  };

  const hideConfirmation = () => {
    setState((prev) => ({
      ...prev,
      isOpen: false,
      loading: false,
      onConfirm: null,
    }));
  };

  const setLoading = (loading: boolean) => {
    setState((prev) => ({
      ...prev,
      loading,
    }));
  };

  const handleConfirm = async () => {
    if (state.onConfirm) {
      setLoading(true);
      try {
        await state.onConfirm();
        hideConfirmation();
      } catch (error) {
        setLoading(false);
        // Error handling is done by the calling component
      }
    }
  };

  // Convenience methods for common confirmation types
  const confirmDelete = (
    itemName: string,
    onConfirm: () => void,
    customMessage?: string
  ) => {
    showConfirmation(
      {
        title: 'Delete Confirmation',
        message:
          customMessage ||
          `Are you sure you want to delete this item? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
        itemName,
      },
      onConfirm
    );
  };

  const confirmAction = (
    title: string,
    message: string,
    onConfirm: () => void,
    options?: Partial<ConfirmationConfig>
  ) => {
    showConfirmation(
      {
        title,
        message,
        confirmText: 'Confirm',
        cancelText: 'Cancel',
        type: 'warning',
        ...options,
      },
      onConfirm
    );
  };

  return {
    // State
    isOpen: state.isOpen,
    loading: state.loading,
    title: state.title,
    message: state.message,
    confirmText: state.confirmText,
    cancelText: state.cancelText,
    type: state.type,
    itemName: state.itemName,

    // Actions
    showConfirmation,
    hideConfirmation,
    handleConfirm,

    // Convenience methods
    confirmDelete,
    confirmAction,
  };
};
