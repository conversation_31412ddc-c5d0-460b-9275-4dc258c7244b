import { Button } from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const UnauthenticatedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-100'>
      <div className='max-w-md w-full px-6 py-8 bg-white rounded-lg shadow-md'>
        <div className='text-center'>
          <svg
            className='mx-auto h-16 w-16 text-yellow-500'
            fill='none'
            stroke='currentColor'
            viewBox='0 0 24 24'
          >
            <path
              strokeLinecap='round'
              strokeLinejoin='round'
              strokeWidth={2}
              d='M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8V7a4 4 0 00-8 0v4h8z'
            />
          </svg>
          <h2 className='mt-4 text-2xl font-semibold text-gray-800'>
            Authentication Required
          </h2>
          <p className='mt-2 text-gray-600'>
            Please sign in to access this page. If you believe this is an error,
            contact your administrator.
          </p>
          <div className='mt-6'>
            <Button
              variant='contained'
              color='primary'
              onClick={() => navigate('/login')}
              className='w-full'
            >
              Sign In
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnauthenticatedPage;
