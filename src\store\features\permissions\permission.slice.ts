import {
  createAsyncThunk,
  createSlice,
  isPending,
  isRejected,
} from '@reduxjs/toolkit';

import { Permission } from '../../../types';
import { ApiError } from '../../../utils/reducer-utils';
import permissionService, {
  AssignPermissionsData,
  GetRolePermissionsParams,
} from './permission.service';

interface ApiFeature {
  id: string;
  featureName: string;
  description: string;
  type: string;
  subType?: string;
  permissionKeys: string[];
  isActive: boolean;
}

interface PermissionState {
  rolePermissions: Permission[];
  allPermissions: Permission[];
  allFeatures: ApiFeature[];
  orgFeatures: ApiFeature[];
  allFeaturesLoading: boolean;
  orgFeaturesLoading: boolean;
  loading: boolean;
  error: string | null;
  assignLoading: boolean;
  assignError: string | null;
  assignSuccess: boolean;
  successMessage: string | null;
}

const initialState: PermissionState = {
  rolePermissions: [],
  allPermissions: [],
  allFeatures: [],
  orgFeatures: [],
  allFeaturesLoading: false,
  orgFeaturesLoading: false,
  loading: false,
  error: null,
  assignLoading: false,
  successMessage: null,
  assignError: null,
  assignSuccess: false,
};

// Async thunks
export const assignPermissions = createAsyncThunk(
  'permissions/assignPermissions',
  async (data: AssignPermissionsData, { rejectWithValue }) => {
    try {
      const response = await permissionService.assignPermissions(data);
      return { ...response, roleId: data.roleId };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const updatePermissions = createAsyncThunk(
  'permissions/updatePermissions',
  async (data: AssignPermissionsData, { rejectWithValue }) => {
    try {
      const response = await permissionService.updatePermissions(data);
      return { ...response, roleId: data.roleId };
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getRolePermissions = createAsyncThunk(
  'permissions/getRolePermissions',
  async (params: GetRolePermissionsParams, { rejectWithValue }) => {
    try {
      return await permissionService.getRolePermissions(params);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getAllFeatures = createAsyncThunk(
  'permissions/getAllFeatures',
  async (_, { rejectWithValue }) => {
    try {
      return await permissionService.getAllFeatures();
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const getOrganizationFeatures = createAsyncThunk(
  'permissions/getOrganizationFeatures',
  async (subscriberId: string, { rejectWithValue }) => {
    try {
      return await permissionService.getOrganizationFeatures(subscriberId);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const permissionSlice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {
    clearAssignMessages: (state) => {
      state.assignSuccess = false;
      state.assignError = null;
      state.successMessage = null;
    },
    clearRolePermissions: (state) => {
      state.rolePermissions = [];
    },
  },
  extraReducers: (builder) => {
    // Assign permissions
    builder.addCase(assignPermissions.fulfilled, (state, _action) => {
      state.assignLoading = false;
      state.assignSuccess = true;
      state.assignError = null;
      state.successMessage = 'Permissions assigned successfully';
    });

    // Update permissions
    builder.addCase(updatePermissions.fulfilled, (state, _action) => {
      state.assignLoading = false;
      state.assignSuccess = true;
      state.assignError = null;
      state.successMessage = 'Permissions updated successfully';
    });

    // Get role permissions
    builder.addCase(getRolePermissions.fulfilled, (state, action) => {
      state.loading = false;
      // Transform API response to match our permission format
      // Handle new structure with APIs array containing permissionKey objects
      const payload = action.payload;
      let permissions: string[] = [];

      if (payload && payload.APIs && Array.isArray(payload.APIs)) {
        // New structure: extract permissionKey from APIs array
        permissions = payload.APIs.filter(
          (api: { permissionKey: string }) =>
            api.permissionKey && api.permissionKey.trim() !== ''
        ).map((api: { permissionKey: string }) => api.permissionKey);
      } else if (payload && payload.data && Array.isArray(payload.data)) {
        // Fallback to old structure for backward compatibility
        permissions = payload.data
          .filter(
            (permission: {
              key: string | null;
              api: string;
              methods: string[];
            }) => permission.key !== null && permission.key !== undefined
          )
          .map(
            (permission: { key: string; api: string; methods: string[] }) =>
              permission.key
          );
      }

      // Map to our permission format
      state.rolePermissions = permissions.map((key: string) => ({
        id: key,
        key,
        api: '', // Not available in new structure
        methods: [], // Not available in new structure
      }));
      state.error = null;
    });

    // Get all features
    builder.addCase(getAllFeatures.fulfilled, (state, action) => {
      state.allFeaturesLoading = false;
      state.allFeatures = action.payload.features || [];
      state.error = null;
    });

    // Get organization features
    builder.addCase(getOrganizationFeatures.fulfilled, (state, action) => {
      state.orgFeaturesLoading = false;
      state.orgFeatures = action.payload.features || [];
      state.error = null;
    });

    // Handle pending states
    builder.addMatcher(isPending, (state, action) => {
      if (
        action.type.includes('assignPermissions') ||
        action.type.includes('updatePermissions')
      ) {
        state.assignLoading = true;
        state.assignError = null;
      } else if (action.type.includes('getAllFeatures')) {
        state.allFeaturesLoading = true;
        state.error = null;
      } else if (action.type.includes('getOrganizationFeatures')) {
        state.orgFeaturesLoading = true;
        state.error = null;
      } else {
        state.loading = true;
        state.error = null;
      }
    });

    // Handle rejected states
    builder.addMatcher(isRejected, (state, action) => {
      const error = action.payload as ApiError;
      if (
        action.type.includes('assignPermissions') ||
        action.type.includes('updatePermissions')
      ) {
        state.assignLoading = false;
        state.error =
          error?.response?.data?.message ||
          error?.message ||
          'Failed to assign permissions. Please try again.';
      } else {
        state.loading = false;
        state.error =
          (typeof error?.response?.data === 'string'
            ? error.response.data
            : error?.response?.data?.message) ||
          error?.message ||
          'Failed to load permissions. Please try again.';
      }
    });
  },
});

export const { clearAssignMessages, clearRolePermissions } =
  permissionSlice.actions;

export default permissionSlice.reducer;
