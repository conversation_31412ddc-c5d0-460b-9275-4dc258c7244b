import { format } from 'date-fns';
import { HeartPulse, Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import DataTable, { Column } from '../../components/Common/DataTable';
import Modal from '../../components/Common/Modal';
import OrganizationRequired from '../../components/Common/OrganizationRequired';
import StatusBadge from '../../components/Common/StatusBadge';
import { useAuth } from '../../hooks/useAuth';
import { AppDispatch, RootState } from '../../store';
import { fetchVitals } from '../../store/slices/vitalSlice';
import { VitalType } from '../../types';
import { getOrganizationId } from '../../utils/organization-utils';
import VitalForm from './VitalForm';

const VitalList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { selectedOrganization } = useAuth();

  const { vitals, loading, total, page, limit } = useSelector(
    (state: RootState) => state.vitals
  );

  const [statusFilter, setStatusFilter] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedVital, setSelectedVital] = useState<VitalType | null>(null);

  useEffect(() => {
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchVitals({
          organizationId: orgId,
          page: 1,
          limit: 10,
        })
      );
    }
  }, [dispatch, selectedOrganization]);

  const handlePageChange = (newPage: number) => {
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchVitals({
          organizationId: orgId,
          page: newPage,
          limit,
          status: statusFilter,
        })
      );
    }
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    const orgId = getOrganizationId(selectedOrganization);
    if (orgId) {
      dispatch(
        fetchVitals({
          organizationId: orgId,
          page: 1,
          limit,
          status,
        })
      );
    }
  };

  const handleCreate = () => {
    setSelectedVital(null);
    setShowModal(true);
  };

  const handleEdit = (vital: VitalType) => {
    setSelectedVital(vital);
    setShowModal(true);
  };

  const columns: Column<VitalType>[] = [
    {
      key: 'name',
      label: 'Vital Name',
      render: (name, vital) => (
        <div className='flex items-center space-x-3'>
          <div className='flex-shrink-0'>
            <div className='w-10 h-10 bg-red-100 rounded-full flex items-center justify-center'>
              <HeartPulse className='w-5 h-5 text-red-600' />
            </div>
          </div>
          <div>
            <div className='font-medium text-gray-900'>{name}</div>
            <div className='text-sm text-gray-500'>Unit: {vital.unit}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'dataType',
      label: 'Data Type',
      render: (type) => (
        <span className='px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 capitalize'>
          {type.replace('-', ' ')}
        </span>
      ),
    },
    {
      key: 'status',
      label: 'Status',
      render: (status) => <StatusBadge status={status} />,
    },
    {
      key: 'createdAt',
      label: 'Created At',
      render: (createdAt) => format(new Date(createdAt), 'MMM dd, yyyy HH:mm'),
    },
  ];

  if (!selectedOrganization) {
    return <OrganizationRequired feature='vitals' />;
  }

  return (
    <div className='space-y-6'>
      <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Vitals</h1>
          <p className='mt-1 text-sm text-gray-500'>
            Manage vital sign types for {selectedOrganization.name}
          </p>
        </div>
        <button
          onClick={handleCreate}
          className='mt-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
        >
          <Plus className='w-4 h-4 mr-2' />
          Add Vital
        </button>
      </div>

      <DataTable
        data={vitals}
        columns={columns}
        loading={loading}
        pagination={{
          page,
          limit,
          total,
          onPageChange: handlePageChange,
        }}
        filterable={true}
        filters={
          <div className='flex items-center space-x-4'>
            <label
              htmlFor='status-filter'
              className='text-sm font-medium text-gray-700'
            >
              Status:
            </label>
            <select
              id='status-filter'
              value={statusFilter}
              onChange={(e) => handleStatusFilter(e.target.value)}
              className='block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md'
            >
              <option value=''>All</option>
              <option value='active'>Active</option>
              <option value='inactive'>Inactive</option>
            </select>
          </div>
        }
        onEdit={handleEdit}
      />

      <Modal
        isOpen={showModal}
        title={selectedVital ? 'Edit Vital' : 'Create Vital'}
        onClose={() => setShowModal(false)}
      >
        <VitalForm
          vital={selectedVital}
          onSuccess={() => setShowModal(false)}
        />
      </Modal>
    </div>
  );
};

export default VitalList;
