import { Button } from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router-dom';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className='min-h-screen flex items-center justify-center bg-gray-100'>
      <div className='text-center'>
        <h1 className='text-6xl font-bold text-gray-800 mb-4'>404</h1>
        <h2 className='text-2xl font-semibold text-gray-600 mb-4'>
          Page Not Found
        </h2>
        <p className='text-gray-500 mb-8'>
          The page you're looking for doesn't exist or has been moved.
        </p>
        <Button
          variant='contained'
          color='primary'
          onClick={() => navigate('/')}
        >
          Return to Home
        </Button>
      </div>
    </div>
  );
};

export default NotFoundPage;
