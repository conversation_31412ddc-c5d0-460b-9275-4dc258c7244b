import React from 'react';

interface GreenCheckIconProps {
  width?: number;
  height?: number;
  className?: string;
}

const GreenCheckIcon: React.FC<GreenCheckIconProps> = ({
  width = 18,
  height = 18,
  className = '',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox='0 0 18 18'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        xmlns='http://www.w3.org/2000/svg'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.841 0.589553C9.90972 -0.196518 8.54732 -0.196517 7.616 0.589553L6.76784 1.30544C6.372 1.63954 5.88217 1.84245 5.36602 1.8861L4.26007 1.97963C3.04569 2.08233 2.08233 3.04569 1.97963 4.26007L1.8861 5.36602C1.84245 5.88217 1.63954 6.372 1.30544 6.76785L0.589553 7.616C-0.196518 8.54732 -0.196517 9.90972 0.589553 10.841L1.30544 11.6892C1.63954 12.085 1.84245 12.5749 1.8861 13.091L1.97963 14.197C2.08233 15.4114 3.04569 16.3748 4.26007 16.4774L5.36602 16.5709C5.88217 16.6146 6.372 16.8175 6.76785 17.1516L7.616 17.8675C8.54732 18.6535 9.90972 18.6535 10.841 17.8675L11.6892 17.1516C12.085 16.8175 12.5749 16.6146 13.091 16.5709L14.197 16.4774C15.4114 16.3748 16.3748 15.4114 16.4774 14.197L16.5709 13.091C16.6146 12.5749 16.8175 12.085 17.1516 11.6892L17.8675 10.841C18.6535 9.90972 18.6535 8.54732 17.8675 7.616L17.1516 6.76784C16.8175 6.372 16.6146 5.88217 16.5709 5.36602L16.4774 4.26007C16.3748 3.04569 15.4114 2.08233 14.197 1.97963L13.091 1.8861C12.5749 1.84245 12.085 1.63954 11.6892 1.30544L10.841 0.589553ZM13.7745 7.52407C14.2139 7.08473 14.2139 6.37242 13.7745 5.93308C13.3353 5.49373 12.6229 5.49373 12.1835 5.93308L7.97905 10.1376L6.27455 8.43308C5.83522 7.99373 5.1229 7.99373 4.68357 8.43308C4.24422 8.87242 4.24422 9.58473 4.68357 10.0241L7.18357 12.524C7.6229 12.9634 8.33522 12.9634 8.77455 12.524L13.7745 7.52407Z'
        fill='#3ED37A'
      />
    </svg>
  );
};

export default GreenCheckIcon;
