import React, { useEffect, useRef, useState } from 'react';

interface TooltipProps {
  children: React.ReactNode;
  title: React.ReactNode;
  open?: boolean;
  arrow?: boolean;
  hidden?: boolean;
  wrapperClassName?: string;
}

const Tooltip: React.FC<TooltipProps> = ({
  children,
  title,
  open = false,
  arrow: _arrow = true,
  hidden = false,
  wrapperClassName = '',
}) => {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (open && wrapperRef.current) {
      const wrapperRect = wrapperRef.current.getBoundingClientRect();

      let top = wrapperRect.bottom + 8;
      let left = wrapperRect.left;

      // Ensure minimum positioning
      if (left < 16) {
        left = 16;
      }

      if (top < 16) {
        top = wrapperRect.bottom + 8;
      }

      setPosition({ top, left });
    }
  }, [open]);

  if (hidden) {
    return <div className={wrapperClassName}>{children}</div>;
  }

  return (
    <>
      <div ref={wrapperRef} className={wrapperClassName}>
        {children}
      </div>
      {open && (
        <div
          ref={tooltipRef}
          className='fixed z-50 w-auto max-w-[90vw]'
          style={{
            top: position.top,
            left: Math.max(
              16,
              Math.min(position.left, window.innerWidth - 370)
            ),
          }}
        >
          {title}
        </div>
      )}
    </>
  );
};

export default Tooltip;
