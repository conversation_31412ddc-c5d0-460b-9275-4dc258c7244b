import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Navigate } from 'react-router-dom';

import GreenCheckIcon from '../../components/Common/GreenCheckIcon';
import { SUBSCRIBERS_ENDPOINT } from '../../constants/api-endpoints';
import { PATHS } from '../../constants/paths';
import { useToast } from '../../contexts/ToastContext';
import { useAuth } from '../../hooks/useAuth';
import api from '../../services/api';
import {
  getOrganizationPlan,
  getSubscriptionFeatures,
  SubscriptionFeature,
  SubscriptionPlan,
} from '../../store/features/subscription/subscription.service';
import { getDaysUntilDate } from '../../utils/date-utils';

// Helper function to check if error is from a cancelled request
const isCancelledRequestError = (error: unknown): boolean => {
  if (!error) return false;
  const errorObj = error as Record<string, unknown>;
  return (
    errorObj?.name === 'RequestCancelledError' ||
    errorObj?.name === 'CanceledError' ||
    errorObj?.name === 'Cancel' ||
    errorObj?.code === 'ERR_CANCELED' ||
    errorObj?.code === 'CANCELLED' ||
    (typeof errorObj?.message === 'string' &&
      errorObj.message.toLowerCase().includes('cancel'))
  );
};

interface SubscriberResponse {
  id: string;
  name: string;
  activeSubscription?: {
    id: string;
    planName: string;
    endDate: string;
    totalAmount: number;
    features: {
      MRD?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      EMR?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      Billing?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
    };
    addOnFeatures: {
      MRD?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      EMR?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
      Billing?: Array<{
        featureId: string;
        monthlyAmount: number;
        yearlyAmount: number;
      }>;
    };
  };
  subscriptionHistory?: Array<unknown>;
}

const SubscriptionList: React.FC = () => {
  const { isSubscriptionOrganization } = useAuth();
  const [subscriberData, setSubscriberData] =
    useState<SubscriberResponse | null>(null);
  const [organizationPlan, setOrganizationPlan] =
    useState<SubscriptionPlan | null>(null);
  const [features, setFeatures] = useState<SubscriptionFeature[]>([]);
  const [loading, setLoading] = useState(false);
  const { error: showError } = useToast();
  const hasFetchedRef = useRef(false);
  const hasFetchedOrgPlanRef = useRef(false);

  // Get subscriberId from emrUserInfo in localStorage
  const getSubscriberId = useCallback((): string | null => {
    try {
      const emrUserInfoStr = localStorage.getItem('emrUserInfo');
      if (emrUserInfoStr) {
        const emrUserInfo = JSON.parse(emrUserInfoStr);
        return emrUserInfo.subscriberId || null;
      }
    } catch {
      // Failed to get subscriberId from localStorage
    }
    return null;
  }, []);

  const fetchSubscriberDetails = useCallback(async () => {
    const subscriberId = getSubscriberId();
    if (!subscriberId) {
      showError('Error', 'Subscriber ID not found. Please log in again.');
      return;
    }

    setLoading(true);
    try {
      // Fetch subscriber details using the API service
      const response = await api.get<SubscriberResponse>(SUBSCRIBERS_ENDPOINT, {
        params: { id: subscriberId },
      });

      const data = response.data;
      setSubscriberData(data);

      // Fetch all features to get feature names
      const featuresResponse = await getSubscriptionFeatures();
      setFeatures(featuresResponse.features || []);
    } catch (err: unknown) {
      if (isCancelledRequestError(err)) {
        setLoading(false);
        return;
      }
      const errorMessage =
        (err as { response?: { data?: { message?: string } } })?.response?.data
          ?.message ||
        (err as { message?: string })?.message ||
        'Failed to load subscription details';
      showError('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  }, [getSubscriberId, showError]);

  const fetchOrganizationPlan = useCallback(async () => {
    try {
      const plan = await getOrganizationPlan();
      setOrganizationPlan(plan);
      hasFetchedOrgPlanRef.current = true;
    } catch (err: unknown) {
      if (isCancelledRequestError(err)) {
        return;
      }
      const errorMessage =
        (err as { response?: { data?: { message?: string } } })?.response?.data
          ?.message ||
        (err as { message?: string })?.message ||
        'Failed to load organization plan';
      showError('Error', errorMessage);
    }
  }, [showError]);

  useEffect(() => {
    if (!hasFetchedRef.current) {
      fetchSubscriberDetails();
      hasFetchedRef.current = true;
    }
    if (!hasFetchedOrgPlanRef.current) {
      fetchOrganizationPlan();
      hasFetchedOrgPlanRef.current = true;
    }
  }, [fetchSubscriberDetails, fetchOrganizationPlan]);

  // Format date for display
  const formatDate = useCallback((dateString: string | undefined): string => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric',
      });
    } catch {
      return dateString;
    }
  }, []);

  // Format currency
  const formatCurrency = useCallback(
    (amount: number | undefined | null): string => {
      if (amount === undefined || amount === null) return '₹0';
      return `₹${amount.toLocaleString('en-IN')}`;
    },
    []
  );

  // Check if addons are present
  const hasAddons = useMemo(() => {
    if (!subscriberData?.activeSubscription?.addOnFeatures) return false;
    const addons = subscriberData.activeSubscription.addOnFeatures;
    return (
      (addons.MRD && addons.MRD.length > 0) ||
      (addons.EMR && addons.EMR.length > 0) ||
      (addons.Billing && addons.Billing.length > 0)
    );
  }, [subscriberData]);

  // Get feature name by featureId
  const getFeatureName = useCallback(
    (featureId: string): string => {
      const feature = features.find((f) => f.id === featureId);
      return feature?.featureName || featureId;
    },
    [features]
  );

  // Build hierarchical features list
  const hierarchicalFeatures = useMemo(() => {
    if (!subscriberData?.activeSubscription) return [];

    const result: Array<{
      category: string;
      features: Array<{ name: string; isAddon: boolean }>;
    }> = [];

    const categories = ['MRD', 'EMR', 'Billing'] as const;

    categories.forEach((category) => {
      const categoryFeatures: Array<{ name: string; isAddon: boolean }> = [];

      // Add regular features
      if (subscriberData.activeSubscription?.features[category]) {
        subscriberData.activeSubscription.features[category]?.forEach(
          (feature) => {
            categoryFeatures.push({
              name: getFeatureName(feature.featureId),
              isAddon: false,
            });
          }
        );
      }

      // Add addon features
      if (subscriberData.activeSubscription?.addOnFeatures[category]) {
        subscriberData.activeSubscription.addOnFeatures[category]?.forEach(
          (feature) => {
            categoryFeatures.push({
              name: getFeatureName(feature.featureId),
              isAddon: true,
            });
          }
        );
      }

      if (categoryFeatures.length > 0) {
        result.push({
          category,
          features: categoryFeatures,
        });
      }
    });

    return result;
  }, [subscriberData, getFeatureName]);

  // Extract heading from organization plan description
  const extractHeading = useCallback((html: string | undefined): string => {
    if (!html) return 'Customise according to your needs';
    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(html, 'text/html');
      // Try to find the heading in strong tag or h2
      const strongTag = doc.querySelector('p strong');
      if (strongTag) {
        const text = strongTag.textContent?.trim();
        if (text) return text;
      }
      const h2Tag = doc.querySelector('h2 strong');
      if (h2Tag) {
        const text = h2Tag.textContent?.trim();
        if (text) return text;
      }
    } catch {
      // Fallback to default
    }
    return 'Customise according to your needs';
  }, []);

  // Sanitize HTML for safe rendering
  const sanitizeHtml = useCallback((html?: string): string => {
    if (!html) return '';
    if (typeof window === 'undefined') {
      return html;
    }
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    doc
      .querySelectorAll('script, style, iframe, object')
      .forEach((el) => el.remove());
    return doc.body.innerHTML || '';
  }, []);

  // Get description content after "Get a Quote" heading
  const getDescriptionAfterQuote = useCallback(
    (html: string | undefined): string => {
      if (!html) return '';
      try {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // Find the "Get a Quote" h2 element
        let foundQuoteHeading = false;
        const contentParts: string[] = [];

        // Get all elements after the "Get a Quote" h2
        doc.body.childNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            if (element.tagName === 'H2') {
              const text = element.textContent?.trim().toLowerCase();
              if (text && text.includes('get a quote')) {
                foundQuoteHeading = true;
                return;
              }
            }
            if (foundQuoteHeading) {
              contentParts.push(element.outerHTML);
            }
          }
        });

        if (contentParts.length > 0) {
          return contentParts.join('');
        }

        // Fallback: return everything after the first h2
        const firstH2 = doc.querySelector('h2');
        if (firstH2) {
          let afterH2 = '';
          let found = false;
          doc.body.childNodes.forEach((node) => {
            if (found) {
              if (node.nodeType === Node.ELEMENT_NODE) {
                afterH2 += (node as Element).outerHTML;
              } else if (node.nodeType === Node.TEXT_NODE) {
                afterH2 += node.textContent;
              }
            }
            if (node === firstH2) {
              found = true;
            }
          });
          return afterH2;
        }

        return html;
      } catch {
        return html || '';
      }
    },
    []
  );

  const organizationPlanHeading = useMemo(() => {
    if (!organizationPlan?.description)
      return 'Customise according to your needs';
    return extractHeading(organizationPlan.description);
  }, [organizationPlan, extractHeading]);

  const descriptionAfterQuote = useMemo(() => {
    if (!organizationPlan?.description) return '';
    const content = getDescriptionAfterQuote(organizationPlan.description);
    return sanitizeHtml(content);
  }, [organizationPlan, getDescriptionAfterQuote, sanitizeHtml]);

  const handleGetQuote = () => {
    const baseUrl =
      (typeof window !== 'undefined'
        ? (window as any)._env_?.VITE_EMR_URL
        : '') ||
      import.meta.env.VITE_EMR_URL ||
      '';
    window.open(`${baseUrl}/subscription/custom-pricing`, '_blank');
  };

  if (isSubscriptionOrganization()) {
    return <Navigate to={PATHS.DASHBOARD} replace />;
  }

  if (loading && !subscriberData) {
    return (
      <div className='space-y-6'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Subscription</h1>
        </div>
        <div className='flex justify-center items-center py-8'>
          <p className='text-gray-500'>Loading subscription details...</p>
        </div>
      </div>
    );
  }

  if (!subscriberData?.activeSubscription) {
    return (
      <div className='space-y-6'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Subscription</h1>
        </div>
        <div className='bg-white rounded-lg shadow p-6'>
          <p className='text-gray-500'>No subscription found.</p>
        </div>
      </div>
    );
  }

  const subscription = subscriberData.activeSubscription;

  return (
    <div className='space-y-3'>
      <div>
        <h1 className='text-2xl font-bold text-gray-900'>Subscription</h1>
      </div>

      <div className='flex gap-6 items-stretch'>
        <div className='w-[48%] bg-white rounded-lg shadow p-3 flex flex-col'>
          <h3 className='text-md font-semibold text-gray-900 mt-2'>
            Your Current Plan
          </h3>
          <div className='flex items-center gap-2 mb-1'>
            {subscription.planName && (
              <span className='text-3xl font-bold text-black-600'>
                {subscription.planName}
              </span>
            )}
          </div>

          <div className='space-y-1 mb-4'>
            {subscription.endDate &&
              (() => {
                const daysUntilExpiry = getDaysUntilDate(subscription.endDate);
                const isExpiringSoon =
                  daysUntilExpiry <= 30 && daysUntilExpiry >= 0;

                return (
                  <p className='text-sm text-red-600'>
                    {isExpiringSoon
                      ? daysUntilExpiry === 0
                        ? 'Expiring today'
                        : `Expires in ${daysUntilExpiry} ${daysUntilExpiry === 1 ? 'day' : 'days'}`
                      : `Expiring on ${formatDate(subscription.endDate)}`}
                  </p>
                );
              })()}

            <div className='flex items-baseline gap-2'>
              <span className='text-xl font-bold text-black-600'>
                {formatCurrency(subscription.totalAmount)}
              </span>
              {hasAddons && (
                <span className='text-xl font-bold text-black-600'>
                  (With add ons)
                </span>
              )}
            </div>
          </div>

          {/* Features List - Scrollable */}
          {hierarchicalFeatures.length > 0 && (
            <div className='flex-1 max-h-96 overflow-y-auto pr-2 scrollbar-no-arrows'>
              <div className='space-y-4'>
                {hierarchicalFeatures.map((categoryData) => (
                  <div key={categoryData.category} className='space-y-2'>
                    <div className='text-lg flex items-center gap-2'>
                      <GreenCheckIcon width={20} height={20} />
                      <span className='font-bold text-gray-900'>
                        {categoryData.category}
                      </span>
                    </div>
                    <div className='ml-7 space-y-1'>
                      {categoryData.features.map((feature, index) => (
                        <div key={index} className='flex items-center gap-2'>
                          <GreenCheckIcon width={16} height={16} />
                          <span className='text-md font-semibold text-gray-700'>
                            {feature.name}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Organization Plan Card - 30% width */}
        {organizationPlan && (
          <div className='w-[48%] bg-white rounded-lg shadow p-6 flex flex-col'>
            <div className='mb-2'>
              <h4 className='text-md font-semibold text-gray-900'>
                To Upgrade your Current Plan
              </h4>
              <h3 className='text-xl font-bold text-gray-900'>
                {organizationPlan.planName || 'Enterprise'}
              </h3>
              {organizationPlan.validity && (
                <p className='text-sm text-gray-500 mt-1'>
                  {organizationPlan.validity === 'Both'
                    ? 'Billed Monthly & Annually'
                    : organizationPlan.validity === 'Monthly'
                      ? 'Billed Monthly'
                      : 'Billed Annually'}
                </p>
              )}
            </div>

            <h2 className='text-2xl font-bold text-gray-900 mb-4'>
              {organizationPlanHeading}
            </h2>

            <button
              onClick={handleGetQuote}
              className='w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors mb-4'
            >
              Get Quote
            </button>

            {descriptionAfterQuote && (
              <div className='flex-1 max-h-96 overflow-y-auto pr-2 scrollbar-no-arrows'>
                <div
                  className='prose prose-sm max-w-none text-gray-700'
                  dangerouslySetInnerHTML={{ __html: descriptionAfterQuote }}
                />
              </div>
            )}
          </div>
        )}
      </div>

      <style>{`
        .scrollbar-no-arrows::-webkit-scrollbar {
          width: 6px;
        }
        .scrollbar-no-arrows::-webkit-scrollbar-track {
          background: transparent;
        }
        .scrollbar-no-arrows::-webkit-scrollbar-thumb {
          background: #9ca3af;
          border-radius: 10px;
        }
        .scrollbar-no-arrows::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }
        .scrollbar-no-arrows::-webkit-scrollbar-button {
          display: none;
          height: 0;
          width: 0;
        }
        .scrollbar-no-arrows::-webkit-scrollbar-button:start:decrement,
        .scrollbar-no-arrows::-webkit-scrollbar-button:end:increment {
          display: none;
        }
        .prose ul {
          list-style-type: disc;
          padding-left: 1.5rem;
          margin: 0.5rem 0;
        }
        .prose li {
          margin: 0.25rem 0;
        }
        .prose p {
          margin: 0.5rem 0;
        }
        .prose h2, .prose h3 {
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          font-weight: 600;
        }
        .prose strong {
          font-weight: 700;
        }
      `}</style>
    </div>
  );
};

export default SubscriptionList;
