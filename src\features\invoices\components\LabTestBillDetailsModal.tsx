import { format } from 'date-fns';
import React from 'react';

import LoadingSpinner from '../../../components/Common/LoadingSpinner';
import { capitalizeFirstLetter } from '../../../utils/inputUtils';

interface LabTestBillDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  invoice: any;
  isLoading?: boolean;
}

const LabTestBillDetailsModal: React.FC<LabTestBillDetailsModalProps> = ({
  isOpen,
  onClose,
  invoice,
  isLoading = false,
}) => {
  if (!isOpen) return null;

  const { billDetails, patient, doctor } = invoice || {};
  const totalAmount = billDetails?.totalAmount || 0;

  return (
    <div className='fixed inset-0 z-50 overflow-y-auto'>
      <div className='flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0'>
        <div className='fixed inset-0 transition-opacity' onClick={onClose}>
          <div className='absolute inset-0 bg-gray-500 opacity-75'></div>
        </div>

        <span className='hidden sm:inline-block sm:align-middle sm:h-screen'>
          &#8203;
        </span>

        <div className='inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full'>
          <div className='bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4'>
            {isLoading ? (
              <div className='flex justify-center items-center p-12'>
                <LoadingSpinner size='lg' />
              </div>
            ) : invoice ? (
              <div className='space-y-4'>
                <div className='flex justify-between items-center mb-4 border-b'>
                  <h3 className='text-lg leading-6 font-medium text-gray-900 pb-3'>
                    Lab Master-Bill Details
                  </h3>
                  <button
                    onClick={onClose}
                    className='text-gray-400 hover:text-gray-500 focus:outline-none'
                  >
                    <span className='sr-only'>Close</span>
                    <svg
                      className='h-6 w-6'
                      fill='none'
                      viewBox='0 0 24 24'
                      stroke='currentColor'
                    >
                      <path
                        strokeLinecap='round'
                        strokeLinejoin='round'
                        strokeWidth={2}
                        d='M6 18L18 6M6 6l12 12'
                      />
                    </svg>
                  </button>
                </div>
                <div className='flex flex-wrap items-center justify-between text-sm text-gray-900 gap-4 py-2 border-b'>
                  <div className='flex items-center gap-1'>
                    <span className='font-medium text-gray-700'>
                      Patient Name :
                    </span>
                    <span>
                      {patient?.name || 'N/A'}
                      {patient?.sex
                        ? ` (${patient.sex.charAt(0).toUpperCase()})`
                        : ''}
                    </span>
                  </div>

                  <div className='flex items-center gap-1'>
                    <span className='font-medium text-gray-700'>
                      Patient ID :
                    </span>
                    <span>{patient?.id || 'N/A'}</span>
                  </div>

                  <div className='flex items-center gap-1'>
                    <span className='font-medium text-gray-700'>Age :</span>
                    <span>{patient?.age ? `${patient.age} Y` : 'N/A'}</span>
                  </div>

                  <div className='flex items-center gap-1'>
                    <span className='font-medium text-gray-700'>Date :</span>
                    <span>
                      {format(
                        new Date(billDetails?.billedAt || invoice.createdAt),
                        'dd MMM yyyy'
                      )}
                    </span>
                  </div>

                  <div className='flex items-center gap-1'>
                    <span className='font-medium text-gray-700'>
                      Doctor's Name :
                    </span>
                    <span>{capitalizeFirstLetter(doctor?.name) || 'N/A'}</span>
                  </div>
                </div>

                <div className='mt-3'>
                  <table className='min-w-full border border-gray-300 rounded-md overflow-hidden'>
                    <thead className='bg-gray-600 text-white'>
                      <tr>
                        <th className='px-4 py-2 text-xs font-medium text-center border-r border-gray-400'>
                          Department
                        </th>
                        <th className='px-4 py-2 text-xs font-medium text-center border-r border-gray-400'>
                          Test Name
                        </th>
                        <th className='px-4 py-2 text-xs font-medium text-center'>
                          Amount (₹)
                        </th>
                      </tr>
                    </thead>

                    <tbody>
                      {billDetails?.tests?.map((test: any, index: number) => (
                        <tr
                          key={index}
                          className='border border-gray-300'
                          style={{ backgroundColor: '#DAE1E7' }}
                        >
                          <td className='px-4 py-2 text-sm text-center border-r border-gray-300'>
                            {test.department}
                          </td>
                          <td className='px-4 py-2 text-sm text-center border-r border-gray-300'>
                            {test.testName}
                          </td>
                          <td className='px-4 py-2 text-sm text-center'>
                            {test.amount}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className='pt-5 text-xs'>
                  {billDetails?.paymentMethod && (
                    <p>Payment made through {billDetails.paymentMethod}</p>
                  )}
                  {(billDetails?.billedBy || invoice.created_by_name) && (
                    <p>
                      Billed by{' '}
                      {capitalizeFirstLetter(
                        billDetails.billedBy || invoice.created_by_name
                      )}{' '}
                      at{' '}
                      {format(
                        new Date(billDetails?.billedAt || invoice.createdAt),
                        ' hh:mm a'
                      )}
                    </p>
                  )}
                </div>

                <div className='mt-6 border-t border-gray-200 flex justify-end'>
                  <span className='font-medium text-gray-900'>
                    Total Amount: ₹{totalAmount.toFixed(2)}
                  </span>
                </div>

                <div className='border-t border-gray-200 px-6 py-4'>
                  <div className='text-center text-sm text-gray-500'>
                    <div className='powered-by flex items-center justify-center gap-2'>
                      <span>Powered By</span>
                      <img
                        src='/images/Vector.png'
                        alt='Arca Logo'
                        className='arca-logo'
                        onError={(e) => {
                          const target = e.currentTarget;
                          target.style.display = 'none';
                          const textSpan =
                            target.nextElementSibling as HTMLElement;
                          if (textSpan) {
                            textSpan.classList.remove('hidden');
                          }
                        }}
                      />
                      <span className='arca-logo-text hidden'>ARCA</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className='text-center p-8 text-gray-500'>
                Unable to load lab test details
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LabTestBillDetailsModal;
