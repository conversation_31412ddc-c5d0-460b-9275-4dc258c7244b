# Deployment Guide

This document outlines the deployment process and environments for the ArcaAI Admin application.

## Deployment Environments

### Production Environment

- **Platform**: Azure Static Web Apps
- **Trigger**: Merge to `production` branch
- **Automation**: GitHub Actions workflow
- **Workflow File**: `.github/workflows/azure-static-web-apps-lively-rock-04162e600.yml`

### QA Environment

- **Platform**: Vercel
- **Trigger**: Merge to `main` branch
- **Automation**: Automatic deployment via Vercel integration

## Deployment Flow

```mermaid
graph LR
    A[Feature Branch] --> B[Pull Request to main]
    B --> C[Merge to main]
    C --> D[Deploy to QA - Vercel]
    D --> E[QA Testing]
    E --> F[Pull Request to production]
    F --> G[Merge to production]
    G --> H[Deploy to Production - Azure]
```

## Branch Strategy

### Main Branch (`main`)

- Development integration branch
- Automatically deploys to QA environment on Vercel
- Used for testing and validation before production release

### Production Branch (`production`)

- Production-ready code
- Automatically deploys to Azure Static Web Apps
- Only merge after thorough QA testing

## Deployment Process

### To QA Environment

1. Create feature branch from `main`
2. Develop and test locally
3. Create pull request to `main` branch
4. Code review and approval
5. Merge to `main` branch
6. Automatic deployment to Vercel QA environment

### To Production Environment

1. Ensure QA testing is complete on `main` branch
2. Create pull request from `main` to `production` branch
3. Final review and approval
4. Merge to `production` branch
5. GitHub Actions triggers Azure Static Web Apps deployment

## Configuration Files

### Azure Static Web Apps

- Configuration: `staticwebapp.config.json`
- Workflow: `.github/workflows/azure-static-web-apps-lively-rock-04162e600.yml`

### Vercel

- Configuration: `vercel.json`
- Automatic deployment via Vercel GitHub integration

## Environment Variables

Ensure the following environment variables are configured in each deployment environment:

- Production (Azure): Configure in Azure Static Web Apps settings
- QA (Vercel): Configure in Vercel project settings

Reference `.env.example` for required environment variables.

## Rollback Strategy

### Production Rollback

1. Access Azure Static Web Apps portal
2. Navigate to deployment history
3. Select previous stable deployment
4. Activate rollback

### QA Rollback

1. Access Vercel dashboard
2. Navigate to deployments
3. Select previous deployment
4. Promote to current

## Monitoring and Logs

### Production

- Azure Static Web Apps provides built-in monitoring
- Access logs through Azure portal

### QA

- Vercel provides deployment logs and analytics
- Access through Vercel dashboard

## Troubleshooting

### Common Issues

1. **Build Failures**: Check build logs in respective platforms
2. **Environment Variables**: Verify all required variables are set
3. **Branch Protection**: Ensure proper branch protection rules are configured

### Support Contacts

- Azure Issues: Check Azure Static Web Apps documentation
- Vercel Issues: Check Vercel documentation
- GitHub Actions: Review workflow logs in GitHub Actions tab
