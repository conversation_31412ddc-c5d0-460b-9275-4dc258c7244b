import { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { AppDispatch, RootState } from '../../../store';
import {
  clearFilters,
  fetchInvoices,
  setBillTypeFilter,
  setDateRange,
  setGenderFilter,
  setPage,
  setSearchText,
} from '../../../store/features/invoices';
import { InvoiceParams } from '../../../store/features/invoices/invoice.service';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';

export const useInvoices = (organizationId?: string | null) => {
  const dispatch = useDispatch<AppDispatch>();
  const searchDebounceTimer = useRef<NodeJS.Timeout | null>(null);
  const previousParamsRef = useRef<string>('');
  const isFetchingRef = useRef<boolean>(false);
  const paramsRef = useRef<{
    organizationId?: string | null;
    page: number;
    limit: number;
    searchText: string;
    billTypeFilter: string;
    genderFilter: string;
    fromDate: string;
    toDate: string;
  }>({
    organizationId: null,
    page: 1,
    limit: 20,
    searchText: '',
    billTypeFilter: 'all',
    genderFilter: 'all',
    fromDate: '',
    toDate: '',
  });

  const {
    invoices,
    loading,
    total,
    page,
    limit,
    searchText,
    billTypeFilter,
    genderFilter,
    fromDate,
    toDate,
  } = useSelector((state: RootState) => state.invoices);

  const currentOrganizationId = organizationId ?? getCurrentOrganizationId();

  const fetchInvoicesData = useCallback(() => {
    if (!currentOrganizationId) return;

    const currentParams = {
      organizationId: currentOrganizationId,
      page,
      limit,
      searchText,
      billTypeFilter,
      genderFilter,
      fromDate,
      toDate,
    };

    const paramsKey = JSON.stringify(currentParams);

    if (previousParamsRef.current === paramsKey) {
      return;
    }

    if (isFetchingRef.current) {
      return;
    }

    previousParamsRef.current = paramsKey;
    paramsRef.current = currentParams;
    isFetchingRef.current = true;

    const params: InvoiceParams = {
      organizationId: currentOrganizationId,
      page,
      limit,
      ...(searchText ? { search: searchText } : {}),
      ...(billTypeFilter !== 'all' ? { type: billTypeFilter } : {}),
      ...(genderFilter !== 'all' ? { gender: genderFilter } : {}),
      ...(fromDate ? { startDate: fromDate } : {}),
      ...(toDate ? { endDate: toDate } : {}),
    };

    dispatch(fetchInvoices(params)).finally(() => {
      isFetchingRef.current = false;
    });
  }, [
    dispatch,
    currentOrganizationId,
    page,
    limit,
    searchText,
    billTypeFilter,
    genderFilter,
    fromDate,
    toDate,
  ]);

  const search = useCallback(
    (value: string) => {
      if (searchDebounceTimer.current) {
        clearTimeout(searchDebounceTimer.current);
      }

      searchDebounceTimer.current = setTimeout(() => {
        dispatch(setSearchText(value));
      }, 400);
    },
    [dispatch]
  );

  const filterByBillType = useCallback(
    (value: string) => {
      dispatch(setBillTypeFilter(value));
    },
    [dispatch]
  );

  const filterByGender = useCallback(
    (value: string) => {
      dispatch(setGenderFilter(value));
    },
    [dispatch]
  );

  const filterByDateRange = useCallback(
    (from: string, to: string) => {
      dispatch(
        setDateRange({
          fromDate: from,
          toDate: to,
        })
      );
    },
    [dispatch]
  );

  const changePage = useCallback(
    (newPage: number) => {
      dispatch(setPage(newPage));
    },
    [dispatch]
  );

  const clearAllFilters = useCallback(() => {
    dispatch(clearFilters());
  }, [dispatch]);

  useEffect(() => {
    fetchInvoicesData();
  }, [fetchInvoicesData]);

  useEffect(() => {
    return () => {
      if (searchDebounceTimer.current) {
        clearTimeout(searchDebounceTimer.current);
      }
    };
  }, []);

  return {
    invoices,
    loading,
    total,
    page,
    limit,
    searchText,
    billTypeFilter,
    genderFilter,
    fromDate,
    toDate,
    search,
    filterByBillType,
    filterByGender,
    filterByDateRange,
    changePage,
    clearFilters: clearAllFilters,
  };
};
