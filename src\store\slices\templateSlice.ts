import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';

import { generateMockApiResponse, mockTemplates } from '../../data/mockData';
import { PaginatedResponse, TableFilters, Template } from '../../types';

interface TemplatesState {
  templates: Template[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: TemplatesState = {
  templates: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

// Mock API calls
const fetchTemplatesFromApi = async (
  filters: TableFilters & {
    organizationId: string;
    page: number;
    limit: number;
  }
) => {
  const { organizationId, page, limit, search, status } = filters;
  let filteredTemplates = mockTemplates.filter(
    (t: Template) => t.organizationId === organizationId
  );

  if (search) {
    const lowercasedSearch = search.toLowerCase();
    filteredTemplates = filteredTemplates.filter(
      (t: Template) =>
        t.name.toLowerCase().includes(lowercasedSearch) ||
        t.type.toLowerCase().includes(lowercasedSearch)
    );
  }

  if (status) {
    filteredTemplates = filteredTemplates.filter(
      (t: Template) => t.status === status
    );
  }

  const total = filteredTemplates.length;
  const paginatedData = filteredTemplates.slice(
    (page - 1) * limit,
    page * limit
  );

  return generateMockApiResponse<PaginatedResponse<Template>>({
    data: paginatedData,
    total,
    page,
    limit,
  });
};

const createTemplateInApi = async (
  data: Omit<Template, 'id' | 'createdAt'>
) => {
  const newTemplate: Template = {
    id: `template-${Date.now()}`,
    ...data,
    createdAt: new Date(),
  };
  mockTemplates.push(newTemplate);
  return generateMockApiResponse(newTemplate);
};

const updateTemplateInApi = async (
  id: string,
  data: Partial<Omit<Template, 'id' | 'createdAt'>>
) => {
  const index = mockTemplates.findIndex((t: Template) => t.id === id);
  if (index !== -1) {
    mockTemplates[index] = { ...mockTemplates[index], ...data } as Template;
    return generateMockApiResponse(mockTemplates[index]);
  }
  throw new Error('Template not found');
};

// Async Thunks
export const fetchTemplates = createAsyncThunk(
  'templates/fetchTemplates',
  async (
    filters: TableFilters & {
      organizationId: string;
      page: number;
      limit: number;
    }
  ) => {
    const response = await fetchTemplatesFromApi(filters);
    return response.data;
  }
);

export const createTemplate = createAsyncThunk(
  'templates/createTemplate',
  async (data: Omit<Template, 'id' | 'createdAt'>) => {
    const response = await createTemplateInApi(data);
    return response.data;
  }
);

export const updateTemplate = createAsyncThunk(
  'templates/updateTemplate',
  async ({
    id,
    data,
  }: {
    id: string;
    data: Partial<Omit<Template, 'id' | 'createdAt'>>;
  }) => {
    const response = await updateTemplateInApi(id, data);
    return response.data;
  }
);

const templateSlice = createSlice({
  name: 'templates',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchTemplates.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchTemplates.fulfilled,
        (state, action: PayloadAction<PaginatedResponse<Template>>) => {
          state.loading = false;
          state.templates = action.payload.data;
          state.total = action.payload.total;
          state.page = action.payload.page;
          state.limit = action.payload.limit;
        }
      )
      .addCase(fetchTemplates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch templates';
      })
      .addCase(createTemplate.fulfilled, (state) => {
        // To reflect the new item, we could either re-fetch the list or add it manually.
        // For simplicity, we'll just increment total and let the next fetch show it.
        state.total += 1;
      })
      .addCase(
        updateTemplate.fulfilled,
        (state, action: PayloadAction<Template>) => {
          const index = state.templates.findIndex(
            (t) => t.id === action.payload.id
          );
          if (index !== -1) {
            state.templates[index] = action.payload;
          }
        }
      );
  },
});

export default templateSlice.reducer;
