import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { v4 as uuidv4 } from 'uuid';

import { generateMockApiResponse, mockLanguages } from '../../data/mockData';
import { Language, PaginatedResponse } from '../../types';

interface LanguageState {
  languages: Language[];
  loading: boolean;
  error: string | null;
  total: number;
  page: number;
  limit: number;
}

const initialState: LanguageState = {
  languages: [],
  loading: false,
  error: null,
  total: 0,
  page: 1,
  limit: 10,
};

export const fetchLanguages = createAsyncThunk(
  'languages/fetchLanguages',
  async (params: {
    organizationId: string;
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) => {
    let filteredLanguages = mockLanguages.filter(
      (lang) => lang.organizationId === params.organizationId
    );

    if (params.search) {
      filteredLanguages = filteredLanguages.filter(
        (lang) =>
          lang.name.toLowerCase().includes(params.search!.toLowerCase()) ||
          lang.isoCode.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    if (params.status) {
      filteredLanguages = filteredLanguages.filter(
        (lang) => lang.status === params.status
      );
    }

    const page = params.page || 1;
    const limit = params.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredLanguages.slice(startIndex, endIndex);

    const response: PaginatedResponse<Language> = {
      data: paginatedData,
      total: filteredLanguages.length,
      page,
      limit,
    };

    return await generateMockApiResponse(response, 500);
  }
);

export const createLanguage = createAsyncThunk(
  'languages/createLanguage',
  async (languageData: Omit<Language, 'id' | 'createdAt'>) => {
    const newLanguage: Language = {
      id: uuidv4(),
      ...languageData,
      createdAt: new Date(),
    };
    mockLanguages.unshift(newLanguage);
    return await generateMockApiResponse(newLanguage, 800);
  }
);

export const updateLanguage = createAsyncThunk(
  'languages/updateLanguage',
  async ({ id, data }: { id: string; data: Partial<Language> }) => {
    const index = mockLanguages.findIndex((lang) => lang.id === id);
    if (index === -1) {
      throw new Error('Language not found');
    }
    const updatedLanguage = { ...mockLanguages[index], ...data } as Language;
    mockLanguages[index] = updatedLanguage;
    return await generateMockApiResponse(updatedLanguage, 800);
  }
);

const languageSlice = createSlice({
  name: 'languages',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchLanguages.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLanguages.fulfilled, (state, action) => {
        state.loading = false;
        state.languages = action.payload.data.data;
        state.total = action.payload.data.total;
        state.page = action.payload.data.page;
        state.limit = action.payload.data.limit;
      })
      .addCase(fetchLanguages.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch languages';
      })
      .addCase(createLanguage.fulfilled, (state, action) => {
        state.languages.unshift(action.payload.data);
        state.total += 1;
      })
      .addCase(updateLanguage.fulfilled, (state, action) => {
        const index = state.languages.findIndex(
          (lang) => lang.id === action.payload.data.id
        );
        if (index !== -1) {
          state.languages[index] = action.payload.data;
        }
      });
  },
});

export default languageSlice.reducer;
