import {
  PERMISSIONS_ENDPOINT,
  SUBSCRIPTION_FEATURE_ENDPOINT,
  SUBSCRIPTION_ORG_FEATURES_ENDPOINT,
} from '../../../constants/api-endpoints';
import api from '../../../services/api';

export type AssignPermissionsData = {
  roleId: string;
  permissions: string[]; // Array of permission IDs
};

export type GetRolePermissionsParams = {
  roleId: string;
};

/**
 * Assign permissions to a role
 */
const assignPermissions = async (data: AssignPermissionsData) => {
  const response = await api.post(
    `${PERMISSIONS_ENDPOINT}/assign-permissions`,
    {
      roleId: data.roleId,
      permissions: data.permissions,
    }
  );
  return response.data;
};

/**
 * Update permissions for a role (PATCH method)
 */
const updatePermissions = async (data: AssignPermissionsData) => {
  const response = await api.post(`${PERMISSIONS_ENDPOINT}/assign`, {
    roleId: data.roleId,
    permissions: data.permissions,
  });
  return response.data;
};

/**
 * Get permissions assigned to a specific role
 */
const getRolePermissions = async (params: GetRolePermissionsParams) => {
  const response = await api.get(`${PERMISSIONS_ENDPOINT}/api-list`, {
    params: {
      roleId: params.roleId,
    },
  });
  return response.data;
};

/**
 * Get all available features
 */
const getAllFeatures = async () => {
  const response = await api.get(SUBSCRIPTION_FEATURE_ENDPOINT);
  const data = response.data;

  if (Array.isArray(data)) {
    return { features: data, count: data.length };
  } else if (data && typeof data === 'object' && 'features' in data) {
    return data;
  } else {
    return { features: [], count: 0 };
  }
};

/**
 * Get features for a specific organization
 */
const getOrganizationFeatures = async (subscriberId: string) => {
  const response = await api.get(
    `${SUBSCRIPTION_ORG_FEATURES_ENDPOINT}?subscriberId=${subscriberId}`
  );
  return response.data;
};

const permissionService = {
  assignPermissions,
  updatePermissions,
  getRolePermissions,
  getAllFeatures,
  getOrganizationFeatures,
};

export default permissionService;
