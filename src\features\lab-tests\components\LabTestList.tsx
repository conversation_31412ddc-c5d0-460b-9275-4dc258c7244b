import { Download, Plus, Trash2, X } from 'lucide-react';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';

import AppButton from '../../../components/Common/AppButton';
import ConfirmationModal from '../../../components/Common/ConfirmationModal';
import DataTable, { Column } from '../../../components/Common/DataTable';
import Modal from '../../../components/Common/Modal';
import SelectInput from '../../../components/Common/MUISelect';
import SearchBar from '../../../components/Common/SearchBar';
import StatusBadge from '../../../components/Common/StatusBadge';
import Tooltip from '../../../components/Common/Tooltip';
import { getCurrentOrganizationId } from '../../../utils/auth-utils';
import { useLabTests } from '../hooks/useLabTests';
import {
  fetchLabTestDepartments,
  getDepartmentFilterValue,
  LabTestDepartment,
} from '../services/department.service';
import {
  LabTestListItem,
  LabTestListParams,
  UpdateLabTestRequest,
} from '../types/labTest.types';
import AddLabTestForm from './AddLabTestForm';
import { BulkUpdateStatusIndicator } from './BulkUpdateStatusIndicator';
import LabTestForm from './LabTestForm';

interface LabTestListProps {
  organizationName: string;
  organizationId?: string;
}

const LabTestList: React.FC<LabTestListProps> = memo(
  ({ organizationName, organizationId }) => {
    const currentOrgId = organizationId || getCurrentOrganizationId();

    const [showEditModal, setShowEditModal] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingTest, setEditingTest] = useState<LabTestListItem | null>(
      null
    );
    const [departments, setDepartments] = useState<LabTestDepartment[]>([]);
    const [loadingDepartments, setLoadingDepartments] = useState(true);
    const [showBulkUpdateStatus, setShowBulkUpdateStatus] = useState(false);
    const [showConfirmModal, setShowConfirmModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const [isUpdating, setIsUpdating] = useState(false);

    // Track which operation initiated the bulk process
    const [bulkOperationType, setBulkOperationType] = useState<
      'update' | 'remove' | null
    >(null);

    const {
      tests,
      loading,
      creating,
      error: _error,
      updating,
      searchText,
      page,
      limit,
      total,
      departmentFilter,
      isActiveFilter,
      selectedTests,
      isAllSelected,
      totalPages: _totalPages,
      fetchLabTests: _fetchLabTests,
      createLabTest,
      updateLabTests,
      removeLabTests,
      exportLabTests,
      search,
      filterByDepartment,
      selectTest,
      selectAllTests,
      clearSelection: _clearSelection,
      clearMessages: _clearMessages,
      updateTestInList: _updateTestInList,
      hasSelection,
      selectedCount,
      bulkUpdate,
    } = useLabTests(currentOrgId || undefined);

    // Clear selection only when component unmounts, but preserve if bulk operation is in progress
    useEffect(() => {
      return () => {
        // Only clear selection if no bulk operation is in progress
        if (
          !bulkUpdate.isPolling ||
          (bulkUpdate.status?.status !== 'PROCESSING' &&
            bulkUpdate.status?.status !== 'PENDING')
        ) {
          _clearSelection();
        }
      };
    }, [_clearSelection, bulkUpdate.isPolling, bulkUpdate.status?.status]);

    // Check if bulk update is in progress
    const isBulkUpdateInProgress = updating || bulkUpdate.isPolling;

    // Enhanced loading states that persist throughout the entire operation
    const isRemoveInProgress =
      isRemoving || (isBulkUpdateInProgress && bulkOperationType === 'remove');
    const isUpdateInProgress =
      isUpdating || (isBulkUpdateInProgress && bulkOperationType === 'update');

    // Reset operation type when bulk process completes
    useEffect(() => {
      if (!isBulkUpdateInProgress && bulkOperationType) {
        setBulkOperationType(null);
        setIsRemoving(false);
        setIsUpdating(false);
      }
    }, [isBulkUpdateInProgress, bulkOperationType]);

    // Load departments on mount
    React.useEffect(() => {
      const loadDepartments = async () => {
        try {
          setLoadingDepartments(true);
          const depts = await fetchLabTestDepartments();
          setDepartments(depts);
        } catch {
          // Failed to load departments, will use fallback
        } finally {
          setLoadingDepartments(false);
        }
      };

      loadDepartments();
    }, []);

    // Handle inline toggle for isActive - removed since status is now non-clickable

    // Handle save test (call API to update)
    const handleSaveTest = useCallback(
      async (
        testId: string,
        data: {
          displayName?: string | null;
          department: string;
          organizationCost: number;
          isActive: boolean;
        }
      ): Promise<void> => {
        if (!currentOrgId) return;

        try {
          // Prepare API data according to the new payload structure
          const updateData = {
            organizationId: currentOrgId,
            tests: [
              {
                testId,
                isActive: data.isActive,
                price: data.organizationCost,
                displayName: data.displayName || null,
                department: data.department,
              },
            ],
            suppressStatusIndicator: true,
          };

          await updateLabTests(updateData);

          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          // Only include department if not empty
          if (departmentFilter && departmentFilter !== '') {
            params.department = departmentFilter;
          }

          // Type assertion to Partial<LabTestListParams> since we've ensured types are correct
          await _fetchLabTests(params as Partial<LabTestListParams>);
        } catch (error) {
          console.error('Error saving test:', error);
          throw error; // Re-throw to let the form handle the error
        }
      },
      [
        currentOrgId,
        updateLabTests,
        _fetchLabTests,
        searchText,
        departmentFilter,
        isActiveFilter,
        page,
        limit,
      ]
    );

    const handleCreateLabTest = useCallback(
      async (data: {
        testName: string;
        department: string;
        organizationCost: number | null;
        isActive: boolean;
      }) => {
        try {
          const processedData = {
            ...data,
            organizationCost: data.organizationCost ?? 0,
          };
          await createLabTest(processedData);
        } catch (error) {
          console.error('Error creating lab test:', error);
          throw error; // Re-throw to let the form handle the error
        }
      },
      [createLabTest]
    );

    // Handle bulk operations - directly call API
    const handleBulkUpdate = useCallback(async () => {
      if (!currentOrgId) return;

      setIsUpdating(true);
      setBulkOperationType('update');

      // For select all, we'll let the BulkUpdateStatusIndicator handle the polling
      // For individual selections, we'll refresh the list immediately after update
      const isSelectAllOperation = isAllSelected;

      try {
        const updateData: UpdateLabTestRequest = {
          organizationId: currentOrgId,
          tests: isSelectAllOperation
            ? []
            : selectedTests.map((testId: string) => ({
                testId,
                isActive: true,
                price: 0,
                department: 'OTHERS',
              })),
          selectAll: isSelectAllOperation,
          department: isSelectAllOperation
            ? getDepartmentFilterValue(departmentFilter)
            : '',
        };

        // Show status indicator for select all operations
        if (isSelectAllOperation) {
          setShowBulkUpdateStatus(true);
        }

        // Perform the update
        await updateLabTests({
          ...updateData,
          // Only suppress status for individual selections
          suppressStatusIndicator: !isSelectAllOperation,
        });

        // For individual selections, refresh the list immediately
        if (!isSelectAllOperation) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter && departmentFilter !== '') {
            params.department = getDepartmentFilterValue(departmentFilter);
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }
      } catch (error) {
        console.error('Error updating lab tests:', error);
        // Error handling is done in the Redux slice
      } finally {
        // Don't reset isUpdating here if it's a select all operation
        // It will be reset when the bulk process completes
        if (!isSelectAllOperation) {
          setIsUpdating(false);
          setBulkOperationType(null);
        }
      }
    }, [
      currentOrgId,
      departmentFilter,
      isActiveFilter,
      isAllSelected,
      selectedTests,
      updateLabTests,
      _clearSelection,
      _fetchLabTests,
      searchText,
      page,
      limit,
    ]);

    // Handle remove operation
    const handleRemove = useCallback(async () => {
      if (!currentOrgId || (!selectedTests.length && !isAllSelected)) {
        return;
      }

      setIsRemoving(true);
      setBulkOperationType('remove');

      if (isAllSelected) {
        setShowBulkUpdateStatus(true);
      }

      try {
        // Call the remove API (no parameters needed, it uses the current selection)
        const result = await removeLabTests();

        // If the removal was successful, refresh the list
        if (
          result &&
          typeof result === 'object' &&
          'success' in result &&
          result.success
        ) {
          const params: Record<string, any> = {
            organizationId: currentOrgId,
            searchText,
            page,
            pageSize: limit,
            isActive:
              isActiveFilter === 'all'
                ? undefined
                : isActiveFilter === 'active',
          };

          if (departmentFilter && departmentFilter !== '') {
            params.department = getDepartmentFilterValue(departmentFilter);
          }

          await _fetchLabTests(params as Partial<LabTestListParams>);
        }

        // Close the confirmation modal
        setShowConfirmModal(false);
      } catch (error) {
        console.error('Error removing lab tests:', error);
      } finally {
        // Don't reset isRemoving here if it's a select all operation
        // It will be reset when the bulk process completes
        if (!isAllSelected) {
          setIsRemoving(false);
          setBulkOperationType(null);
        }
      }
    }, [
      currentOrgId,
      selectedTests,
      isAllSelected,
      departmentFilter,
      isActiveFilter,
      removeLabTests,
      _fetchLabTests,
      searchText,
      page,
      limit,
    ]);

    const confirmRemove = useCallback(() => {
      setShowConfirmModal(true);
    }, [setShowConfirmModal]);

    const cancelRemove = useCallback(() => {
      setShowConfirmModal(false);
    }, [setShowConfirmModal]);

    // Handle mark all tests (across all pages)
    const handleMarkAll = useCallback(() => {
      selectAllTests();
    }, [selectAllTests]);

    // Handle export
    const handleExport = useCallback(() => {
      exportLabTests();
    }, [exportLabTests]);

    // Handle search
    const handleSearchChange = useCallback(
      (searchTerm: string) => {
        search(searchTerm);
      },
      [search]
    );

    // Handle department filter
    const handleDepartmentFilterChange = useCallback(
      (department: string) => {
        // Store the original selected value in Redux, not the transformed value
        filterByDepartment(department);
      },
      [filterByDepartment]
    );

    // Handle status filter
    // const handleStatusFilterChange = useCallback(
    //   (status: string) => {
    //     filterByStatus(status);
    //   },
    //   [filterByStatus]
    // );

    // Pagination handler for new API
    const handlePageChange = useCallback(
      (newPage: number) => {
        // Always use the page number directly for navigation
        _fetchLabTests({ page: newPage });
      },
      [_fetchLabTests]
    );

    // Calculate counts of active and inactive selected items
    const { activeCount, inactiveCount } = useMemo(() => {
      if (isAllSelected) {
        // If all are selected, we don't know the exact counts across all pages without fetching all.
        // We use selectedCount (which is 'total') with a fallback to the current tests length
        // to ensure the buttons are enabled if there's data.
        const effectiveCount = selectedCount || tests.length;
        return { activeCount: effectiveCount, inactiveCount: effectiveCount };
      }

      // Calculate counts based on current selection for individual pages
      const active = tests.filter(
        (test: LabTestListItem) =>
          selectedTests.includes(test.testId) && test.isActive
      ).length;
      const inactive = tests.filter(
        (test: LabTestListItem) =>
          selectedTests.includes(test.testId) && !test.isActive
      ).length;

      // Also consider selected tests that might not be on the current page
      // If we have selected tests but none are on the current page, we should still
      // show at least 1 in the counts if we want the buttons to stay enabled.
      // However, the most reliable way when we don't have all data is to use the length
      // of selectedTests as a proxy for the total selected.
      const totalSelected = selectedTests.length;

      return {
        activeCount: active || (totalSelected > 0 ? totalSelected : 0),
        inactiveCount: inactive || (totalSelected > 0 ? totalSelected : 0),
      };
    }, [selectedTests, tests, isAllSelected, selectedCount]);

    // Handle selection of a single test
    const handleSelectTest = useCallback(
      (testId: string, selected: boolean) => {
        // The selectTest action will handle adding/removing the test ID
        // based on the selected parameter
        if (selected) {
          selectTest(testId);
        } else {
          // If unchecking, we need to remove the test ID from the selection
          selectTest(testId); // The Redux action will handle toggling
        }
      },
      [selectTest]
    );

    // Removed auto-selection of active tests to allow manual selection

    // Memoized columns definition
    const columns: Column<LabTestListItem>[] = useMemo(
      () => [
        {
          key: 'testName',
          label: 'Test Name',
          render: (_, test) => (
            <div style={{ maxWidth: 200 }}>
              <div className='text-sm font-medium text-gray-900'>
                {test.testName || ''}
              </div>
              <Tooltip content={test.shortName || ''}>
                <div
                  className='text-xs text-gray-500 cursor-help'
                  style={{
                    maxWidth: 250,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    display: 'block',
                  }}
                >
                  {test.shortName || ''}
                </div>
              </Tooltip>
            </div>
          ),
          width: '300px',
        },
        {
          key: 'displayName',
          label: 'Display Name',
          render: (_, test) => (
            <div className='text-sm text-gray-900'>
              {test.displayName || '-'}
            </div>
          ),
          width: '300px',
        },
        {
          key: 'defaultCost',
          label: 'Cost',
          render: (_, test) => (
            <span className='text-sm font-medium text-gray-900'>
              ₹{(test.defaultCost || 0).toFixed(2)}
            </span>
          ),
          width: '180px',
        },
        {
          key: 'organizationPrice',
          label: 'Organization Price',
          render: (_, test) => (
            <span className='text-sm font-medium text-blue-600'>
              {test.organizationPrice !== undefined &&
              test.organizationPrice !== null
                ? `₹${test.organizationPrice.toFixed(2)}`
                : '-'}
            </span>
          ),
        },
        {
          key: 'departments',
          label: 'Department',
          render: (_, test) => (
            <div className='flex flex-wrap gap-1'>
              {test.class ? (
                <span className='inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                  {test.class}
                </span>
              ) : (
                <span className='text-sm text-gray-500'>None</span>
              )}
            </div>
          ),
        },
        {
          key: 'isActive',
          label: 'Status',
          render: (_, test) => (
            <StatusBadge
              status={test.isActive ? 'active' : 'inactive'}
              size='sm'
            />
          ),
        },
      ],
      [
        selectedTests,
        handleSelectTest,
        handleMarkAll,
        isAllSelected,
        tests.length,
      ]
    );

    return (
      <div className='space-y-6'>
        <BulkUpdateStatusIndicator show={showBulkUpdateStatus} />

        {/* Confirmation Modal */}
        <ConfirmationModal
          isOpen={showConfirmModal}
          onClose={cancelRemove}
          onConfirm={handleRemove}
          title='Confirm Removal'
          message={
            isAllSelected
              ? 'Are you sure you want to remove all selected lab tests? This action cannot be undone.'
              : `Are you sure you want to remove ${selectedCount} selected lab test(s)?`
          }
          confirmText='Remove'
          cancelText='Cancel'
          type='danger'
          loading={isRemoveInProgress}
        />

        <div className='flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4'>
          <div>
            <h1 className='text-2xl font-bold text-gray-900'>Lab Tests</h1>
            <p className='mt-1 text-sm text-gray-500'>
              Manage lab tests for {organizationName}
            </p>
          </div>
          <div className='flex items-center space-x-3'>
            <AppButton
              onClick={() => setShowAddModal(true)}
              kind='primary'
              disabled={loading}
            >
              <Plus className='w-4 h-4 mr-2' /> New Test
            </AppButton>
            <SearchBar
              value={searchText}
              onChange={handleSearchChange}
              placeholder='Search lab tests...'
              size='md'
            />
            {hasSelection && (
              <>
                <AppButton
                  onClick={handleBulkUpdate}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isUpdateInProgress ||
                    isRemoveInProgress ||
                    inactiveCount === 0
                  }
                  kind='primary'
                  startIcon={
                    isUpdateInProgress ? (
                      <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                    ) : null
                  }
                >
                  {isUpdateInProgress
                    ? `Updating... `
                    : `Bulk Update${inactiveCount > 0 ? ` (${inactiveCount})` : ''}`}
                </AppButton>
                <AppButton
                  onClick={confirmRemove}
                  disabled={
                    !hasSelection ||
                    loading ||
                    isRemoveInProgress ||
                    isUpdateInProgress ||
                    activeCount === 0
                  }
                  kind='primary'
                  startIcon={
                    isRemoveInProgress ? (
                      <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
                    ) : (
                      <Trash2 className='w-4 h-4' />
                    )
                  }
                  style={{
                    backgroundColor:
                      !hasSelection ||
                      loading ||
                      isRemoveInProgress ||
                      isUpdateInProgress ||
                      activeCount === 0
                        ? '#D3D3D3'
                        : '#dc2626',
                  }}
                >
                  {isRemoveInProgress
                    ? `Removing... `
                    : `Remove${activeCount > 0 ? ` (${activeCount})` : ''}`}
                </AppButton>
              </>
            )}

            {/* Temporarily hidden Import Excel button */}
            {/* eslint-disable-next-line no-constant-binary-expression */}
            {false && (
              <AppButton
                onClick={handleExport}
                disabled={loading}
                kind='secondary'
                startIcon={<Download className='w-4 h-4' />}
                size='small'
              >
                Import Excel
              </AppButton>
            )}
          </div>
        </div>

        {/* Data Table */}
        <DataTable<LabTestListItem>
          columns={columns}
          data={tests}
          loading={loading}
          selectedIds={selectedTests}
          isAllSelected={isAllSelected || isBulkUpdateInProgress}
          disableSelectAll={isBulkUpdateInProgress}
          onSelectAll={selectAllTests}
          onSelectOne={handleSelectTest}
          searchFilters={
            <div className='flex items-center space-x-3'>
              <div className='relative'>
                <SelectInput
                  value={loadingDepartments ? '' : departmentFilter || ''}
                  onChange={(e) => handleDepartmentFilterChange(e.target.value)}
                  disabled={loadingDepartments}
                  placeholder='Filter by Department'
                  options={
                    loadingDepartments
                      ? [{ label: 'Loading departments...', value: '' }]
                      : departments.map((dept) => ({
                          label: dept.label,
                          value: dept.value,
                        }))
                  }
                  size='small'
                  sx={{
                    minWidth: 220,
                    '& .MuiOutlinedInput-root': {
                      paddingRight: departmentFilter ? '36px' : '14px',
                    },
                  }}
                />
                {departmentFilter && !loadingDepartments && (
                  <button
                    type='button'
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDepartmentFilterChange('');
                    }}
                    className='absolute right-9 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-0.5 rounded-full hover:bg-gray-100 flex items-center justify-center z-10'
                    aria-label='Clear filter'
                  >
                    <X className='w-3 h-3' />
                  </button>
                )}
              </div>
            </div>
          }
          pagination={{
            total,
            page,
            limit,
            onPageChange: handlePageChange,
          }}
          onEdit={(test) => {
            setEditingTest(test);
            setShowEditModal(true);
          }}
        />

        {/* Edit Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => !updating && setShowEditModal(false)}
          title='Edit Lab Test'
          size='md'
        >
          <div className='px-4 pb-4'>
            <LabTestForm
              test={editingTest}
              onSubmit={handleSaveTest}
              onSuccess={() => {
                setShowEditModal(false);
                setEditingTest(null);
              }}
              onClose={() => !updating && setShowEditModal(false)}
              updating={updating}
            />
          </div>
        </Modal>

        {/* Add Lab Test Modal */}
        <Modal
          isOpen={showAddModal}
          onClose={() => !creating && setShowAddModal(false)}
          title='Add Lab Test'
          size='md'
        >
          <div className='px-4 pb-4'>
            <AddLabTestForm
              onSubmit={handleCreateLabTest}
              onSuccess={() => {
                setShowAddModal(false);
              }}
              onClose={() => !creating && setShowAddModal(false)}
              submitting={creating}
            />
          </div>
        </Modal>
      </div>
    );
  }
);

LabTestList.displayName = 'LabTestList';

export default LabTestList;
