import {
  AuditLog,
  Bank,
  Branch,
  Department,
  Doctor,
  Language,
  Template,
  VitalType,
} from '../types';

// Mock Departments
export const mockDepartments: Department[] = [
  {
    id: 'dept-1',
    name: 'Cardiology',
    description: 'Heart and cardiovascular care',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'dept-2',
    name: 'Neurology',
    description: 'Brain and nervous system care',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'dept-3',
    name: 'Orthopedics',
    description: 'Bone and joint care',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-20'),
  },
  {
    id: 'dept-4',
    name: 'Pediatrics',
    description: 'Child healthcare',
    status: 'inactive',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-20'),
  },
];

// Mock Branches
export const mockBranches: Branch[] = [
  {
    id: 'branch-1',
    name: 'Main Branch',
    address: '123 Healthcare Ave, Mumbai, Maharashtra 400001',
    contactPhone: '+91-22-********',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'branch-2',
    name: 'Satellite Branch',
    address: '789 Medical Street, Mumbai, Maharashtra 400002',
    contactPhone: '+91-22-********',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-03-01'),
  },
];

// Mock Banks
export const mockBanks: Bank[] = [
  {
    id: 'bank-1',
    bankName: 'HDFC Bank',
    accountNumber: '50100********9',
    ifscCode: 'HDFC0001234',
    branchName: 'Bandra West Branch',
    accountHolderName: 'Metro General Hospital',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'bank-2',
    bankName: 'ICICI Bank',
    accountNumber: '**************',
    ifscCode: 'ICIC0001234',
    branchName: 'Andheri East Branch',
    accountHolderName: 'Metro General Hospital',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-02-01'),
  },
];

// Mock Languages
export const mockLanguages: Language[] = [
  {
    id: 'lang-1',
    name: 'English',
    isoCode: 'en',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'lang-2',
    name: 'Hindi',
    isoCode: 'hi',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'lang-3',
    name: 'Marathi',
    isoCode: 'mr',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
];

// Mock Vital Types
export const mockVitalTypes: VitalType[] = [
  {
    id: 'vital-1',
    name: 'Blood Pressure',
    unit: 'mmHg',
    dataType: 'numeric-range',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'vital-2',
    name: 'Heart Rate',
    unit: 'bpm',
    dataType: 'numeric',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'vital-3',
    name: 'Temperature',
    unit: '°F',
    dataType: 'numeric',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'vital-4',
    name: 'Weight',
    unit: 'kg',
    dataType: 'numeric',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-01-15'),
  },
];

// Mock Doctors
export const mockDoctors: Doctor[] = [
  {
    id: 'doc-1',
    firstName: 'Dr. Anjali',
    lastName: 'Patel',
    specialty: 'Cardiology',
    designation: 'Senior Consultant',
    email: '<EMAIL>',
    contactPhone: '+91-9********2',
    departmentId: 'dept-1',
    branchIds: ['branch-1'],
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'doc-2',
    firstName: 'Dr. Vikram',
    lastName: 'Singh',
    specialty: 'Neurology',
    designation: 'Consultant',
    email: '<EMAIL>',
    contactPhone: '+91-9********4',
    departmentId: 'dept-2',
    branchIds: ['branch-1', 'branch-2'],
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-02-15'),
  },
  {
    id: 'doc-3',
    firstName: 'Dr. Sunita',
    lastName: 'Rao',
    specialty: 'Orthopedics',
    designation: 'Associate Consultant',
    email: '<EMAIL>',
    contactPhone: '+91-**********',
    departmentId: 'dept-3',
    branchIds: ['branch-2'],
    status: 'inactive',
    organizationId: 'org-1',
    createdAt: new Date('2024-03-01'),
  },
];

// Mock Audit Logs
const userNames = [
  'Super Admin',
  'Rajesh Sharma',
  'Dr. Anjali Patel',
  'Nurse Rita',
];

const actionTypes = [
  'USER_CREATED',
  'PATIENT_UPDATED',
  'CONSULTATION_FINALIZED',
  'ROLE_MODIFIED',
];

const entityTypes = ['User', 'Patient', 'Consultation', 'Role'];

const detailsArray = [
  'New user account created',
  'Patient contact information updated',
  'Consultation record finalized',
  'Role permissions modified',
];

export const mockAuditLogs: AuditLog[] = Array.from(
  { length: 50 },
  (_, index) => ({
    id: `audit-${index + 1}`,
    timestamp: new Date(
      2024,
      5,
      10 - Math.floor(index / 10),
      9 + (index % 12),
      index % 60
    ),
    userId: `user-${(index % 4) + 1}`,
    userName: userNames[index % 4] || 'Unknown User',
    actionType: actionTypes[index % 4] || 'UNKNOWN_ACTION',
    entityType: entityTypes[index % 4] || 'Unknown',
    entityId: `entity-${index + 1}`,
    details: detailsArray[index % 4] || 'No details available',
    ipAddress: `192.168.1.${100 + (index % 50)}`,
    organizationId: 'org-1',
  })
);

// Mock Templates
export const mockTemplates: Template[] = [
  {
    id: 'template-1',
    name: 'Standard Prescription',
    type: 'Prescription',
    content:
      '<h1>Prescription</h1><p>Patient Name: {{patientName}}</p><p>Medication: {{medication}}</p>',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'template-2',
    name: 'Detailed Discharge Summary',
    type: 'Discharge Summary',
    content:
      '<h1>Discharge Summary</h1><p>Patient Name: {{patientName}}</p><p>Diagnosis: {{diagnosis}}</p>',
    status: 'active',
    organizationId: 'org-1',
    createdAt: new Date('2024-02-10'),
  },
  {
    id: 'template-3',
    name: 'Basic Lab Report',
    type: 'Lab Report',
    content:
      '<h1>Lab Report</h1><p>Patient Name: {{patientName}}</p><p>Test: {{testName}}</p><p>Result: {{result}}</p>',
    status: 'inactive',
    organizationId: 'org-1',
    createdAt: new Date('2024-03-05'),
  },
];

// Helper function to generate dummy data for API simulation
export const generateMockApiResponse = <T>(data: T, delay = 500) => {
  return new Promise<{ data: T; message: string; success: boolean }>(
    (resolve) => {
      setTimeout(() => {
        resolve({
          data,
          message: 'Success',
          success: true,
        });
      }, delay);
    }
  );
};

export const generateMockApiError = (message: string, delay = 500) => {
  return new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(new Error(message));
    }, delay);
  });
};
